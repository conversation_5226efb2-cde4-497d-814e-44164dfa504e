const express = require('express');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/dashboard/overview
 * @desc    Get dashboard overview data
 * @access  Private
 */
router.get('/overview', authenticateToken, (req, res) => {
  // Mock dashboard data
  const dashboardData = {
    totalRevenue: 15750.00,
    totalExpenses: 3250.00,
    netIncome: 12500.00,
    totalInvoices: 8,
    paidInvoices: 6,
    pendingInvoices: 2,
    overdueInvoices: 0,
    totalClients: 5,
    activeClients: 4,
    recentInvoices: [
      {
        id: 'inv_1',
        invoiceNumber: 'INV-001',
        clientName: 'Acme Corp',
        amount: 2500.00,
        status: 'PAID',
        dueDate: '2024-01-15',
        createdAt: '2024-01-01'
      },
      {
        id: 'inv_2',
        invoiceNumber: 'INV-002',
        clientName: 'Tech Solutions',
        amount: 1800.00,
        status: 'SENT',
        dueDate: '2024-01-20',
        createdAt: '2024-01-05'
      },
      {
        id: 'inv_3',
        invoiceNumber: 'INV-003',
        clientName: 'Design Studio',
        amount: 3200.00,
        status: 'DRAFT',
        dueDate: '2024-01-25',
        createdAt: '2024-01-10'
      }
    ],
    recentExpenses: [
      {
        id: 'exp_1',
        description: 'Office Supplies',
        amount: 150.00,
        category: 'OFFICE_SUPPLIES',
        date: '2024-01-12'
      },
      {
        id: 'exp_2',
        description: 'Software License',
        amount: 99.00,
        category: 'SOFTWARE',
        date: '2024-01-10'
      },
      {
        id: 'exp_3',
        description: 'Business Lunch',
        amount: 75.00,
        category: 'MEALS',
        date: '2024-01-08'
      }
    ],
    monthlyRevenue: [
      { month: 'Jan', revenue: 15750, expenses: 3250 },
      { month: 'Dec', revenue: 12400, expenses: 2800 },
      { month: 'Nov', revenue: 18200, expenses: 4100 },
      { month: 'Oct', revenue: 14600, expenses: 3600 },
      { month: 'Sep', revenue: 16800, expenses: 3900 },
      { month: 'Aug', revenue: 13200, expenses: 2900 }
    ],
    expensesByCategory: [
      { category: 'Software', amount: 450, percentage: 35 },
      { category: 'Office Supplies', amount: 320, percentage: 25 },
      { category: 'Travel', amount: 280, percentage: 22 },
      { category: 'Meals', amount: 150, percentage: 12 },
      { category: 'Other', amount: 80, percentage: 6 }
    ],
    invoiceStatusDistribution: [
      { status: 'Paid', count: 6, percentage: 75 },
      { status: 'Sent', count: 2, percentage: 25 },
      { status: 'Draft', count: 0, percentage: 0 },
      { status: 'Overdue', count: 0, percentage: 0 }
    ]
  };

  res.json({
    message: 'Dashboard data retrieved successfully (mock mode)',
    data: dashboardData
  });
});

/**
 * @route   GET /api/dashboard/stats
 * @desc    Get dashboard statistics
 * @access  Private
 */
router.get('/stats', authenticateToken, (req, res) => {
  const stats = {
    thisMonth: {
      revenue: 15750.00,
      expenses: 3250.00,
      profit: 12500.00,
      invoicesSent: 3,
      invoicesPaid: 2
    },
    lastMonth: {
      revenue: 12400.00,
      expenses: 2800.00,
      profit: 9600.00,
      invoicesSent: 4,
      invoicesPaid: 4
    },
    growth: {
      revenue: 27.0, // percentage
      expenses: 16.1,
      profit: 30.2,
      invoices: -25.0
    },
    yearToDate: {
      revenue: 156750.00,
      expenses: 32500.00,
      profit: 124250.00,
      invoicesSent: 28,
      invoicesPaid: 24
    }
  };

  res.json({
    message: 'Dashboard statistics retrieved successfully (mock mode)',
    stats
  });
});

module.exports = router;
