import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/@heroicons/react/24/solid/esm/AcademicCapIcon.js
var React = __toESM(require_react(), 1);
function AcademicCapIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React.createElement("title", {
    id: titleId
  }, title) : null, React.createElement("path", {
    d: "M11.7 2.805a.75.75 0 0 1 .6 0A60.65 60.65 0 0 1 22.83 8.72a.75.75 0 0 1-.231 1.337 49.948 49.948 0 0 0-9.902 3.912l-.003.002c-.114.06-.227.119-.34.18a.75.75 0 0 1-.707 0A50.88 50.88 0 0 0 7.5 12.173v-.224c0-.131.067-.248.172-.311a54.615 54.615 0 0 1 4.653-2.52.75.75 0 0 0-.65-1.352 56.123 56.123 0 0 0-4.78 2.589 1.858 1.858 0 0 0-.859 1.228 49.803 49.803 0 0 0-4.634-1.527.75.75 0 0 1-.231-1.337A60.653 60.653 0 0 1 11.7 2.805Z"
  }), React.createElement("path", {
    d: "M13.06 15.473a48.45 48.45 0 0 1 7.666-3.282c.134 1.414.22 2.843.255 4.284a.75.75 0 0 1-.46.711 47.87 47.87 0 0 0-8.105 4.342.75.75 0 0 1-.832 0 47.87 47.87 0 0 0-8.104-4.342.75.75 0 0 1-.461-.71c.035-1.442.121-2.87.255-4.286.921.304 1.83.634 2.726.99v1.27a1.5 1.5 0 0 0-.14 2.508c-.09.38-.222.753-.397 1.11.452.213.901.434 1.346.66a6.727 6.727 0 0 0 .551-1.607 1.5 1.5 0 0 0 .14-2.67v-.645a48.549 48.549 0 0 1 3.44 1.667 2.25 2.25 0 0 0 2.12 0Z"
  }), React.createElement("path", {
    d: "M4.462 19.462c.42-.419.753-.89 1-1.395.453.214.902.435 1.347.662a6.742 6.742 0 0 1-1.286 1.794.75.75 0 0 1-1.06-1.06Z"
  }));
}
var ForwardRef = React.forwardRef(AcademicCapIcon);
var AcademicCapIcon_default = ForwardRef;

// node_modules/@heroicons/react/24/solid/esm/AdjustmentsHorizontalIcon.js
var React2 = __toESM(require_react(), 1);
function AdjustmentsHorizontalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React2.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React2.createElement("title", {
    id: titleId
  }, title) : null, React2.createElement("path", {
    d: "M18.75 12.75h1.5a.75.75 0 0 0 0-1.5h-1.5a.75.75 0 0 0 0 1.5ZM12 6a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 12 6ZM12 18a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 12 18ZM3.75 6.75h1.5a.75.75 0 1 0 0-1.5h-1.5a.75.75 0 0 0 0 1.5ZM5.25 18.75h-1.5a.75.75 0 0 1 0-1.5h1.5a.75.75 0 0 1 0 1.5ZM3 12a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 3 12ZM9 3.75a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5ZM12.75 12a2.25 2.25 0 1 1 4.5 0 2.25 2.25 0 0 1-4.5 0ZM9 15.75a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5Z"
  }));
}
var ForwardRef2 = React2.forwardRef(AdjustmentsHorizontalIcon);
var AdjustmentsHorizontalIcon_default = ForwardRef2;

// node_modules/@heroicons/react/24/solid/esm/AdjustmentsVerticalIcon.js
var React3 = __toESM(require_react(), 1);
function AdjustmentsVerticalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React3.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React3.createElement("title", {
    id: titleId
  }, title) : null, React3.createElement("path", {
    d: "M6 12a.75.75 0 0 1-.75-.75v-7.5a.75.75 0 1 1 1.5 0v7.5A.75.75 0 0 1 6 12ZM18 12a.75.75 0 0 1-.75-.75v-7.5a.75.75 0 0 1 1.5 0v7.5A.75.75 0 0 1 18 12ZM6.75 20.25v-1.5a.75.75 0 0 0-1.5 0v1.5a.75.75 0 0 0 1.5 0ZM18.75 18.75v1.5a.75.75 0 0 1-1.5 0v-1.5a.75.75 0 0 1 1.5 0ZM12.75 5.25v-1.5a.75.75 0 0 0-1.5 0v1.5a.75.75 0 0 0 1.5 0ZM12 21a.75.75 0 0 1-.75-.75v-7.5a.75.75 0 0 1 1.5 0v7.5A.75.75 0 0 1 12 21ZM3.75 15a2.25 2.25 0 1 0 4.5 0 2.25 2.25 0 0 0-4.5 0ZM12 11.25a2.25 2.25 0 1 1 0-4.5 2.25 2.25 0 0 1 0 4.5ZM15.75 15a2.25 2.25 0 1 0 4.5 0 2.25 2.25 0 0 0-4.5 0Z"
  }));
}
var ForwardRef3 = React3.forwardRef(AdjustmentsVerticalIcon);
var AdjustmentsVerticalIcon_default = ForwardRef3;

// node_modules/@heroicons/react/24/solid/esm/ArchiveBoxArrowDownIcon.js
var React4 = __toESM(require_react(), 1);
function ArchiveBoxArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React4.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React4.createElement("title", {
    id: titleId
  }, title) : null, React4.createElement("path", {
    d: "M3.375 3C2.339 3 1.5 3.84 1.5 4.875v.75c0 1.036.84 1.875 1.875 1.875h17.25c1.035 0 1.875-.84 1.875-1.875v-.75C22.5 3.839 21.66 3 20.625 3H3.375Z"
  }), React4.createElement("path", {
    fillRule: "evenodd",
    d: "m3.087 9 .54 9.176A3 3 0 0 0 6.62 21h10.757a3 3 0 0 0 2.995-2.824L20.913 9H3.087ZM12 10.5a.75.75 0 0 1 .75.75v4.94l1.72-1.72a.75.75 0 1 1 1.06 1.06l-3 3a.75.75 0 0 1-1.06 0l-3-3a.75.75 0 1 1 1.06-1.06l1.72 1.72v-4.94a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef4 = React4.forwardRef(ArchiveBoxArrowDownIcon);
var ArchiveBoxArrowDownIcon_default = ForwardRef4;

// node_modules/@heroicons/react/24/solid/esm/ArchiveBoxXMarkIcon.js
var React5 = __toESM(require_react(), 1);
function ArchiveBoxXMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React5.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React5.createElement("title", {
    id: titleId
  }, title) : null, React5.createElement("path", {
    d: "M3.375 3C2.339 3 1.5 3.84 1.5 4.875v.75c0 1.036.84 1.875 1.875 1.875h17.25c1.035 0 1.875-.84 1.875-1.875v-.75C22.5 3.839 21.66 3 20.625 3H3.375Z"
  }), React5.createElement("path", {
    fillRule: "evenodd",
    d: "m3.087 9 .54 9.176A3 3 0 0 0 6.62 21h10.757a3 3 0 0 0 2.995-2.824L20.913 9H3.087Zm6.133 2.845a.75.75 0 0 1 1.06 0l1.72 1.72 1.72-1.72a.75.75 0 1 1 1.06 1.06l-1.72 1.72 1.72 1.72a.75.75 0 1 1-1.06 1.06L12 15.685l-1.72 1.72a.75.75 0 1 1-1.06-1.06l1.72-1.72-1.72-1.72a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef5 = React5.forwardRef(ArchiveBoxXMarkIcon);
var ArchiveBoxXMarkIcon_default = ForwardRef5;

// node_modules/@heroicons/react/24/solid/esm/ArchiveBoxIcon.js
var React6 = __toESM(require_react(), 1);
function ArchiveBoxIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React6.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React6.createElement("title", {
    id: titleId
  }, title) : null, React6.createElement("path", {
    d: "M3.375 3C2.339 3 1.5 3.84 1.5 4.875v.75c0 1.036.84 1.875 1.875 1.875h17.25c1.035 0 1.875-.84 1.875-1.875v-.75C22.5 3.839 21.66 3 20.625 3H3.375Z"
  }), React6.createElement("path", {
    fillRule: "evenodd",
    d: "m3.087 9 .54 9.176A3 3 0 0 0 6.62 21h10.757a3 3 0 0 0 2.995-2.824L20.913 9H3.087Zm6.163 3.75A.75.75 0 0 1 10 12h4a.75.75 0 0 1 0 1.5h-4a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef6 = React6.forwardRef(ArchiveBoxIcon);
var ArchiveBoxIcon_default = ForwardRef6;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownCircleIcon.js
var React7 = __toESM(require_react(), 1);
function ArrowDownCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React7.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React7.createElement("title", {
    id: titleId
  }, title) : null, React7.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-.53 14.03a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V8.25a.75.75 0 0 0-1.5 0v5.69l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef7 = React7.forwardRef(ArrowDownCircleIcon);
var ArrowDownCircleIcon_default = ForwardRef7;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownLeftIcon.js
var React8 = __toESM(require_react(), 1);
function ArrowDownLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React8.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React8.createElement("title", {
    id: titleId
  }, title) : null, React8.createElement("path", {
    fillRule: "evenodd",
    d: "M20.03 3.97a.75.75 0 0 1 0 1.06L6.31 18.75h9.44a.75.75 0 0 1 0 1.5H4.5a.75.75 0 0 1-.75-.75V8.25a.75.75 0 0 1 1.5 0v9.44L18.97 3.97a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef8 = React8.forwardRef(ArrowDownLeftIcon);
var ArrowDownLeftIcon_default = ForwardRef8;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownOnSquareStackIcon.js
var React9 = __toESM(require_react(), 1);
function ArrowDownOnSquareStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React9.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React9.createElement("title", {
    id: titleId
  }, title) : null, React9.createElement("path", {
    fillRule: "evenodd",
    d: "M9.75 6.75h-3a3 3 0 0 0-3 3v7.5a3 3 0 0 0 3 3h7.5a3 3 0 0 0 3-3v-7.5a3 3 0 0 0-3-3h-3V1.5a.75.75 0 0 0-1.5 0v5.25Zm0 0h1.5v5.69l1.72-1.72a.75.75 0 1 1 1.06 1.06l-3 3a.75.75 0 0 1-1.06 0l-3-3a.75.75 0 1 1 1.06-1.06l1.72 1.72V6.75Z",
    clipRule: "evenodd"
  }), React9.createElement("path", {
    d: "M7.151 21.75a2.999 2.999 0 0 0 2.599 1.5h7.5a3 3 0 0 0 3-3v-7.5c0-1.11-.603-2.08-1.5-2.599v7.099a4.5 4.5 0 0 1-4.5 4.5H7.151Z"
  }));
}
var ForwardRef9 = React9.forwardRef(ArrowDownOnSquareStackIcon);
var ArrowDownOnSquareStackIcon_default = ForwardRef9;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownOnSquareIcon.js
var React10 = __toESM(require_react(), 1);
function ArrowDownOnSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React10.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React10.createElement("title", {
    id: titleId
  }, title) : null, React10.createElement("path", {
    d: "M12 1.5a.75.75 0 0 1 .75.75V7.5h-1.5V2.25A.75.75 0 0 1 12 1.5ZM11.25 7.5v5.69l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V7.5h3.75a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3v-9a3 3 0 0 1 3-3h3.75Z"
  }));
}
var ForwardRef10 = React10.forwardRef(ArrowDownOnSquareIcon);
var ArrowDownOnSquareIcon_default = ForwardRef10;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownRightIcon.js
var React11 = __toESM(require_react(), 1);
function ArrowDownRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React11.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React11.createElement("title", {
    id: titleId
  }, title) : null, React11.createElement("path", {
    fillRule: "evenodd",
    d: "M3.97 3.97a.75.75 0 0 1 1.06 0l13.72 13.72V8.25a.75.75 0 0 1 1.5 0V19.5a.75.75 0 0 1-.75.75H8.25a.75.75 0 0 1 0-1.5h9.44L3.97 5.03a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef11 = React11.forwardRef(ArrowDownRightIcon);
var ArrowDownRightIcon_default = ForwardRef11;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownTrayIcon.js
var React12 = __toESM(require_react(), 1);
function ArrowDownTrayIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React12.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React12.createElement("title", {
    id: titleId
  }, title) : null, React12.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef12 = React12.forwardRef(ArrowDownTrayIcon);
var ArrowDownTrayIcon_default = ForwardRef12;

// node_modules/@heroicons/react/24/solid/esm/ArrowDownIcon.js
var React13 = __toESM(require_react(), 1);
function ArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React13.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React13.createElement("title", {
    id: titleId
  }, title) : null, React13.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25a.75.75 0 0 1 .75.75v16.19l6.22-6.22a.75.75 0 1 1 1.06 1.06l-7.5 7.5a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 1 1 1.06-1.06l6.22 6.22V3a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef13 = React13.forwardRef(ArrowDownIcon);
var ArrowDownIcon_default = ForwardRef13;

// node_modules/@heroicons/react/24/solid/esm/ArrowLeftCircleIcon.js
var React14 = __toESM(require_react(), 1);
function ArrowLeftCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React14.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React14.createElement("title", {
    id: titleId
  }, title) : null, React14.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-4.28 9.22a.75.75 0 0 0 0 1.06l3 3a.75.75 0 1 0 1.06-1.06l-1.72-1.72h5.69a.75.75 0 0 0 0-1.5h-5.69l1.72-1.72a.75.75 0 0 0-1.06-1.06l-3 3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef14 = React14.forwardRef(ArrowLeftCircleIcon);
var ArrowLeftCircleIcon_default = ForwardRef14;

// node_modules/@heroicons/react/24/solid/esm/ArrowLeftEndOnRectangleIcon.js
var React15 = __toESM(require_react(), 1);
function ArrowLeftEndOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React15.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React15.createElement("title", {
    id: titleId
  }, title) : null, React15.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm5.03 4.72a.75.75 0 0 1 0 1.06l-1.72 1.72h10.94a.75.75 0 0 1 0 1.5H10.81l1.72 1.72a.75.75 0 1 1-1.06 1.06l-3-3a.75.75 0 0 1 0-1.06l3-3a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef15 = React15.forwardRef(ArrowLeftEndOnRectangleIcon);
var ArrowLeftEndOnRectangleIcon_default = ForwardRef15;

// node_modules/@heroicons/react/24/solid/esm/ArrowLeftOnRectangleIcon.js
var React16 = __toESM(require_react(), 1);
function ArrowLeftOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React16.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React16.createElement("title", {
    id: titleId
  }, title) : null, React16.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm5.03 4.72a.75.75 0 0 1 0 1.06l-1.72 1.72h10.94a.75.75 0 0 1 0 1.5H10.81l1.72 1.72a.75.75 0 1 1-1.06 1.06l-3-3a.75.75 0 0 1 0-1.06l3-3a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef16 = React16.forwardRef(ArrowLeftOnRectangleIcon);
var ArrowLeftOnRectangleIcon_default = ForwardRef16;

// node_modules/@heroicons/react/24/solid/esm/ArrowLeftStartOnRectangleIcon.js
var React17 = __toESM(require_react(), 1);
function ArrowLeftStartOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React17.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React17.createElement("title", {
    id: titleId
  }, title) : null, React17.createElement("path", {
    fillRule: "evenodd",
    d: "M16.5 3.75a1.5 1.5 0 0 1 1.5 1.5v13.5a1.5 1.5 0 0 1-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5V15a.75.75 0 0 0-1.5 0v3.75a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3V5.25a3 3 0 0 0-3-3h-6a3 3 0 0 0-3 3V9A.75.75 0 1 0 9 9V5.25a1.5 1.5 0 0 1 1.5-1.5h6ZM5.78 8.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 0 0 0 1.06l3 3a.75.75 0 0 0 1.06-1.06l-1.72-1.72H15a.75.75 0 0 0 0-1.5H4.06l1.72-1.72a.75.75 0 0 0 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef17 = React17.forwardRef(ArrowLeftStartOnRectangleIcon);
var ArrowLeftStartOnRectangleIcon_default = ForwardRef17;

// node_modules/@heroicons/react/24/solid/esm/ArrowLeftIcon.js
var React18 = __toESM(require_react(), 1);
function ArrowLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React18.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React18.createElement("title", {
    id: titleId
  }, title) : null, React18.createElement("path", {
    fillRule: "evenodd",
    d: "M11.03 3.97a.75.75 0 0 1 0 1.06l-6.22 6.22H21a.75.75 0 0 1 0 1.5H4.81l6.22 6.22a.75.75 0 1 1-1.06 1.06l-7.5-7.5a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef18 = React18.forwardRef(ArrowLeftIcon);
var ArrowLeftIcon_default = ForwardRef18;

// node_modules/@heroicons/react/24/solid/esm/ArrowLongDownIcon.js
var React19 = __toESM(require_react(), 1);
function ArrowLongDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React19.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React19.createElement("title", {
    id: titleId
  }, title) : null, React19.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25a.75.75 0 0 1 .75.75v16.19l2.47-2.47a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0l-3.75-3.75a.75.75 0 1 1 1.06-1.06l2.47 2.47V3a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef19 = React19.forwardRef(ArrowLongDownIcon);
var ArrowLongDownIcon_default = ForwardRef19;

// node_modules/@heroicons/react/24/solid/esm/ArrowLongLeftIcon.js
var React20 = __toESM(require_react(), 1);
function ArrowLongLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React20.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React20.createElement("title", {
    id: titleId
  }, title) : null, React20.createElement("path", {
    fillRule: "evenodd",
    d: "M7.28 7.72a.75.75 0 0 1 0 1.06l-2.47 2.47H21a.75.75 0 0 1 0 1.5H4.81l2.47 2.47a.75.75 0 1 1-1.06 1.06l-3.75-3.75a.75.75 0 0 1 0-1.06l3.75-3.75a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef20 = React20.forwardRef(ArrowLongLeftIcon);
var ArrowLongLeftIcon_default = ForwardRef20;

// node_modules/@heroicons/react/24/solid/esm/ArrowLongRightIcon.js
var React21 = __toESM(require_react(), 1);
function ArrowLongRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React21.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React21.createElement("title", {
    id: titleId
  }, title) : null, React21.createElement("path", {
    fillRule: "evenodd",
    d: "M16.72 7.72a.75.75 0 0 1 1.06 0l3.75 3.75a.75.75 0 0 1 0 1.06l-3.75 3.75a.75.75 0 1 1-1.06-1.06l2.47-2.47H3a.75.75 0 0 1 0-1.5h16.19l-2.47-2.47a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef21 = React21.forwardRef(ArrowLongRightIcon);
var ArrowLongRightIcon_default = ForwardRef21;

// node_modules/@heroicons/react/24/solid/esm/ArrowLongUpIcon.js
var React22 = __toESM(require_react(), 1);
function ArrowLongUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React22.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React22.createElement("title", {
    id: titleId
  }, title) : null, React22.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 2.47a.75.75 0 0 1 1.06 0l3.75 3.75a.75.75 0 0 1-1.06 1.06l-2.47-2.47V21a.75.75 0 0 1-1.5 0V4.81L8.78 7.28a.75.75 0 0 1-1.06-1.06l3.75-3.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef22 = React22.forwardRef(ArrowLongUpIcon);
var ArrowLongUpIcon_default = ForwardRef22;

// node_modules/@heroicons/react/24/solid/esm/ArrowPathRoundedSquareIcon.js
var React23 = __toESM(require_react(), 1);
function ArrowPathRoundedSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React23.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React23.createElement("title", {
    id: titleId
  }, title) : null, React23.createElement("path", {
    fillRule: "evenodd",
    d: "M12 5.25c1.213 0 2.415.046 3.605.135a3.256 3.256 0 0 1 3.01 3.01c.044.583.077 1.17.1 1.759L17.03 8.47a.75.75 0 1 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 0 0-1.06-1.06l-1.752 1.751c-.023-.65-.06-1.296-.108-1.939a4.756 4.756 0 0 0-4.392-4.392 49.422 49.422 0 0 0-7.436 0A4.756 4.756 0 0 0 3.89 8.282c-.017.224-.033.447-.046.672a.75.75 0 1 0 1.497.092c.013-.217.028-.434.044-.651a3.256 3.256 0 0 1 3.01-3.01c1.19-.09 2.392-.135 3.605-.135Zm-6.97 6.22a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.752-1.751c.023.65.06 1.296.108 1.939a4.756 4.756 0 0 0 4.392 4.392 49.413 49.413 0 0 0 7.436 0 4.756 4.756 0 0 0 4.392-4.392c.017-.223.032-.447.046-.672a.75.75 0 0 0-1.497-.092c-.013.217-.028.434-.044.651a3.256 3.256 0 0 1-3.01 3.01 47.953 47.953 0 0 1-7.21 0 3.256 3.256 0 0 1-3.01-3.01 47.759 47.759 0 0 1-.1-1.759L6.97 15.53a.75.75 0 0 0 1.06-1.06l-3-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef23 = React23.forwardRef(ArrowPathRoundedSquareIcon);
var ArrowPathRoundedSquareIcon_default = ForwardRef23;

// node_modules/@heroicons/react/24/solid/esm/ArrowPathIcon.js
var React24 = __toESM(require_react(), 1);
function ArrowPathIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React24.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React24.createElement("title", {
    id: titleId
  }, title) : null, React24.createElement("path", {
    fillRule: "evenodd",
    d: "M4.755 10.059a7.5 7.5 0 0 1 12.548-3.364l1.903 1.903h-3.183a.75.75 0 1 0 0 1.5h4.992a.75.75 0 0 0 .75-.75V4.356a.75.75 0 0 0-1.5 0v3.18l-1.9-1.9A9 9 0 0 0 3.306 9.67a.75.75 0 1 0 1.45.388Zm15.408 3.352a.75.75 0 0 0-.919.53 7.5 7.5 0 0 1-12.548 3.364l-1.902-1.903h3.183a.75.75 0 0 0 0-1.5H2.984a.75.75 0 0 0-.75.75v4.992a.75.75 0 0 0 1.5 0v-3.18l1.9 1.9a9 9 0 0 0 15.059-*********** 0 0 0-.53-.918Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef24 = React24.forwardRef(ArrowPathIcon);
var ArrowPathIcon_default = ForwardRef24;

// node_modules/@heroicons/react/24/solid/esm/ArrowRightCircleIcon.js
var React25 = __toESM(require_react(), 1);
function ArrowRightCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React25.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React25.createElement("title", {
    id: titleId
  }, title) : null, React25.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm4.28 10.28a.75.75 0 0 0 0-1.06l-3-3a.75.75 0 1 0-1.06 1.06l1.72 1.72H8.25a.75.75 0 0 0 0 1.5h5.69l-1.72 1.72a.75.75 0 1 0 1.06 1.06l3-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef25 = React25.forwardRef(ArrowRightCircleIcon);
var ArrowRightCircleIcon_default = ForwardRef25;

// node_modules/@heroicons/react/24/solid/esm/ArrowRightEndOnRectangleIcon.js
var React26 = __toESM(require_react(), 1);
function ArrowRightEndOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React26.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React26.createElement("title", {
    id: titleId
  }, title) : null, React26.createElement("path", {
    fillRule: "evenodd",
    d: "M16.5 3.75a1.5 1.5 0 0 1 1.5 1.5v13.5a1.5 1.5 0 0 1-1.5 1.5h-6a1.5 1.5 0 0 1-1.5-1.5V15a.75.75 0 0 0-1.5 0v3.75a3 3 0 0 0 3 3h6a3 3 0 0 0 3-3V5.25a3 3 0 0 0-3-3h-6a3 3 0 0 0-3 3V9A.75.75 0 1 0 9 9V5.25a1.5 1.5 0 0 1 1.5-1.5h6Zm-5.03 4.72a.75.75 0 0 0 0 1.06l1.72 1.72H2.25a.75.75 0 0 0 0 1.5h10.94l-1.72 1.72a.75.75 0 1 0 1.06 1.06l3-3a.75.75 0 0 0 0-1.06l-3-3a.75.75 0 0 0-1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef26 = React26.forwardRef(ArrowRightEndOnRectangleIcon);
var ArrowRightEndOnRectangleIcon_default = ForwardRef26;

// node_modules/@heroicons/react/24/solid/esm/ArrowRightOnRectangleIcon.js
var React27 = __toESM(require_react(), 1);
function ArrowRightOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React27.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React27.createElement("title", {
    id: titleId
  }, title) : null, React27.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef27 = React27.forwardRef(ArrowRightOnRectangleIcon);
var ArrowRightOnRectangleIcon_default = ForwardRef27;

// node_modules/@heroicons/react/24/solid/esm/ArrowRightStartOnRectangleIcon.js
var React28 = __toESM(require_react(), 1);
function ArrowRightStartOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React28.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React28.createElement("title", {
    id: titleId
  }, title) : null, React28.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 3.75A1.5 1.5 0 0 0 6 5.25v13.5a1.5 1.5 0 0 0 1.5 1.5h6a1.5 1.5 0 0 0 1.5-1.5V15a.75.75 0 0 1 1.5 0v3.75a3 3 0 0 1-3 3h-6a3 3 0 0 1-3-3V5.25a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3V9A.75.75 0 0 1 15 9V5.25a1.5 1.5 0 0 0-1.5-1.5h-6Zm10.72 4.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1 0 1.06l-3 3a.75.75 0 1 1-1.06-1.06l1.72-1.72H9a.75.75 0 0 1 0-1.5h10.94l-1.72-1.72a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef28 = React28.forwardRef(ArrowRightStartOnRectangleIcon);
var ArrowRightStartOnRectangleIcon_default = ForwardRef28;

// node_modules/@heroicons/react/24/solid/esm/ArrowRightIcon.js
var React29 = __toESM(require_react(), 1);
function ArrowRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React29.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React29.createElement("title", {
    id: titleId
  }, title) : null, React29.createElement("path", {
    fillRule: "evenodd",
    d: "M12.97 3.97a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06l6.22-6.22H3a.75.75 0 0 1 0-1.5h16.19l-6.22-6.22a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef29 = React29.forwardRef(ArrowRightIcon);
var ArrowRightIcon_default = ForwardRef29;

// node_modules/@heroicons/react/24/solid/esm/ArrowSmallDownIcon.js
var React30 = __toESM(require_react(), 1);
function ArrowSmallDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React30.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React30.createElement("title", {
    id: titleId
  }, title) : null, React30.createElement("path", {
    fillRule: "evenodd",
    d: "M12 3.75a.75.75 0 0 1 .75.75v13.19l5.47-5.47a.75.75 0 1 1 1.06 1.06l-6.75 6.75a.75.75 0 0 1-1.06 0l-6.75-6.75a.75.75 0 1 1 1.06-1.06l5.47 5.47V4.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef30 = React30.forwardRef(ArrowSmallDownIcon);
var ArrowSmallDownIcon_default = ForwardRef30;

// node_modules/@heroicons/react/24/solid/esm/ArrowSmallLeftIcon.js
var React31 = __toESM(require_react(), 1);
function ArrowSmallLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React31.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React31.createElement("title", {
    id: titleId
  }, title) : null, React31.createElement("path", {
    fillRule: "evenodd",
    d: "M20.25 12a.75.75 0 0 1-.75.75H6.31l5.47 5.47a.75.75 0 1 1-1.06 1.06l-6.75-6.75a.75.75 0 0 1 0-1.06l6.75-6.75a.75.75 0 1 1 1.06 1.06l-5.47 5.47H19.5a.75.75 0 0 1 .75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef31 = React31.forwardRef(ArrowSmallLeftIcon);
var ArrowSmallLeftIcon_default = ForwardRef31;

// node_modules/@heroicons/react/24/solid/esm/ArrowSmallRightIcon.js
var React32 = __toESM(require_react(), 1);
function ArrowSmallRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React32.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React32.createElement("title", {
    id: titleId
  }, title) : null, React32.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 12a.75.75 0 0 1 .75-.75h13.19l-5.47-5.47a.75.75 0 0 1 1.06-1.06l6.75 6.75a.75.75 0 0 1 0 1.06l-6.75 6.75a.75.75 0 1 1-1.06-1.06l5.47-5.47H4.5a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef32 = React32.forwardRef(ArrowSmallRightIcon);
var ArrowSmallRightIcon_default = ForwardRef32;

// node_modules/@heroicons/react/24/solid/esm/ArrowSmallUpIcon.js
var React33 = __toESM(require_react(), 1);
function ArrowSmallUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React33.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React33.createElement("title", {
    id: titleId
  }, title) : null, React33.createElement("path", {
    fillRule: "evenodd",
    d: "M12 20.25a.75.75 0 0 1-.75-.75V6.31l-5.47 5.47a.75.75 0 0 1-1.06-1.06l6.75-6.75a.75.75 0 0 1 1.06 0l6.75 6.75a.75.75 0 1 1-1.06 1.06l-5.47-5.47V19.5a.75.75 0 0 1-.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef33 = React33.forwardRef(ArrowSmallUpIcon);
var ArrowSmallUpIcon_default = ForwardRef33;

// node_modules/@heroicons/react/24/solid/esm/ArrowTopRightOnSquareIcon.js
var React34 = __toESM(require_react(), 1);
function ArrowTopRightOnSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React34.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React34.createElement("title", {
    id: titleId
  }, title) : null, React34.createElement("path", {
    fillRule: "evenodd",
    d: "M15.75 2.25H21a.75.75 0 0 1 .75.75v5.25a.75.75 0 0 1-1.5 0V4.81L8.03 17.03a.75.75 0 0 1-1.06-1.06L19.19 3.75h-3.44a.75.75 0 0 1 0-1.5Zm-10.5 4.5a1.5 1.5 0 0 0-1.5 1.5v10.5a1.5 1.5 0 0 0 1.5 1.5h10.5a1.5 1.5 0 0 0 1.5-1.5V10.5a.75.75 0 0 1 1.5 0v8.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V8.25a3 3 0 0 1 3-3h8.25a.75.75 0 0 1 0 1.5H5.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef34 = React34.forwardRef(ArrowTopRightOnSquareIcon);
var ArrowTopRightOnSquareIcon_default = ForwardRef34;

// node_modules/@heroicons/react/24/solid/esm/ArrowTrendingDownIcon.js
var React35 = __toESM(require_react(), 1);
function ArrowTrendingDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React35.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React35.createElement("title", {
    id: titleId
  }, title) : null, React35.createElement("path", {
    fillRule: "evenodd",
    d: "M1.72 5.47a.75.75 0 0 1 1.06 0L9 11.69l3.756-3.756a.75.75 0 0 1 .985-.066 12.698 12.698 0 0 1 4.575 6.832l.308 1.149 2.277-3.943a.75.75 0 1 1 1.299.75l-3.182 5.51a.75.75 0 0 1-1.025.275l-5.511-3.181a.75.75 0 0 1 .75-1.3l3.943 2.277-.308-1.149a11.194 11.194 0 0 0-3.528-5.617l-3.809 3.81a.75.75 0 0 1-1.06 0L1.72 6.53a.75.75 0 0 1 0-1.061Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef35 = React35.forwardRef(ArrowTrendingDownIcon);
var ArrowTrendingDownIcon_default = ForwardRef35;

// node_modules/@heroicons/react/24/solid/esm/ArrowTrendingUpIcon.js
var React36 = __toESM(require_react(), 1);
function ArrowTrendingUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React36.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React36.createElement("title", {
    id: titleId
  }, title) : null, React36.createElement("path", {
    fillRule: "evenodd",
    d: "M15.22 6.268a.75.75 0 0 1 .968-.431l5.942 2.28a.75.75 0 0 1 .431.97l-2.28 5.94a.75.75 0 1 1-1.4-.537l1.63-4.251-1.086.484a11.2 11.2 0 0 0-5.45 *********** 0 0 1-1.199.19L9 12.312l-6.22 6.22a.75.75 0 0 1-1.06-1.061l6.75-6.75a.75.75 0 0 1 1.06 0l3.606 3.606a12.695 12.695 0 0 1 5.68-4.974l1.086-.483-4.251-1.632a.75.75 0 0 1-.432-.97Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef36 = React36.forwardRef(ArrowTrendingUpIcon);
var ArrowTrendingUpIcon_default = ForwardRef36;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnDownLeftIcon.js
var React37 = __toESM(require_react(), 1);
function ArrowTurnDownLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React37.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React37.createElement("title", {
    id: titleId
  }, title) : null, React37.createElement("path", {
    fillRule: "evenodd",
    d: "M20.239 3.749a.75.75 0 0 0-.75.75V15H5.549l2.47-2.47a.75.75 0 0 0-1.06-1.06l-3.75 3.75a.75.75 0 0 0 0 1.06l3.75 3.75a.75.75 0 1 0 1.06-1.06L5.55 16.5h14.69a.75.75 0 0 0 .75-.75V4.5a.75.75 0 0 0-.75-.751Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef37 = React37.forwardRef(ArrowTurnDownLeftIcon);
var ArrowTurnDownLeftIcon_default = ForwardRef37;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnDownRightIcon.js
var React38 = __toESM(require_react(), 1);
function ArrowTurnDownRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React38.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React38.createElement("title", {
    id: titleId
  }, title) : null, React38.createElement("path", {
    fillRule: "evenodd",
    d: "M3.74 3.749a.75.75 0 0 1 .75.75V15h13.938l-2.47-2.47a.75.75 0 0 1 1.061-1.06l3.75 3.75a.75.75 0 0 1 0 1.06l-3.75 3.75a.75.75 0 0 1-1.06-1.06l2.47-2.47H3.738a.75.75 0 0 1-.75-.75V4.5a.75.75 0 0 1 .75-.751Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef38 = React38.forwardRef(ArrowTurnDownRightIcon);
var ArrowTurnDownRightIcon_default = ForwardRef38;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnLeftDownIcon.js
var React39 = __toESM(require_react(), 1);
function ArrowTurnLeftDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React39.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React39.createElement("title", {
    id: titleId
  }, title) : null, React39.createElement("path", {
    fillRule: "evenodd",
    d: "M20.24 3.75a.75.75 0 0 1-.75.75H8.989v13.939l2.47-2.47a.75.75 0 1 1 1.06 1.061l-3.75 3.75a.75.75 0 0 1-1.06 0l-3.751-3.75a.75.75 0 1 1 1.06-1.06l2.47 2.469V3.75a.75.75 0 0 1 .75-.75H19.49a.75.75 0 0 1 .75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef39 = React39.forwardRef(ArrowTurnLeftDownIcon);
var ArrowTurnLeftDownIcon_default = ForwardRef39;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnLeftUpIcon.js
var React40 = __toESM(require_react(), 1);
function ArrowTurnLeftUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React40.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React40.createElement("title", {
    id: titleId
  }, title) : null, React40.createElement("path", {
    fillRule: "evenodd",
    d: "M20.24 20.249a.75.75 0 0 0-.75-.75H8.989V5.56l2.47 2.47a.75.75 0 0 0 1.06-1.061l-3.75-3.75a.75.75 0 0 0-1.06 0l-3.75 3.75a.75.75 0 1 0 1.06 1.06l2.47-2.469V20.25c0 .414.335.75.75.75h11.25a.75.75 0 0 0 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef40 = React40.forwardRef(ArrowTurnLeftUpIcon);
var ArrowTurnLeftUpIcon_default = ForwardRef40;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnRightDownIcon.js
var React41 = __toESM(require_react(), 1);
function ArrowTurnRightDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React41.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React41.createElement("title", {
    id: titleId
  }, title) : null, React41.createElement("path", {
    fillRule: "evenodd",
    d: "M3.738 3.75c0 .414.336.75.75.75H14.99v13.939l-2.47-2.47a.75.75 0 0 0-1.06 1.061l3.75 3.75a.75.75 0 0 0 1.06 0l3.751-3.75a.75.75 0 0 0-1.06-1.06l-2.47 2.469V3.75a.75.75 0 0 0-.75-.75H4.487a.75.75 0 0 0-.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef41 = React41.forwardRef(ArrowTurnRightDownIcon);
var ArrowTurnRightDownIcon_default = ForwardRef41;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnRightUpIcon.js
var React42 = __toESM(require_react(), 1);
function ArrowTurnRightUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React42.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React42.createElement("title", {
    id: titleId
  }, title) : null, React42.createElement("path", {
    fillRule: "evenodd",
    d: "M3.738 20.249a.75.75 0 0 1 .75-.75H14.99V5.56l-2.47 2.47a.75.75 0 0 1-1.06-1.061l3.75-3.75a.75.75 0 0 1 1.06 0l3.751 3.75a.75.75 0 0 1-1.06 1.06L16.49 5.56V20.25a.75.75 0 0 1-.75.75H4.487a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef42 = React42.forwardRef(ArrowTurnRightUpIcon);
var ArrowTurnRightUpIcon_default = ForwardRef42;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnUpLeftIcon.js
var React43 = __toESM(require_react(), 1);
function ArrowTurnUpLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React43.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React43.createElement("title", {
    id: titleId
  }, title) : null, React43.createElement("path", {
    fillRule: "evenodd",
    d: "M20.239 20.25a.75.75 0 0 1-.75-.75V8.999H5.549l2.47 2.47a.75.75 0 0 1-1.06 1.06l-3.75-3.75a.75.75 0 0 1 0-1.06l3.75-3.75a.75.75 0 1 1 1.06 1.06l-2.47 2.47h14.69a.75.75 0 0 1 .75.75V19.5a.75.75 0 0 1-.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef43 = React43.forwardRef(ArrowTurnUpLeftIcon);
var ArrowTurnUpLeftIcon_default = ForwardRef43;

// node_modules/@heroicons/react/24/solid/esm/ArrowTurnUpRightIcon.js
var React44 = __toESM(require_react(), 1);
function ArrowTurnUpRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React44.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React44.createElement("title", {
    id: titleId
  }, title) : null, React44.createElement("path", {
    fillRule: "evenodd",
    d: "M3.74 20.25a.75.75 0 0 0 .75-.75V8.999h13.938l-2.47 2.47a.75.75 0 0 0 1.061 1.06l3.75-3.75a.75.75 0 0 0 0-1.06l-3.75-3.75a.75.75 0 0 0-1.06 1.06l2.47 2.47H3.738a.75.75 0 0 0-.75.75V19.5c0 .414.336.75.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef44 = React44.forwardRef(ArrowTurnUpRightIcon);
var ArrowTurnUpRightIcon_default = ForwardRef44;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpCircleIcon.js
var React45 = __toESM(require_react(), 1);
function ArrowUpCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React45.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React45.createElement("title", {
    id: titleId
  }, title) : null, React45.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm.53 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v5.69a.75.75 0 0 0 1.5 0v-5.69l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef45 = React45.forwardRef(ArrowUpCircleIcon);
var ArrowUpCircleIcon_default = ForwardRef45;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpLeftIcon.js
var React46 = __toESM(require_react(), 1);
function ArrowUpLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React46.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React46.createElement("title", {
    id: titleId
  }, title) : null, React46.createElement("path", {
    fillRule: "evenodd",
    d: "M5.25 6.31v9.44a.75.75 0 0 1-1.5 0V4.5a.75.75 0 0 1 .75-.75h11.25a.75.75 0 0 1 0 1.5H6.31l13.72 13.72a.75.75 0 1 1-1.06 1.06L5.25 6.31Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef46 = React46.forwardRef(ArrowUpLeftIcon);
var ArrowUpLeftIcon_default = ForwardRef46;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpOnSquareStackIcon.js
var React47 = __toESM(require_react(), 1);
function ArrowUpOnSquareStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React47.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React47.createElement("title", {
    id: titleId
  }, title) : null, React47.createElement("path", {
    d: "M9.97.97a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1-1.06 1.06l-1.72-1.72v3.44h-1.5V3.31L8.03 5.03a.75.75 0 0 1-1.06-1.06l3-3ZM9.75 6.75v6a.75.75 0 0 0 1.5 0v-6h3a3 3 0 0 1 3 3v7.5a3 3 0 0 1-3 3h-7.5a3 3 0 0 1-3-3v-7.5a3 3 0 0 1 3-3h3Z"
  }), React47.createElement("path", {
    d: "M7.151 21.75a2.999 2.999 0 0 0 2.599 1.5h7.5a3 3 0 0 0 3-3v-7.5c0-1.11-.603-2.08-1.5-2.599v7.099a4.5 4.5 0 0 1-4.5 4.5H7.151Z"
  }));
}
var ForwardRef47 = React47.forwardRef(ArrowUpOnSquareStackIcon);
var ArrowUpOnSquareStackIcon_default = ForwardRef47;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpOnSquareIcon.js
var React48 = __toESM(require_react(), 1);
function ArrowUpOnSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React48.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React48.createElement("title", {
    id: titleId
  }, title) : null, React48.createElement("path", {
    d: "M11.47 1.72a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1-1.06 1.06l-1.72-1.72V7.5h-1.5V4.06L9.53 5.78a.75.75 0 0 1-1.06-1.06l3-3ZM11.25 7.5V15a.75.75 0 0 0 1.5 0V7.5h3.75a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3v-9a3 3 0 0 1 3-3h3.75Z"
  }));
}
var ForwardRef48 = React48.forwardRef(ArrowUpOnSquareIcon);
var ArrowUpOnSquareIcon_default = ForwardRef48;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpRightIcon.js
var React49 = __toESM(require_react(), 1);
function ArrowUpRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React49.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React49.createElement("title", {
    id: titleId
  }, title) : null, React49.createElement("path", {
    fillRule: "evenodd",
    d: "M8.25 3.75H19.5a.75.75 0 0 1 .75.75v11.25a.75.75 0 0 1-1.5 0V6.31L5.03 20.03a.75.75 0 0 1-1.06-1.06L17.69 5.25H8.25a.75.75 0 0 1 0-1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef49 = React49.forwardRef(ArrowUpRightIcon);
var ArrowUpRightIcon_default = ForwardRef49;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpTrayIcon.js
var React50 = __toESM(require_react(), 1);
function ArrowUpTrayIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React50.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React50.createElement("title", {
    id: titleId
  }, title) : null, React50.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06l-3.22-3.22V16.5a.75.75 0 0 1-1.5 0V4.81L8.03 8.03a.75.75 0 0 1-1.06-1.06l4.5-4.5ZM3 15.75a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef50 = React50.forwardRef(ArrowUpTrayIcon);
var ArrowUpTrayIcon_default = ForwardRef50;

// node_modules/@heroicons/react/24/solid/esm/ArrowUpIcon.js
var React51 = __toESM(require_react(), 1);
function ArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React51.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React51.createElement("title", {
    id: titleId
  }, title) : null, React51.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 2.47a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06l-6.22-6.22V21a.75.75 0 0 1-1.5 0V4.81l-6.22 6.22a.75.75 0 1 1-1.06-1.06l7.5-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef51 = React51.forwardRef(ArrowUpIcon);
var ArrowUpIcon_default = ForwardRef51;

// node_modules/@heroicons/react/24/solid/esm/ArrowUturnDownIcon.js
var React52 = __toESM(require_react(), 1);
function ArrowUturnDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React52.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React52.createElement("title", {
    id: titleId
  }, title) : null, React52.createElement("path", {
    fillRule: "evenodd",
    d: "M15 3.75A5.25 5.25 0 0 0 9.75 9v10.19l4.72-4.72a.75.75 0 1 1 1.06 1.06l-6 6a.75.75 0 0 1-1.06 0l-6-6a.75.75 0 1 1 1.06-1.06l4.72 4.72V9a6.75 6.75 0 0 1 13.5 0v3a.75.75 0 0 1-1.5 0V9c0-2.9-2.35-5.25-5.25-5.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef52 = React52.forwardRef(ArrowUturnDownIcon);
var ArrowUturnDownIcon_default = ForwardRef52;

// node_modules/@heroicons/react/24/solid/esm/ArrowUturnLeftIcon.js
var React53 = __toESM(require_react(), 1);
function ArrowUturnLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React53.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React53.createElement("title", {
    id: titleId
  }, title) : null, React53.createElement("path", {
    fillRule: "evenodd",
    d: "M9.53 2.47a.75.75 0 0 1 0 1.06L4.81 8.25H15a6.75 6.75 0 0 1 0 13.5h-3a.75.75 0 0 1 0-1.5h3a5.25 5.25 0 1 0 0-10.5H4.81l4.72 4.72a.75.75 0 1 1-1.06 1.06l-6-6a.75.75 0 0 1 0-1.06l6-6a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef53 = React53.forwardRef(ArrowUturnLeftIcon);
var ArrowUturnLeftIcon_default = ForwardRef53;

// node_modules/@heroicons/react/24/solid/esm/ArrowUturnRightIcon.js
var React54 = __toESM(require_react(), 1);
function ArrowUturnRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React54.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React54.createElement("title", {
    id: titleId
  }, title) : null, React54.createElement("path", {
    fillRule: "evenodd",
    d: "M14.47 2.47a.75.75 0 0 1 1.06 0l6 6a.75.75 0 0 1 0 1.06l-6 6a.75.75 0 1 1-1.06-1.06l4.72-4.72H9a5.25 5.25 0 1 0 0 10.5h3a.75.75 0 0 1 0 1.5H9a6.75 6.75 0 0 1 0-13.5h10.19l-4.72-4.72a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef54 = React54.forwardRef(ArrowUturnRightIcon);
var ArrowUturnRightIcon_default = ForwardRef54;

// node_modules/@heroicons/react/24/solid/esm/ArrowUturnUpIcon.js
var React55 = __toESM(require_react(), 1);
function ArrowUturnUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React55.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React55.createElement("title", {
    id: titleId
  }, title) : null, React55.createElement("path", {
    fillRule: "evenodd",
    d: "M21.53 9.53a.75.75 0 0 1-1.06 0l-4.72-4.72V15a6.75 6.75 0 0 1-13.5 0v-3a.75.75 0 0 1 1.5 0v3a5.25 5.25 0 1 0 10.5 0V4.81L9.53 9.53a.75.75 0 0 1-1.06-1.06l6-6a.75.75 0 0 1 1.06 0l6 6a.75.75 0 0 1 0 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef55 = React55.forwardRef(ArrowUturnUpIcon);
var ArrowUturnUpIcon_default = ForwardRef55;

// node_modules/@heroicons/react/24/solid/esm/ArrowsPointingInIcon.js
var React56 = __toESM(require_react(), 1);
function ArrowsPointingInIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React56.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React56.createElement("title", {
    id: titleId
  }, title) : null, React56.createElement("path", {
    fillRule: "evenodd",
    d: "M3.22 3.22a.75.75 0 0 1 1.06 0l3.97 3.97V4.5a.75.75 0 0 1 1.5 0V9a.75.75 0 0 1-.75.75H4.5a.75.75 0 0 1 0-1.5h2.69L3.22 4.28a.75.75 0 0 1 0-1.06Zm17.56 0a.75.75 0 0 1 0 1.06l-3.97 3.97h2.69a.75.75 0 0 1 0 1.5H15a.75.75 0 0 1-.75-.75V4.5a.75.75 0 0 1 1.5 0v2.69l3.97-3.97a.75.75 0 0 1 1.06 0ZM3.75 15a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-2.69l-3.97 3.97a.75.75 0 0 1-1.06-1.06l3.97-3.97H4.5a.75.75 0 0 1-.75-.75Zm10.5 0a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-2.69l3.97 3.97a.75.75 0 1 1-1.06 1.06l-3.97-3.97v2.69a.75.75 0 0 1-1.5 0V15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef56 = React56.forwardRef(ArrowsPointingInIcon);
var ArrowsPointingInIcon_default = ForwardRef56;

// node_modules/@heroicons/react/24/solid/esm/ArrowsPointingOutIcon.js
var React57 = __toESM(require_react(), 1);
function ArrowsPointingOutIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React57.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React57.createElement("title", {
    id: titleId
  }, title) : null, React57.createElement("path", {
    fillRule: "evenodd",
    d: "M15 3.75a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0V5.56l-3.97 3.97a.75.75 0 1 1-1.06-1.06l3.97-3.97h-2.69a.75.75 0 0 1-.75-.75Zm-12 0A.75.75 0 0 1 3.75 3h4.5a.75.75 0 0 1 0 1.5H5.56l3.97 3.97a.75.75 0 0 1-1.06 1.06L4.5 5.56v2.69a.75.75 0 0 1-1.5 0v-4.5Zm11.47 11.78a.75.75 0 1 1 1.06-1.06l3.97 3.97v-2.69a.75.75 0 0 1 1.5 0v4.5a.75.75 0 0 1-.75.75h-4.5a.75.75 0 0 1 0-1.5h2.69l-3.97-3.97Zm-4.94-1.06a.75.75 0 0 1 0 1.06L5.56 19.5h2.69a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 1 1.5 0v2.69l3.97-3.97a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef57 = React57.forwardRef(ArrowsPointingOutIcon);
var ArrowsPointingOutIcon_default = ForwardRef57;

// node_modules/@heroicons/react/24/solid/esm/ArrowsRightLeftIcon.js
var React58 = __toESM(require_react(), 1);
function ArrowsRightLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React58.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React58.createElement("title", {
    id: titleId
  }, title) : null, React58.createElement("path", {
    fillRule: "evenodd",
    d: "M15.97 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1 0 1.06l-4.5 4.5a.75.75 0 1 1-1.06-1.06l3.22-3.22H7.5a.75.75 0 0 1 0-1.5h11.69l-3.22-3.22a.75.75 0 0 1 0-1.06Zm-7.94 9a.75.75 0 0 1 0 1.06l-3.22 3.22H16.5a.75.75 0 0 1 0 1.5H4.81l3.22 3.22a.75.75 0 1 1-1.06 1.06l-4.5-4.5a.75.75 0 0 1 0-1.06l4.5-4.5a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef58 = React58.forwardRef(ArrowsRightLeftIcon);
var ArrowsRightLeftIcon_default = ForwardRef58;

// node_modules/@heroicons/react/24/solid/esm/ArrowsUpDownIcon.js
var React59 = __toESM(require_react(), 1);
function ArrowsUpDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React59.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React59.createElement("title", {
    id: titleId
  }, title) : null, React59.createElement("path", {
    fillRule: "evenodd",
    d: "M6.97 2.47a.75.75 0 0 1 1.06 0l4.5 4.5a.75.75 0 0 1-1.06 1.06L8.25 4.81V16.5a.75.75 0 0 1-1.5 0V4.81L3.53 8.03a.75.75 0 0 1-1.06-1.06l4.5-4.5Zm9.53 4.28a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V7.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef59 = React59.forwardRef(ArrowsUpDownIcon);
var ArrowsUpDownIcon_default = ForwardRef59;

// node_modules/@heroicons/react/24/solid/esm/AtSymbolIcon.js
var React60 = __toESM(require_react(), 1);
function AtSymbolIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React60.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React60.createElement("title", {
    id: titleId
  }, title) : null, React60.createElement("path", {
    fillRule: "evenodd",
    d: "M17.834 6.166a8.25 8.25 0 1 0 0 11.668.75.75 0 0 1 1.06 1.06c-3.807 3.808-9.98 3.808-13.788 0-3.808-3.807-3.808-9.98 0-13.788 3.807-3.808 9.98-3.808 13.788 0A9.722 9.722 0 0 1 21.75 12c0 .975-.296 1.887-.809 2.571-.514.685-1.28 1.179-2.191 1.179-.904 0-1.666-.487-2.18-1.164a5.25 5.25 0 1 1-.82-6.26V8.25a.75.75 0 0 1 1.5 0V12c0 .682.208 1.27.509 1.671.3.401.659.579.991.579.332 0 .69-.178.991-.579.3-.4.509-.99.509-1.671a8.222 8.222 0 0 0-2.416-5.834ZM15.75 12a3.75 3.75 0 1 0-7.5 0 3.75 3.75 0 0 0 7.5 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef60 = React60.forwardRef(AtSymbolIcon);
var AtSymbolIcon_default = ForwardRef60;

// node_modules/@heroicons/react/24/solid/esm/BackspaceIcon.js
var React61 = __toESM(require_react(), 1);
function BackspaceIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React61.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React61.createElement("title", {
    id: titleId
  }, title) : null, React61.createElement("path", {
    fillRule: "evenodd",
    d: "M2.515 10.674a1.875 1.875 0 0 0 0 2.652L8.89 19.7c.352.351.829.549 1.326.549H19.5a3 3 0 0 0 3-3V6.75a3 3 0 0 0-3-3h-9.284c-.497 0-.974.198-1.326.55l-6.375 6.374ZM12.53 9.22a.75.75 0 1 0-1.06 1.06L13.19 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06l1.72-1.72 1.72 1.72a.75.75 0 1 0 1.06-1.06L15.31 12l1.72-1.72a.75.75 0 1 0-1.06-1.06l-1.72 1.72-1.72-1.72Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef61 = React61.forwardRef(BackspaceIcon);
var BackspaceIcon_default = ForwardRef61;

// node_modules/@heroicons/react/24/solid/esm/BackwardIcon.js
var React62 = __toESM(require_react(), 1);
function BackwardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React62.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React62.createElement("title", {
    id: titleId
  }, title) : null, React62.createElement("path", {
    d: "M9.195 18.44c1.25.714 2.805-.189 2.805-1.629v-2.34l6.945 3.968c1.25.715 2.805-.188 2.805-1.628V8.69c0-1.44-1.555-2.343-2.805-1.628L12 11.029v-2.34c0-1.44-1.555-2.343-2.805-1.628l-7.108 4.061c-1.26.72-1.26 2.536 0 3.256l7.108 4.061Z"
  }));
}
var ForwardRef62 = React62.forwardRef(BackwardIcon);
var BackwardIcon_default = ForwardRef62;

// node_modules/@heroicons/react/24/solid/esm/BanknotesIcon.js
var React63 = __toESM(require_react(), 1);
function BanknotesIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React63.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React63.createElement("title", {
    id: titleId
  }, title) : null, React63.createElement("path", {
    d: "M12 7.5a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5Z"
  }), React63.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 4.875C1.5 3.839 2.34 3 3.375 3h17.25c1.035 0 1.875.84 1.875 1.875v9.75c0 1.036-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 14.625v-9.75ZM8.25 9.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM18.75 9a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V9.75a.75.75 0 0 0-.75-.75h-.008ZM4.5 9.75A.75.75 0 0 1 5.25 9h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H5.25a.75.75 0 0 1-.75-.75V9.75Z",
    clipRule: "evenodd"
  }), React63.createElement("path", {
    d: "M2.25 18a.75.75 0 0 0 0 1.5c5.4 0 10.63.722 15.6 2.075 1.19.324 2.4-.558 2.4-1.82V18.75a.75.75 0 0 0-.75-.75H2.25Z"
  }));
}
var ForwardRef63 = React63.forwardRef(BanknotesIcon);
var BanknotesIcon_default = ForwardRef63;

// node_modules/@heroicons/react/24/solid/esm/Bars2Icon.js
var React64 = __toESM(require_react(), 1);
function Bars2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React64.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React64.createElement("title", {
    id: titleId
  }, title) : null, React64.createElement("path", {
    fillRule: "evenodd",
    d: "M3 9a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 9Zm0 6.75a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef64 = React64.forwardRef(Bars2Icon);
var Bars2Icon_default = ForwardRef64;

// node_modules/@heroicons/react/24/solid/esm/Bars3BottomLeftIcon.js
var React65 = __toESM(require_react(), 1);
function Bars3BottomLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React65.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React65.createElement("title", {
    id: titleId
  }, title) : null, React65.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6.75A.75.75 0 0 1 3.75 6h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.75ZM3 12a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 12Zm0 5.25a.75.75 0 0 1 .75-.75H12a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef65 = React65.forwardRef(Bars3BottomLeftIcon);
var Bars3BottomLeftIcon_default = ForwardRef65;

// node_modules/@heroicons/react/24/solid/esm/Bars3BottomRightIcon.js
var React66 = __toESM(require_react(), 1);
function Bars3BottomRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React66.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React66.createElement("title", {
    id: titleId
  }, title) : null, React66.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6.75A.75.75 0 0 1 3.75 6h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.75ZM3 12a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 12Zm8.25 5.25a.75.75 0 0 1 .75-.75h8.25a.75.75 0 0 1 0 1.5H12a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef66 = React66.forwardRef(Bars3BottomRightIcon);
var Bars3BottomRightIcon_default = ForwardRef66;

// node_modules/@heroicons/react/24/solid/esm/Bars3CenterLeftIcon.js
var React67 = __toESM(require_react(), 1);
function Bars3CenterLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React67.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React67.createElement("title", {
    id: titleId
  }, title) : null, React67.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6.75A.75.75 0 0 1 3.75 6h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.75ZM3 12a.75.75 0 0 1 .75-.75H12a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 12Zm0 5.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef67 = React67.forwardRef(Bars3CenterLeftIcon);
var Bars3CenterLeftIcon_default = ForwardRef67;

// node_modules/@heroicons/react/24/solid/esm/Bars3Icon.js
var React68 = __toESM(require_react(), 1);
function Bars3Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React68.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React68.createElement("title", {
    id: titleId
  }, title) : null, React68.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6.75A.75.75 0 0 1 3.75 6h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.75ZM3 12a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 12Zm0 5.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef68 = React68.forwardRef(Bars3Icon);
var Bars3Icon_default = ForwardRef68;

// node_modules/@heroicons/react/24/solid/esm/Bars4Icon.js
var React69 = __toESM(require_react(), 1);
function Bars4Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React69.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React69.createElement("title", {
    id: titleId
  }, title) : null, React69.createElement("path", {
    fillRule: "evenodd",
    d: "M3 5.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 5.25Zm0 4.5A.75.75 0 0 1 3.75 9h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 9.75Zm0 4.5a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Zm0 4.5a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef69 = React69.forwardRef(Bars4Icon);
var Bars4Icon_default = ForwardRef69;

// node_modules/@heroicons/react/24/solid/esm/BarsArrowDownIcon.js
var React70 = __toESM(require_react(), 1);
function BarsArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React70.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React70.createElement("title", {
    id: titleId
  }, title) : null, React70.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 4.5A.75.75 0 0 1 3 3.75h14.25a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1-.75-.75Zm0 4.5A.75.75 0 0 1 3 8.25h9.75a.75.75 0 0 1 0 1.5H3A.75.75 0 0 1 2.25 9Zm15-.75A.75.75 0 0 1 18 9v10.19l2.47-2.47a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0l-3.75-3.75a.75.75 0 1 1 1.06-1.06l2.47 2.47V9a.75.75 0 0 1 .75-.75Zm-15 5.25a.75.75 0 0 1 .75-.75h9.75a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef70 = React70.forwardRef(BarsArrowDownIcon);
var BarsArrowDownIcon_default = ForwardRef70;

// node_modules/@heroicons/react/24/solid/esm/BarsArrowUpIcon.js
var React71 = __toESM(require_react(), 1);
function BarsArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React71.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React71.createElement("title", {
    id: titleId
  }, title) : null, React71.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 4.5A.75.75 0 0 1 3 3.75h14.25a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1-.75-.75Zm14.47 3.97a.75.75 0 0 1 1.06 0l3.75 3.75a.75.75 0 1 1-1.06 1.06L18 10.81V21a.75.75 0 0 1-1.5 0V10.81l-2.47 2.47a.75.75 0 1 1-1.06-1.06l3.75-3.75ZM2.25 9A.75.75 0 0 1 3 8.25h9.75a.75.75 0 0 1 0 1.5H3A.75.75 0 0 1 2.25 9Zm0 4.5a.75.75 0 0 1 .75-.75h5.25a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef71 = React71.forwardRef(BarsArrowUpIcon);
var BarsArrowUpIcon_default = ForwardRef71;

// node_modules/@heroicons/react/24/solid/esm/Battery0Icon.js
var React72 = __toESM(require_react(), 1);
function Battery0Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React72.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React72.createElement("title", {
    id: titleId
  }, title) : null, React72.createElement("path", {
    fillRule: "evenodd",
    d: "M.75 9.75a3 3 0 0 1 3-3h15a3 3 0 0 1 3 3v.038c.856.173 1.5.93 1.5 1.837v2.25c0 .907-.644 1.664-1.5 1.838v.037a3 3 0 0 1-3 3h-15a3 3 0 0 1-3-3v-6Zm19.5 0a1.5 1.5 0 0 0-1.5-1.5h-15a1.5 1.5 0 0 0-1.5 1.5v6a1.5 1.5 0 0 0 1.5 1.5h15a1.5 1.5 0 0 0 1.5-1.5v-6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef72 = React72.forwardRef(Battery0Icon);
var Battery0Icon_default = ForwardRef72;

// node_modules/@heroicons/react/24/solid/esm/Battery100Icon.js
var React73 = __toESM(require_react(), 1);
function Battery100Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React73.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React73.createElement("title", {
    id: titleId
  }, title) : null, React73.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 6.75a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-.037c.856-.174 1.5-.93 1.5-1.838v-2.25c0-.907-.644-1.664-1.5-1.837V9.75a3 3 0 0 0-3-3h-15Zm15 1.5a1.5 1.5 0 0 1 1.5 1.5v6a1.5 1.5 0 0 1-1.5 1.5h-15a1.5 1.5 0 0 1-1.5-1.5v-6a1.5 1.5 0 0 1 1.5-1.5h15ZM4.5 9.75a.75.75 0 0 0-.75.75V15c0 .414.336.75.75.75H18a.75.75 0 0 0 .75-.75v-4.5a.75.75 0 0 0-.75-.75H4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef73 = React73.forwardRef(Battery100Icon);
var Battery100Icon_default = ForwardRef73;

// node_modules/@heroicons/react/24/solid/esm/Battery50Icon.js
var React74 = __toESM(require_react(), 1);
function Battery50Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React74.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React74.createElement("title", {
    id: titleId
  }, title) : null, React74.createElement("path", {
    d: "M4.5 9.75a.75.75 0 0 0-.75.75V15c0 .414.336.75.75.75h6.75A.75.75 0 0 0 12 15v-4.5a.75.75 0 0 0-.75-.75H4.5Z"
  }), React74.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 6.75a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-.037c.856-.174 1.5-.93 1.5-1.838v-2.25c0-.907-.644-1.664-1.5-1.837V9.75a3 3 0 0 0-3-3h-15Zm15 1.5a1.5 1.5 0 0 1 1.5 1.5v6a1.5 1.5 0 0 1-1.5 1.5h-15a1.5 1.5 0 0 1-1.5-1.5v-6a1.5 1.5 0 0 1 1.5-1.5h15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef74 = React74.forwardRef(Battery50Icon);
var Battery50Icon_default = ForwardRef74;

// node_modules/@heroicons/react/24/solid/esm/BeakerIcon.js
var React75 = __toESM(require_react(), 1);
function BeakerIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React75.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React75.createElement("title", {
    id: titleId
  }, title) : null, React75.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3.798v5.02a3 3 0 0 1-.879 2.121l-2.377 2.377a9.845 9.845 0 0 1 5.091 1.013 8.315 8.315 0 0 0 5.713.636l.285-.071-3.954-3.955a3 3 0 0 1-.879-2.121v-5.02a23.614 23.614 0 0 0-3 0Zm4.5.138a.75.75 0 0 0 .093-1.495A24.837 24.837 0 0 0 12 2.25a25.048 25.048 0 0 0-3.093.191A.75.75 0 0 0 9 3.936v4.882a1.5 1.5 0 0 1-.44 1.06l-6.293 6.294c-1.62 1.621-.903 4.475 1.471 4.88 2.686.46 5.447.698 8.262.698 2.816 0 5.576-.239 8.262-.697 2.373-.406 3.092-3.26 1.47-4.881L15.44 9.879A1.5 1.5 0 0 1 15 8.818V3.936Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef75 = React75.forwardRef(BeakerIcon);
var BeakerIcon_default = ForwardRef75;

// node_modules/@heroicons/react/24/solid/esm/BellAlertIcon.js
var React76 = __toESM(require_react(), 1);
function BellAlertIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React76.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React76.createElement("title", {
    id: titleId
  }, title) : null, React76.createElement("path", {
    d: "M5.85 3.5a.75.75 0 0 0-1.117-1 9.719 9.719 0 0 0-2.348 4.876.75.75 0 0 0 1.479.248A8.219 8.219 0 0 1 5.85 3.5ZM19.267 2.5a.75.75 0 1 0-1.118 1 8.22 8.22 0 0 1 1.987 *********** 0 0 0 1.48-.248A9.72 9.72 0 0 0 19.266 2.5Z"
  }), React76.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25A6.75 6.75 0 0 0 5.25 9v.75a8.217 8.217 0 0 1-2.119 ********** 0 0 0 .298 1.206c1.544.57 3.16.99 4.831 1.243a3.75 3.75 0 1 0 7.48 0 24.583 24.583 0 0 0 4.83-*********** 0 0 0 .298-1.205 8.217 8.217 0 0 1-2.118-5.52V9A6.75 6.75 0 0 0 12 2.25ZM9.75 18c0-.034 0-.067.002-.1a25.05 25.05 0 0 0 4.496 0l.002.1a2.25 2.25 0 1 1-4.5 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef76 = React76.forwardRef(BellAlertIcon);
var BellAlertIcon_default = ForwardRef76;

// node_modules/@heroicons/react/24/solid/esm/BellSlashIcon.js
var React77 = __toESM(require_react(), 1);
function BellSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React77.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React77.createElement("title", {
    id: titleId
  }, title) : null, React77.createElement("path", {
    d: "M3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18ZM20.57 16.476c-.223.082-.448.161-.674.238L7.319 4.137A6.75 6.75 0 0 1 18.75 9v.75c0 2.123.8 4.057 2.118 5.52a.75.75 0 0 1-.297 1.206Z"
  }), React77.createElement("path", {
    fillRule: "evenodd",
    d: "M5.25 9c0-.184.007-.366.022-.546l10.384 10.384a3.751 3.751 0 0 1-7.396-1.119 24.585 24.585 0 0 1-4.831-*********** 0 0 1-.298-1.205A8.217 8.217 0 0 0 5.25 9.75V9Zm4.502 8.9a2.25 2.25 0 1 0 4.496 0 25.057 25.057 0 0 1-4.496 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef77 = React77.forwardRef(BellSlashIcon);
var BellSlashIcon_default = ForwardRef77;

// node_modules/@heroicons/react/24/solid/esm/BellSnoozeIcon.js
var React78 = __toESM(require_react(), 1);
function BellSnoozeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React78.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React78.createElement("title", {
    id: titleId
  }, title) : null, React78.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25A6.75 6.75 0 0 0 5.25 9v.75a8.217 8.217 0 0 1-2.119 ********** 0 0 0 .298 1.206c1.544.57 3.16.99 4.831 1.243a3.75 3.75 0 1 0 7.48 0 24.583 24.583 0 0 0 4.83-*********** 0 0 0 .298-1.205 8.217 8.217 0 0 1-2.118-5.52V9A6.75 6.75 0 0 0 12 2.25ZM9.75 18c0-.034 0-.067.002-.1a25.05 25.05 0 0 0 4.496 0l.002.1a2.25 2.25 0 1 1-4.5 0Zm.75-10.5a.75.75 0 0 0 0 1.5h1.599l-2.223 3.334A.75.75 0 0 0 10.5 13.5h3a.75.75 0 0 0 0-1.5h-1.599l2.223-3.334A.75.75 0 0 0 13.5 7.5h-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef78 = React78.forwardRef(BellSnoozeIcon);
var BellSnoozeIcon_default = ForwardRef78;

// node_modules/@heroicons/react/24/solid/esm/BellIcon.js
var React79 = __toESM(require_react(), 1);
function BellIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React79.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React79.createElement("title", {
    id: titleId
  }, title) : null, React79.createElement("path", {
    fillRule: "evenodd",
    d: "M5.25 9a6.75 6.75 0 0 1 13.5 0v.75c0 2.123.8 4.057 2.118 5.52a.75.75 0 0 1-.297 1.206c-1.544.57-3.16.99-4.831 1.243a3.75 3.75 0 1 1-7.48 0 24.585 24.585 0 0 1-4.831-*********** 0 0 1-.298-1.205A8.217 8.217 0 0 0 5.25 9.75V9Zm4.502 8.9a2.25 2.25 0 1 0 4.496 0 25.057 25.057 0 0 1-4.496 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef79 = React79.forwardRef(BellIcon);
var BellIcon_default = ForwardRef79;

// node_modules/@heroicons/react/24/solid/esm/BoldIcon.js
var React80 = __toESM(require_react(), 1);
function BoldIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React80.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React80.createElement("title", {
    id: titleId
  }, title) : null, React80.createElement("path", {
    fillRule: "evenodd",
    d: "M5.246 3.744a.75.75 0 0 1 .75-.75h7.125a4.875 4.875 0 0 1 3.346 8.422 5.25 5.25 0 0 1-2.97 9.58h-7.5a.75.75 0 0 1-.75-.75V3.744Zm7.125 6.75a2.625 2.625 0 0 0 0-5.25H8.246v5.25h4.125Zm-4.125 2.251v6h4.5a3 3 0 0 0 0-6h-4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef80 = React80.forwardRef(BoldIcon);
var BoldIcon_default = ForwardRef80;

// node_modules/@heroicons/react/24/solid/esm/BoltSlashIcon.js
var React81 = __toESM(require_react(), 1);
function BoltSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React81.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React81.createElement("title", {
    id: titleId
  }, title) : null, React81.createElement("path", {
    d: "m20.798 11.012-3.188 3.416L9.462 6.28l4.24-4.542a.75.75 0 0 1 1.272.71L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262ZM3.202 12.988 6.39 9.572l8.148 8.148-4.24 4.542a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262ZM3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18Z"
  }));
}
var ForwardRef81 = React81.forwardRef(BoltSlashIcon);
var BoltSlashIcon_default = ForwardRef81;

// node_modules/@heroicons/react/24/solid/esm/BoltIcon.js
var React82 = __toESM(require_react(), 1);
function BoltIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React82.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React82.createElement("title", {
    id: titleId
  }, title) : null, React82.createElement("path", {
    fillRule: "evenodd",
    d: "M14.615 1.595a.75.75 0 0 1 .359.852L12.982 9.75h7.268a.75.75 0 0 1 .548 1.262l-10.5 11.25a.75.75 0 0 1-1.272-.71l1.992-7.302H3.75a.75.75 0 0 1-.548-1.262l10.5-11.25a.75.75 0 0 1 .913-.143Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef82 = React82.forwardRef(BoltIcon);
var BoltIcon_default = ForwardRef82;

// node_modules/@heroicons/react/24/solid/esm/BookOpenIcon.js
var React83 = __toESM(require_react(), 1);
function BookOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React83.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React83.createElement("title", {
    id: titleId
  }, title) : null, React83.createElement("path", {
    d: "M11.25 4.533A9.707 9.707 0 0 0 6 3a9.735 9.735 0 0 0-3.25.555.75.75 0 0 0-.5.707v14.25a.75.75 0 0 0 1 .707A8.237 8.237 0 0 1 6 18.75c1.995 0 3.823.707 5.25 1.886V4.533ZM12.75 20.636A8.214 8.214 0 0 1 18 18.75c.966 0 1.89.166 2.75.47a.75.75 0 0 0 1-.708V4.262a.75.75 0 0 0-.5-.707A9.735 9.735 0 0 0 18 3a9.707 9.707 0 0 0-5.25 1.533v16.103Z"
  }));
}
var ForwardRef83 = React83.forwardRef(BookOpenIcon);
var BookOpenIcon_default = ForwardRef83;

// node_modules/@heroicons/react/24/solid/esm/BookmarkSlashIcon.js
var React84 = __toESM(require_react(), 1);
function BookmarkSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React84.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React84.createElement("title", {
    id: titleId
  }, title) : null, React84.createElement("path", {
    d: "M3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18ZM20.25 5.507v11.561L5.853 2.671c.15-.043.306-.075.467-.094a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93ZM3.75 21V6.932l14.063 14.063L12 18.088l-7.165 3.583A.75.75 0 0 1 3.75 21Z"
  }));
}
var ForwardRef84 = React84.forwardRef(BookmarkSlashIcon);
var BookmarkSlashIcon_default = ForwardRef84;

// node_modules/@heroicons/react/24/solid/esm/BookmarkSquareIcon.js
var React85 = __toESM(require_react(), 1);
function BookmarkSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React85.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React85.createElement("title", {
    id: titleId
  }, title) : null, React85.createElement("path", {
    fillRule: "evenodd",
    d: "M6 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6Zm1.5 1.5a.75.75 0 0 0-.75.75V16.5a.75.75 0 0 0 1.085.67L12 15.089l4.165 2.083a.75.75 0 0 0 1.085-.671V5.25a.75.75 0 0 0-.75-.75h-9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef85 = React85.forwardRef(BookmarkSquareIcon);
var BookmarkSquareIcon_default = ForwardRef85;

// node_modules/@heroicons/react/24/solid/esm/BookmarkIcon.js
var React86 = __toESM(require_react(), 1);
function BookmarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React86.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React86.createElement("title", {
    id: titleId
  }, title) : null, React86.createElement("path", {
    fillRule: "evenodd",
    d: "M6.32 2.577a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V21a.75.75 0 0 1-1.085.67L12 18.089l-7.165 3.583A.75.75 0 0 1 3.75 21V5.507c0-1.47 1.073-2.756 2.57-2.93Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef86 = React86.forwardRef(BookmarkIcon);
var BookmarkIcon_default = ForwardRef86;

// node_modules/@heroicons/react/24/solid/esm/BriefcaseIcon.js
var React87 = __toESM(require_react(), 1);
function BriefcaseIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React87.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React87.createElement("title", {
    id: titleId
  }, title) : null, React87.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 5.25a3 3 0 0 1 3-3h3a3 3 0 0 1 3 3v.205c.933.085 1.857.197 2.774.334 1.454.218 2.476 1.483 2.476 2.917v3.033c0 1.211-.734 2.352-1.936 2.752A24.726 24.726 0 0 1 12 15.75c-2.73 0-5.357-.442-7.814-1.259-1.202-.4-1.936-1.541-1.936-2.752V8.706c0-1.434 1.022-2.7 2.476-2.917A48.814 48.814 0 0 1 7.5 5.455V5.25Zm7.5 0v.09a49.488 49.488 0 0 0-6 0v-.09a1.5 1.5 0 0 1 1.5-1.5h3a1.5 1.5 0 0 1 1.5 1.5Zm-3 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }), React87.createElement("path", {
    d: "M3 18.4v-2.796a4.3 4.3 0 0 0 .713.31A26.226 26.226 0 0 0 12 17.25c2.892 0 5.68-.468 8.287-1.335.252-.084.49-.189.713-.311V18.4c0 1.452-1.047 2.728-2.523 2.923-2.12.282-4.282.427-6.477.427a49.19 49.19 0 0 1-6.477-.427C4.047 21.128 3 19.852 3 18.4Z"
  }));
}
var ForwardRef87 = React87.forwardRef(BriefcaseIcon);
var BriefcaseIcon_default = ForwardRef87;

// node_modules/@heroicons/react/24/solid/esm/BugAntIcon.js
var React88 = __toESM(require_react(), 1);
function BugAntIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React88.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React88.createElement("title", {
    id: titleId
  }, title) : null, React88.createElement("path", {
    fillRule: "evenodd",
    d: "M8.478 1.6a.75.75 0 0 1 .273 1.026 3.72 3.72 0 0 0-.425 1.121c.058.058.118.114.18.168A4.491 4.491 0 0 1 12 2.25c1.413 0 2.673.651 3.497 1.668.06-.054.12-.11.178-.167a3.717 3.717 0 0 0-.426-1.125.75.75 0 1 1 1.298-.752 5.22 5.22 0 0 1 .671 2.046.75.75 0 0 1-.187.582c-.241.27-.505.52-.787.749a4.494 4.494 0 0 1 .216 2.1c-.106.792-.753 1.295-1.417 1.403-.182.03-.364.057-.547.081.152.227.273.476.359.742a23.122 23.122 0 0 0 3.832-.803 23.241 23.241 0 0 0-.345-2.634.75.75 0 0 1 1.474-.28c.21 1.115.348 2.256.404 3.418a.75.75 0 0 1-.516.75c-1.527.499-3.119.854-4.76 1.049-.074.38-.22.735-.423 1.05 2.066.209 4.058.672 5.943 1.358a.75.75 0 0 1 .492.75 24.665 24.665 0 0 1-1.189 ********** 0 0 1-1.425-.47 23.14 23.14 0 0 0 1.077-5.306c-.5-.169-1.009-.32-1.524-.455.068.234.104.484.104.746 0 3.956-2.521 7.5-6 7.5-3.478 0-6-3.544-6-7.5 0-.262.037-.511.104-.746-.514.135-1.022.286-1.522.455.154 1.838.52 3.616 1.077 5.307a.75.75 0 1 1-1.425.468 24.662 24.662 0 0 1-1.19-********** 0 0 1 .493-.749 24.586 24.586 0 0 1 4.964-1.24h.01c.321-.046.644-.085.969-.118a2.983 2.983 0 0 1-.424-1.05 24.614 24.614 0 0 1-4.76-********** 0 0 1-.516-.75c.057-1.16.194-2.302.405-3.417a.75.75 0 0 1 1.474.28c-.164.862-.28 1.74-.345 2.634 1.237.371 2.517.642 3.832.803.085-.266.207-.515.359-.742a18.698 18.698 0 0 1-.547-.08c-.664-.11-1.311-.612-1.417-1.404a4.535 4.535 0 0 1 .217-2.103 6.788 6.788 0 0 1-.788-.751.75.75 0 0 1-.187-.583 5.22 5.22 0 0 1 .67-********** 0 0 1 1.026-.273Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef88 = React88.forwardRef(BugAntIcon);
var BugAntIcon_default = ForwardRef88;

// node_modules/@heroicons/react/24/solid/esm/BuildingLibraryIcon.js
var React89 = __toESM(require_react(), 1);
function BuildingLibraryIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React89.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React89.createElement("title", {
    id: titleId
  }, title) : null, React89.createElement("path", {
    d: "M11.584 2.376a.75.75 0 0 1 .832 0l9 6a.75.75 0 1 1-.832 1.248L12 3.901 3.416 9.624a.75.75 0 0 1-.832-1.248l9-6Z"
  }), React89.createElement("path", {
    fillRule: "evenodd",
    d: "M20.25 10.332v9.918H21a.75.75 0 0 1 0 1.5H3a.75.75 0 0 1 0-1.5h.75v-9.918a.75.75 0 0 1 .634-.74A49.109 49.109 0 0 1 12 9c2.59 0 5.134.202 7.616.592a.75.75 0 0 1 .634.74Zm-7.5 2.418a.75.75 0 0 0-1.5 0v6.75a.75.75 0 0 0 1.5 0v-6.75Zm3-.75a.75.75 0 0 1 .75.75v6.75a.75.75 0 0 1-1.5 0v-6.75a.75.75 0 0 1 .75-.75ZM9 12.75a.75.75 0 0 0-1.5 0v6.75a.75.75 0 0 0 1.5 0v-6.75Z",
    clipRule: "evenodd"
  }), React89.createElement("path", {
    d: "M12 7.875a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z"
  }));
}
var ForwardRef89 = React89.forwardRef(BuildingLibraryIcon);
var BuildingLibraryIcon_default = ForwardRef89;

// node_modules/@heroicons/react/24/solid/esm/BuildingOffice2Icon.js
var React90 = __toESM(require_react(), 1);
function BuildingOffice2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React90.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React90.createElement("title", {
    id: titleId
  }, title) : null, React90.createElement("path", {
    fillRule: "evenodd",
    d: "M3 2.25a.75.75 0 0 0 0 1.5v16.5h-.75a.75.75 0 0 0 0 1.5H15v-18a.75.75 0 0 0 0-1.5H3ZM6.75 19.5v-2.25a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h.75a.75.75 0 0 1 0 1.5h-.75A.75.75 0 0 1 6 6.75ZM6.75 9a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM6 12.75a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 6a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75Zm-.75 3.75A.75.75 0 0 1 10.5 9h.75a.75.75 0 0 1 0 1.5h-.75a.75.75 0 0 1-.75-.75ZM10.5 12a.75.75 0 0 0 0 1.5h.75a.75.75 0 0 0 0-1.5h-.75ZM16.5 6.75v15h5.25a.75.75 0 0 0 0-1.5H21v-12a.75.75 0 0 0 0-1.5h-4.5Zm1.5 4.5a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 2.25a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75v-.008a.75.75 0 0 0-.75-.75h-.008ZM18 17.25a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef90 = React90.forwardRef(BuildingOffice2Icon);
var BuildingOffice2Icon_default = ForwardRef90;

// node_modules/@heroicons/react/24/solid/esm/BuildingOfficeIcon.js
var React91 = __toESM(require_react(), 1);
function BuildingOfficeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React91.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React91.createElement("title", {
    id: titleId
  }, title) : null, React91.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2.25a.75.75 0 0 0 0 1.5v16.5h-.75a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5h-.75V3.75a.75.75 0 0 0 0-1.5h-15ZM9 6a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5H9Zm-.75 3.75A.75.75 0 0 1 9 9h1.5a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM9 12a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5H9Zm3.75-5.25A.75.75 0 0 1 13.5 6H15a.75.75 0 0 1 0 1.5h-1.5a.75.75 0 0 1-.75-.75ZM13.5 9a.75.75 0 0 0 0 1.5H15A.75.75 0 0 0 15 9h-1.5Zm-.75 3.75a.75.75 0 0 1 .75-.75H15a.75.75 0 0 1 0 1.5h-1.5a.75.75 0 0 1-.75-.75ZM9 19.5v-2.25a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.75.75h-4.5A.75.75 0 0 1 9 19.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef91 = React91.forwardRef(BuildingOfficeIcon);
var BuildingOfficeIcon_default = ForwardRef91;

// node_modules/@heroicons/react/24/solid/esm/BuildingStorefrontIcon.js
var React92 = __toESM(require_react(), 1);
function BuildingStorefrontIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React92.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React92.createElement("title", {
    id: titleId
  }, title) : null, React92.createElement("path", {
    d: "M5.223 2.25c-.497 0-.974.198-1.325.55l-1.3 1.298A3.75 3.75 0 0 0 7.5 9.75c.627.47 1.406.75 2.25.75.844 0 1.624-.28 2.25-.75.626.47 1.406.75 2.25.75.844 0 1.623-.28 2.25-.75a3.75 3.75 0 0 0 4.902-5.652l-1.3-1.299a1.875 1.875 0 0 0-1.325-.549H5.223Z"
  }), React92.createElement("path", {
    fillRule: "evenodd",
    d: "M3 20.25v-8.755c1.42.674 3.08.673 4.5 0A5.234 5.234 0 0 0 9.75 12c.804 0 1.568-.182 2.25-.506a5.234 5.234 0 0 0 2.25.506c.804 0 1.567-.182 2.25-.506 1.42.674 3.08.675 4.5.001v8.755h.75a.75.75 0 0 1 0 1.5H2.25a.75.75 0 0 1 0-1.5H3Zm3-6a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-.75.75h-3a.75.75 0 0 1-.75-.75v-3Zm8.25-.75a.75.75 0 0 0-.75.75v5.25c0 .414.336.75.75.75h3a.75.75 0 0 0 .75-.75v-5.25a.75.75 0 0 0-.75-.75h-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef92 = React92.forwardRef(BuildingStorefrontIcon);
var BuildingStorefrontIcon_default = ForwardRef92;

// node_modules/@heroicons/react/24/solid/esm/CakeIcon.js
var React93 = __toESM(require_react(), 1);
function CakeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React93.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React93.createElement("title", {
    id: titleId
  }, title) : null, React93.createElement("path", {
    d: "m15 1.784-.796.795a1.125 1.125 0 1 0 1.591 0L15 1.784ZM12 1.784l-.796.795a1.125 1.125 0 1 0 1.591 0L12 1.784ZM9 1.784l-.796.795a1.125 1.125 0 1 0 1.591 0L9 1.784ZM9.75 7.547c.498-.021.998-.035 1.5-.042V6.75a.75.75 0 0 1 1.5 0v.755c.502.007 1.002.021 1.5.042V6.75a.75.75 0 0 1 1.5 0v.88l.307.022c1.55.117 2.693 1.427 2.693 2.946v1.018a62.182 62.182 0 0 0-13.5 0v-1.018c0-1.519 1.143-2.829 2.693-2.946l.307-.022v-.88a.75.75 0 0 1 1.5 0v.797ZM12 12.75c-2.472 0-4.9.184-7.274.54-1.454.217-2.476 1.482-2.476 2.916v.384a4.104 4.104 0 0 1 2.585.364 2.605 2.605 0 0 0 2.33 0 4.104 4.104 0 0 1 3.67 0 2.605 2.605 0 0 0 2.33 0 4.104 4.104 0 0 1 3.67 0 2.605 2.605 0 0 0 2.33 0 4.104 4.104 0 0 1 2.585-.364v-.384c0-1.434-1.022-2.7-2.476-2.917A49.138 49.138 0 0 0 12 12.75ZM21.75 18.131a2.604 2.604 0 0 0-1.915.165 4.104 4.104 0 0 1-3.67 0 2.605 2.605 0 0 0-2.33 0 4.104 4.104 0 0 1-3.67 0 2.605 2.605 0 0 0-2.33 0 4.104 4.104 0 0 1-3.67 0 2.604 2.604 0 0 0-1.915-.165v2.494c0 1.035.84 1.875 1.875 1.875h15.75c1.035 0 1.875-.84 1.875-1.875v-2.494Z"
  }));
}
var ForwardRef93 = React93.forwardRef(CakeIcon);
var CakeIcon_default = ForwardRef93;

// node_modules/@heroicons/react/24/solid/esm/CalculatorIcon.js
var React94 = __toESM(require_react(), 1);
function CalculatorIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React94.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React94.createElement("title", {
    id: titleId
  }, title) : null, React94.createElement("path", {
    fillRule: "evenodd",
    d: "M6.32 1.827a49.255 49.255 0 0 1 11.36 0c1.497.174 2.57 1.46 2.57 2.93V19.5a3 3 0 0 1-3 3H6.75a3 3 0 0 1-3-3V4.757c0-1.47 1.073-2.756 2.57-2.93ZM7.5 11.25a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H8.25a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H8.25Zm-.75 3a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H8.25a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V18a.75.75 0 0 0-.75-.75H8.25Zm1.748-6a.75.75 0 0 1 .75-.75h.007a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.007a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.335.75.75.75h.007a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75h-.007Zm-.75 3a.75.75 0 0 1 .75-.75h.007a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.007a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.335.75.75.75h.007a.75.75 0 0 0 .75-.75V18a.75.75 0 0 0-.75-.75h-.007Zm1.754-6a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75h-.008Zm-.75 3a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V18a.75.75 0 0 0-.75-.75h-.008Zm1.748-6a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Zm.75 1.5a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75h-.008Zm-8.25-6A.75.75 0 0 1 8.25 6h7.5a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-7.5a.75.75 0 0 1-.75-.75v-.75Zm9 9a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef94 = React94.forwardRef(CalculatorIcon);
var CalculatorIcon_default = ForwardRef94;

// node_modules/@heroicons/react/24/solid/esm/CalendarDateRangeIcon.js
var React95 = __toESM(require_react(), 1);
function CalendarDateRangeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React95.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React95.createElement("title", {
    id: titleId
  }, title) : null, React95.createElement("path", {
    d: "M12 11.993a.75.75 0 0 0-.75.75v.006c0 .414.336.75.75.75h.006a.75.75 0 0 0 .75-.75v-.006a.75.75 0 0 0-.75-.75H12ZM12 16.494a.75.75 0 0 0-.75.75v.005c0 .414.335.75.75.75h.005a.75.75 0 0 0 .75-.75v-.005a.75.75 0 0 0-.75-.75H12ZM8.999 17.244a.75.75 0 0 1 .75-.75h.006a.75.75 0 0 1 .75.75v.006a.75.75 0 0 1-.75.75h-.006a.75.75 0 0 1-.75-.75v-.006ZM7.499 16.494a.75.75 0 0 0-.75.75v.005c0 .414.336.75.75.75h.005a.75.75 0 0 0 .75-.75v-.005a.75.75 0 0 0-.75-.75H7.5ZM13.499 14.997a.75.75 0 0 1 .75-.75h.006a.75.75 0 0 1 .75.75v.005a.75.75 0 0 1-.75.75h-.006a.75.75 0 0 1-.75-.75v-.005ZM14.25 16.494a.75.75 0 0 0-.75.75v.006c0 .414.335.75.75.75h.005a.75.75 0 0 0 .75-.75v-.006a.75.75 0 0 0-.75-.75h-.005ZM15.75 14.995a.75.75 0 0 1 .75-.75h.005a.75.75 0 0 1 .75.75v.006a.75.75 0 0 1-.75.75H16.5a.75.75 0 0 1-.75-.75v-.006ZM13.498 12.743a.75.75 0 0 1 .75-.75h2.25a.75.75 0 1 1 0 1.5h-2.25a.75.75 0 0 1-.75-.75ZM6.748 14.993a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Z"
  }), React95.createElement("path", {
    fillRule: "evenodd",
    d: "M18 2.993a.75.75 0 0 0-1.5 0v1.5h-9V2.994a.75.75 0 1 0-1.5 0v1.497h-.752a3 3 0 0 0-3 3v11.252a3 3 0 0 0 3 3h13.5a3 3 0 0 0 3-3V7.492a3 3 0 0 0-3-3H18V2.993ZM3.748 18.743v-7.5a1.5 1.5 0 0 1 1.5-1.5h13.5a1.5 1.5 0 0 1 1.5 1.5v7.5a1.5 1.5 0 0 1-1.5 1.5h-13.5a1.5 1.5 0 0 1-1.5-1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef95 = React95.forwardRef(CalendarDateRangeIcon);
var CalendarDateRangeIcon_default = ForwardRef95;

// node_modules/@heroicons/react/24/solid/esm/CalendarDaysIcon.js
var React96 = __toESM(require_react(), 1);
function CalendarDaysIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React96.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React96.createElement("title", {
    id: titleId
  }, title) : null, React96.createElement("path", {
    d: "M12.75 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM7.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM8.25 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM9.75 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM10.5 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM12.75 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM14.25 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 17.25a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 15.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM15 12.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM16.5 13.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z"
  }), React96.createElement("path", {
    fillRule: "evenodd",
    d: "M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef96 = React96.forwardRef(CalendarDaysIcon);
var CalendarDaysIcon_default = ForwardRef96;

// node_modules/@heroicons/react/24/solid/esm/CalendarIcon.js
var React97 = __toESM(require_react(), 1);
function CalendarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React97.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React97.createElement("title", {
    id: titleId
  }, title) : null, React97.createElement("path", {
    fillRule: "evenodd",
    d: "M6.75 2.25A.75.75 0 0 1 7.5 3v1.5h9V3A.75.75 0 0 1 18 3v1.5h.75a3 3 0 0 1 3 3v11.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V7.5a3 3 0 0 1 3-3H6V3a.75.75 0 0 1 .75-.75Zm13.5 9a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef97 = React97.forwardRef(CalendarIcon);
var CalendarIcon_default = ForwardRef97;

// node_modules/@heroicons/react/24/solid/esm/CameraIcon.js
var React98 = __toESM(require_react(), 1);
function CameraIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React98.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React98.createElement("title", {
    id: titleId
  }, title) : null, React98.createElement("path", {
    d: "M12 9a3.75 3.75 0 1 0 0 7.5A3.75 3.75 0 0 0 12 9Z"
  }), React98.createElement("path", {
    fillRule: "evenodd",
    d: "M9.344 3.071a49.52 49.52 0 0 1 5.312 0c.967.052 1.83.585 2.332 1.39l.821 1.317c.24.383.645.643 1.11.71.386.054.77.113 1.152.177 1.432.239 2.429 1.493 2.429 2.909V18a3 3 0 0 1-3 3h-15a3 3 0 0 1-3-3V9.574c0-1.416.997-2.67 2.429-2.909.382-.064.766-.123 1.151-.178a1.56 1.56 0 0 0 1.11-.71l.822-1.315a2.942 2.942 0 0 1 2.332-1.39ZM6.75 12.75a5.25 5.25 0 1 1 10.5 0 5.25 5.25 0 0 1-10.5 0Zm12-1.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef98 = React98.forwardRef(CameraIcon);
var CameraIcon_default = ForwardRef98;

// node_modules/@heroicons/react/24/solid/esm/ChartBarSquareIcon.js
var React99 = __toESM(require_react(), 1);
function ChartBarSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React99.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React99.createElement("title", {
    id: titleId
  }, title) : null, React99.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm4.5 7.5a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0v-2.25a.75.75 0 0 1 .75-.75Zm3.75-1.5a.75.75 0 0 0-1.5 0v4.5a.75.75 0 0 0 1.5 0V12Zm2.25-3a.75.75 0 0 1 .75.75v6.75a.75.75 0 0 1-1.5 0V9.75A.75.75 0 0 1 13.5 9Zm3.75-1.5a.75.75 0 0 0-1.5 0v9a.75.75 0 0 0 1.5 0v-9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef99 = React99.forwardRef(ChartBarSquareIcon);
var ChartBarSquareIcon_default = ForwardRef99;

// node_modules/@heroicons/react/24/solid/esm/ChartBarIcon.js
var React100 = __toESM(require_react(), 1);
function ChartBarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React100.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React100.createElement("title", {
    id: titleId
  }, title) : null, React100.createElement("path", {
    d: "M18.375 2.25c-1.035 0-1.875.84-1.875 1.875v15.75c0 1.035.84 1.875 1.875 1.875h.75c1.035 0 1.875-.84 1.875-1.875V4.125c0-1.036-.84-1.875-1.875-1.875h-.75ZM9.75 8.625c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-.75a1.875 1.875 0 0 1-1.875-1.875V8.625ZM3 13.125c0-1.036.84-1.875 1.875-1.875h.75c1.036 0 1.875.84 1.875 1.875v6.75c0 1.035-.84 1.875-1.875 1.875h-.75A1.875 1.875 0 0 1 3 19.875v-6.75Z"
  }));
}
var ForwardRef100 = React100.forwardRef(ChartBarIcon);
var ChartBarIcon_default = ForwardRef100;

// node_modules/@heroicons/react/24/solid/esm/ChartPieIcon.js
var React101 = __toESM(require_react(), 1);
function ChartPieIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React101.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React101.createElement("title", {
    id: titleId
  }, title) : null, React101.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 13.5a8.25 8.25 0 0 1 8.25-********** 0 0 1 .75.75v6.75H18a.75.75 0 0 1 .75.75 8.25 8.25 0 0 1-16.5 0Z",
    clipRule: "evenodd"
  }), React101.createElement("path", {
    fillRule: "evenodd",
    d: "M12.75 3a.75.75 0 0 1 .75-.75 8.25 8.25 0 0 1 8.25 ********** 0 0 1-.75.75h-7.5a.75.75 0 0 1-.75-.75V3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef101 = React101.forwardRef(ChartPieIcon);
var ChartPieIcon_default = ForwardRef101;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleBottomCenterTextIcon.js
var React102 = __toESM(require_react(), 1);
function ChatBubbleBottomCenterTextIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React102.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React102.createElement("title", {
    id: titleId
  }, title) : null, React102.createElement("path", {
    fillRule: "evenodd",
    d: "M4.848 2.771A49.144 49.144 0 0 1 12 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 0 1-3.476.383.39.39 0 0 0-.297.17l-2.755 4.133a.75.75 0 0 1-1.248 0l-2.755-4.133a.39.39 0 0 0-.297-.17 48.9 48.9 0 0 1-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97ZM6.75 8.25a.75.75 0 0 1 .75-.75h9a.75.75 0 0 1 0 1.5h-9a.75.75 0 0 1-.75-.75Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef102 = React102.forwardRef(ChatBubbleBottomCenterTextIcon);
var ChatBubbleBottomCenterTextIcon_default = ForwardRef102;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleBottomCenterIcon.js
var React103 = __toESM(require_react(), 1);
function ChatBubbleBottomCenterIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React103.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React103.createElement("title", {
    id: titleId
  }, title) : null, React103.createElement("path", {
    fillRule: "evenodd",
    d: "M4.848 2.771A49.144 49.144 0 0 1 12 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97a48.901 48.901 0 0 1-3.476.383.39.39 0 0 0-.297.17l-2.755 4.133a.75.75 0 0 1-1.248 0l-2.755-4.133a.39.39 0 0 0-.297-.17 48.9 48.9 0 0 1-3.476-.384c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef103 = React103.forwardRef(ChatBubbleBottomCenterIcon);
var ChatBubbleBottomCenterIcon_default = ForwardRef103;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleLeftEllipsisIcon.js
var React104 = __toESM(require_react(), 1);
function ChatBubbleLeftEllipsisIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React104.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React104.createElement("title", {
    id: titleId
  }, title) : null, React104.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-2.429 0-4.817.178-7.152.521C2.87 3.061 1.5 4.795 1.5 6.741v6.018c0 1.946 1.37 3.68 3.348 3.97.877.129 1.761.234 2.652.316V21a.75.75 0 0 0 1.28.53l4.184-4.183a.39.39 0 0 1 .266-.112c2.006-.05 3.982-.22 5.922-.506 1.978-.29 3.348-2.023 3.348-3.97V6.741c0-1.947-1.37-3.68-3.348-3.97A49.145 49.145 0 0 0 12 2.25ZM8.25 8.625a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Zm2.625 1.125a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm4.875-1.125a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef104 = React104.forwardRef(ChatBubbleLeftEllipsisIcon);
var ChatBubbleLeftEllipsisIcon_default = ForwardRef104;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleLeftRightIcon.js
var React105 = __toESM(require_react(), 1);
function ChatBubbleLeftRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React105.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React105.createElement("title", {
    id: titleId
  }, title) : null, React105.createElement("path", {
    d: "M4.913 2.658c2.075-.27 4.19-.408 6.337-.408 2.147 0 4.262.139 6.337.408 1.922.25 3.291 1.861 3.405 3.727a4.403 4.403 0 0 0-1.032-.211 50.89 50.89 0 0 0-8.42 0c-2.358.196-4.04 2.19-4.04 4.434v4.286a4.47 4.47 0 0 0 2.433 3.984L7.28 21.53A.75.75 0 0 1 6 21v-4.03a48.527 48.527 0 0 1-1.087-.128C2.905 16.58 1.5 14.833 1.5 12.862V6.638c0-1.97 1.405-3.718 3.413-3.979Z"
  }), React105.createElement("path", {
    d: "M15.75 7.5c-1.376 0-2.739.057-4.086.169C10.124 7.797 9 9.103 9 10.609v4.285c0 1.507 1.128 2.814 2.67 2.94 1.243.102 2.5.157 3.768.165l2.782 2.781a.75.75 0 0 0 1.28-.53v-2.39l.33-.026c1.542-.125 2.67-1.433 2.67-2.94v-4.286c0-1.505-1.125-2.811-2.664-2.94A49.392 49.392 0 0 0 15.75 7.5Z"
  }));
}
var ForwardRef105 = React105.forwardRef(ChatBubbleLeftRightIcon);
var ChatBubbleLeftRightIcon_default = ForwardRef105;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleLeftIcon.js
var React106 = __toESM(require_react(), 1);
function ChatBubbleLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React106.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React106.createElement("title", {
    id: titleId
  }, title) : null, React106.createElement("path", {
    fillRule: "evenodd",
    d: "M4.848 2.771A49.144 49.144 0 0 1 12 2.25c2.43 0 4.817.178 7.152.52 1.978.292 3.348 2.024 3.348 3.97v6.02c0 1.946-1.37 3.678-3.348 3.97-1.94.284-3.916.455-5.922.505a.39.39 0 0 0-.266.112L8.78 21.53A.75.75 0 0 1 7.5 21v-3.955a48.842 48.842 0 0 1-2.652-.316c-1.978-.29-3.348-2.024-3.348-3.97V6.741c0-1.946 1.37-3.68 3.348-3.97Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef106 = React106.forwardRef(ChatBubbleLeftIcon);
var ChatBubbleLeftIcon_default = ForwardRef106;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleOvalLeftEllipsisIcon.js
var React107 = __toESM(require_react(), 1);
function ChatBubbleOvalLeftEllipsisIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React107.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React107.createElement("title", {
    id: titleId
  }, title) : null, React107.createElement("path", {
    fillRule: "evenodd",
    d: "M4.804 21.644A6.707 6.707 0 0 0 6 21.75a6.721 6.721 0 0 0 3.583-1.029c.774.182 1.584.279 2.417.279 5.322 0 9.75-3.97 9.75-9 0-5.03-4.428-9-9.75-9s-9.75 3.97-9.75 9c0 2.409 1.025 4.587 2.674 *************.277.428.254.543a3.73 3.73 0 0 1-.814 1.686.75.75 0 0 0 .44 1.223ZM8.25 10.875a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25ZM10.875 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm4.875-1.125a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef107 = React107.forwardRef(ChatBubbleOvalLeftEllipsisIcon);
var ChatBubbleOvalLeftEllipsisIcon_default = ForwardRef107;

// node_modules/@heroicons/react/24/solid/esm/ChatBubbleOvalLeftIcon.js
var React108 = __toESM(require_react(), 1);
function ChatBubbleOvalLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React108.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React108.createElement("title", {
    id: titleId
  }, title) : null, React108.createElement("path", {
    fillRule: "evenodd",
    d: "M5.337 21.718a6.707 6.707 0 0 1-.533-.074.75.75 0 0 1-.44-1.223 3.73 3.73 0 0 0 .814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 0 1-4.246.997Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef108 = React108.forwardRef(ChatBubbleOvalLeftIcon);
var ChatBubbleOvalLeftIcon_default = ForwardRef108;

// node_modules/@heroicons/react/24/solid/esm/CheckBadgeIcon.js
var React109 = __toESM(require_react(), 1);
function CheckBadgeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React109.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React109.createElement("title", {
    id: titleId
  }, title) : null, React109.createElement("path", {
    fillRule: "evenodd",
    d: "M8.603 3.799A4.49 4.49 0 0 1 12 2.25c1.357 0 2.573.6 3.397 1.549a4.49 4.49 0 0 1 3.498 1.307 4.491 4.491 0 0 1 1.307 3.497A4.49 4.49 0 0 1 21.75 12a4.49 4.49 0 0 1-1.549 3.397 4.491 4.491 0 0 1-1.307 3.497 4.491 4.491 0 0 1-3.497 1.307A4.49 4.49 0 0 1 12 21.75a4.49 4.49 0 0 1-3.397-1.549 4.49 4.49 0 0 1-3.498-1.306 4.491 4.491 0 0 1-1.307-3.498A4.49 4.49 0 0 1 2.25 12c0-1.357.6-2.573 1.549-3.397a4.49 4.49 0 0 1 1.307-3.497 4.49 4.49 0 0 1 3.497-1.307Zm7.007 6.387a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef109 = React109.forwardRef(CheckBadgeIcon);
var CheckBadgeIcon_default = ForwardRef109;

// node_modules/@heroicons/react/24/solid/esm/CheckCircleIcon.js
var React110 = __toESM(require_react(), 1);
function CheckCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React110.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React110.createElement("title", {
    id: titleId
  }, title) : null, React110.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef110 = React110.forwardRef(CheckCircleIcon);
var CheckCircleIcon_default = ForwardRef110;

// node_modules/@heroicons/react/24/solid/esm/CheckIcon.js
var React111 = __toESM(require_react(), 1);
function CheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React111.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React111.createElement("title", {
    id: titleId
  }, title) : null, React111.createElement("path", {
    fillRule: "evenodd",
    d: "M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef111 = React111.forwardRef(CheckIcon);
var CheckIcon_default = ForwardRef111;

// node_modules/@heroicons/react/24/solid/esm/ChevronDoubleDownIcon.js
var React112 = __toESM(require_react(), 1);
function ChevronDoubleDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React112.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React112.createElement("title", {
    id: titleId
  }, title) : null, React112.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 13.28a.75.75 0 0 0 1.06 0l7.5-7.5a.75.75 0 0 0-1.06-1.06L12 11.69 5.03 4.72a.75.75 0 0 0-1.06 1.06l7.5 7.5Z",
    clipRule: "evenodd"
  }), React112.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 19.28a.75.75 0 0 0 1.06 0l7.5-7.5a.75.75 0 1 0-1.06-1.06L12 17.69l-6.97-6.97a.75.75 0 0 0-1.06 1.06l7.5 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef112 = React112.forwardRef(ChevronDoubleDownIcon);
var ChevronDoubleDownIcon_default = ForwardRef112;

// node_modules/@heroicons/react/24/solid/esm/ChevronDoubleLeftIcon.js
var React113 = __toESM(require_react(), 1);
function ChevronDoubleLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React113.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React113.createElement("title", {
    id: titleId
  }, title) : null, React113.createElement("path", {
    fillRule: "evenodd",
    d: "M10.72 11.47a.75.75 0 0 0 0 1.06l7.5 7.5a.75.75 0 1 0 1.06-1.06L12.31 12l6.97-6.97a.75.75 0 0 0-1.06-1.06l-7.5 7.5Z",
    clipRule: "evenodd"
  }), React113.createElement("path", {
    fillRule: "evenodd",
    d: "M4.72 11.47a.75.75 0 0 0 0 1.06l7.5 7.5a.75.75 0 1 0 1.06-1.06L6.31 12l6.97-6.97a.75.75 0 0 0-1.06-1.06l-7.5 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef113 = React113.forwardRef(ChevronDoubleLeftIcon);
var ChevronDoubleLeftIcon_default = ForwardRef113;

// node_modules/@heroicons/react/24/solid/esm/ChevronDoubleRightIcon.js
var React114 = __toESM(require_react(), 1);
function ChevronDoubleRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React114.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React114.createElement("title", {
    id: titleId
  }, title) : null, React114.createElement("path", {
    fillRule: "evenodd",
    d: "M13.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L11.69 12 4.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",
    clipRule: "evenodd"
  }), React114.createElement("path", {
    fillRule: "evenodd",
    d: "M19.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 1 1-1.06-1.06L17.69 12l-6.97-6.97a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef114 = React114.forwardRef(ChevronDoubleRightIcon);
var ChevronDoubleRightIcon_default = ForwardRef114;

// node_modules/@heroicons/react/24/solid/esm/ChevronDoubleUpIcon.js
var React115 = __toESM(require_react(), 1);
function ChevronDoubleUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React115.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React115.createElement("title", {
    id: titleId
  }, title) : null, React115.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 10.72a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06L12 12.31l-6.97 6.97a.75.75 0 0 1-1.06-1.06l7.5-7.5Z",
    clipRule: "evenodd"
  }), React115.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 4.72a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06L12 6.31l-6.97 6.97a.75.75 0 0 1-1.06-1.06l7.5-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef115 = React115.forwardRef(ChevronDoubleUpIcon);
var ChevronDoubleUpIcon_default = ForwardRef115;

// node_modules/@heroicons/react/24/solid/esm/ChevronDownIcon.js
var React116 = __toESM(require_react(), 1);
function ChevronDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React116.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React116.createElement("title", {
    id: titleId
  }, title) : null, React116.createElement("path", {
    fillRule: "evenodd",
    d: "M12.53 16.28a.75.75 0 0 1-1.06 0l-7.5-7.5a.75.75 0 0 1 1.06-1.06L12 14.69l6.97-6.97a.75.75 0 1 1 1.06 1.06l-7.5 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef116 = React116.forwardRef(ChevronDownIcon);
var ChevronDownIcon_default = ForwardRef116;

// node_modules/@heroicons/react/24/solid/esm/ChevronLeftIcon.js
var React117 = __toESM(require_react(), 1);
function ChevronLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React117.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React117.createElement("title", {
    id: titleId
  }, title) : null, React117.createElement("path", {
    fillRule: "evenodd",
    d: "M7.72 12.53a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 1 1 1.06 1.06L9.31 12l6.97 6.97a.75.75 0 1 1-1.06 1.06l-7.5-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef117 = React117.forwardRef(ChevronLeftIcon);
var ChevronLeftIcon_default = ForwardRef117;

// node_modules/@heroicons/react/24/solid/esm/ChevronRightIcon.js
var React118 = __toESM(require_react(), 1);
function ChevronRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React118.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React118.createElement("title", {
    id: titleId
  }, title) : null, React118.createElement("path", {
    fillRule: "evenodd",
    d: "M16.28 11.47a.75.75 0 0 1 0 1.06l-7.5 7.5a.75.75 0 0 1-1.06-1.06L14.69 12 7.72 5.03a.75.75 0 0 1 1.06-1.06l7.5 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef118 = React118.forwardRef(ChevronRightIcon);
var ChevronRightIcon_default = ForwardRef118;

// node_modules/@heroicons/react/24/solid/esm/ChevronUpDownIcon.js
var React119 = __toESM(require_react(), 1);
function ChevronUpDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React119.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React119.createElement("title", {
    id: titleId
  }, title) : null, React119.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 4.72a.75.75 0 0 1 1.06 0l3.75 3.75a.75.75 0 0 1-1.06 1.06L12 6.31 8.78 9.53a.75.75 0 0 1-1.06-1.06l3.75-3.75Zm-3.75 9.75a.75.75 0 0 1 1.06 0L12 17.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-3.75 3.75a.75.75 0 0 1-1.06 0l-3.75-3.75a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef119 = React119.forwardRef(ChevronUpDownIcon);
var ChevronUpDownIcon_default = ForwardRef119;

// node_modules/@heroicons/react/24/solid/esm/ChevronUpIcon.js
var React120 = __toESM(require_react(), 1);
function ChevronUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React120.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React120.createElement("title", {
    id: titleId
  }, title) : null, React120.createElement("path", {
    fillRule: "evenodd",
    d: "M11.47 7.72a.75.75 0 0 1 1.06 0l7.5 7.5a.75.75 0 1 1-1.06 1.06L12 9.31l-6.97 6.97a.75.75 0 0 1-1.06-1.06l7.5-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef120 = React120.forwardRef(ChevronUpIcon);
var ChevronUpIcon_default = ForwardRef120;

// node_modules/@heroicons/react/24/solid/esm/CircleStackIcon.js
var React121 = __toESM(require_react(), 1);
function CircleStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React121.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React121.createElement("title", {
    id: titleId
  }, title) : null, React121.createElement("path", {
    d: "M21 6.375c0 2.692-4.03 4.875-9 4.875S3 9.067 3 6.375 7.03 1.5 12 1.5s9 2.183 9 4.875Z"
  }), React121.createElement("path", {
    d: "M12 12.75c2.685 0 5.19-.586 7.078-1.609a8.283 8.283 0 0 0 1.897-1.384c.***************.025.368C21 12.817 16.97 15 12 15s-9-2.183-9-4.875c0-.124.009-.247.025-.368a8.285 8.285 0 0 0 1.897 1.384C6.809 12.164 9.315 12.75 12 12.75Z"
  }), React121.createElement("path", {
    d: "M12 16.5c2.685 0 5.19-.586 7.078-1.609a8.282 8.282 0 0 0 1.897-1.384c.***************.025.368 0 2.692-4.03 4.875-9 4.875s-9-2.183-9-4.875c0-.124.009-.247.025-.368a8.284 8.284 0 0 0 1.897 1.384C6.809 15.914 9.315 16.5 12 16.5Z"
  }), React121.createElement("path", {
    d: "M12 20.25c2.685 0 5.19-.586 7.078-1.609a8.282 8.282 0 0 0 1.897-1.384c.***************.025.368 0 2.692-4.03 4.875-9 4.875s-9-2.183-9-4.875c0-.124.009-.247.025-.368a8.284 8.284 0 0 0 1.897 1.384C6.809 19.664 9.315 20.25 12 20.25Z"
  }));
}
var ForwardRef121 = React121.forwardRef(CircleStackIcon);
var CircleStackIcon_default = ForwardRef121;

// node_modules/@heroicons/react/24/solid/esm/ClipboardDocumentCheckIcon.js
var React122 = __toESM(require_react(), 1);
function ClipboardDocumentCheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React122.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React122.createElement("title", {
    id: titleId
  }, title) : null, React122.createElement("path", {
    fillRule: "evenodd",
    d: "M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6ZM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5Z",
    clipRule: "evenodd"
  }), React122.createElement("path", {
    fillRule: "evenodd",
    d: "M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375Zm9.586 4.594a.75.75 0 0 0-1.172-.938l-2.476 3.096-.908-.907a.75.75 0 0 0-1.06 1.06l1.5 1.5a.75.75 0 0 0 1.116-.062l3-3.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef122 = React122.forwardRef(ClipboardDocumentCheckIcon);
var ClipboardDocumentCheckIcon_default = ForwardRef122;

// node_modules/@heroicons/react/24/solid/esm/ClipboardDocumentListIcon.js
var React123 = __toESM(require_react(), 1);
function ClipboardDocumentListIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React123.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React123.createElement("title", {
    id: titleId
  }, title) : null, React123.createElement("path", {
    fillRule: "evenodd",
    d: "M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6ZM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5Z",
    clipRule: "evenodd"
  }), React123.createElement("path", {
    fillRule: "evenodd",
    d: "M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375ZM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef123 = React123.forwardRef(ClipboardDocumentListIcon);
var ClipboardDocumentListIcon_default = ForwardRef123;

// node_modules/@heroicons/react/24/solid/esm/ClipboardDocumentIcon.js
var React124 = __toESM(require_react(), 1);
function ClipboardDocumentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React124.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React124.createElement("title", {
    id: titleId
  }, title) : null, React124.createElement("path", {
    fillRule: "evenodd",
    d: "M17.663 3.118c.**************.673.05C19.876 3.298 21 4.604 21 6.109v9.642a3 3 0 0 1-3 3V16.5c0-5.922-4.576-10.775-10.384-11.217.324-1.132 1.3-2.01 2.548-2.114.224-.019.448-.036.673-.051A3 3 0 0 1 13.5 1.5H15a3 3 0 0 1 2.663 1.618ZM12 4.5A1.5 1.5 0 0 1 13.5 3H15a1.5 1.5 0 0 1 1.5 1.5H12Z",
    clipRule: "evenodd"
  }), React124.createElement("path", {
    d: "M3 8.625c0-1.036.84-1.875 1.875-1.875h.375A3.75 3.75 0 0 1 9 10.5v1.875c0 1.036.84 1.875 1.875 1.875h1.875A3.75 3.75 0 0 1 16.5 18v2.625c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625v-12Z"
  }), React124.createElement("path", {
    d: "M10.5 10.5a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963 5.23 5.23 0 0 0-3.434-1.279h-1.875a.375.375 0 0 1-.375-.375V10.5Z"
  }));
}
var ForwardRef124 = React124.forwardRef(ClipboardDocumentIcon);
var ClipboardDocumentIcon_default = ForwardRef124;

// node_modules/@heroicons/react/24/solid/esm/ClipboardIcon.js
var React125 = __toESM(require_react(), 1);
function ClipboardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React125.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React125.createElement("title", {
    id: titleId
  }, title) : null, React125.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3A1.501 1.501 0 0 0 9 4.5h6A1.5 1.5 0 0 0 13.5 3h-3Zm-2.693.178A3 3 0 0 1 10.5 1.5h3a3 3 0 0 1 2.694 1.678c.497.042.992.092 1.486.15 1.497.173 2.57 1.46 2.57 2.929V19.5a3 3 0 0 1-3 3H6.75a3 3 0 0 1-3-3V6.257c0-1.47 1.073-2.756 2.57-2.93.493-.057.989-.107 1.487-.15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef125 = React125.forwardRef(ClipboardIcon);
var ClipboardIcon_default = ForwardRef125;

// node_modules/@heroicons/react/24/solid/esm/ClockIcon.js
var React126 = __toESM(require_react(), 1);
function ClockIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React126.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React126.createElement("title", {
    id: titleId
  }, title) : null, React126.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-3.75V6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef126 = React126.forwardRef(ClockIcon);
var ClockIcon_default = ForwardRef126;

// node_modules/@heroicons/react/24/solid/esm/CloudArrowDownIcon.js
var React127 = __toESM(require_react(), 1);
function CloudArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React127.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React127.createElement("title", {
    id: titleId
  }, title) : null, React127.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.25 6a.75.75 0 0 0-1.5 0v4.94l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V9.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef127 = React127.forwardRef(CloudArrowDownIcon);
var CloudArrowDownIcon_default = ForwardRef127;

// node_modules/@heroicons/react/24/solid/esm/CloudArrowUpIcon.js
var React128 = __toESM(require_react(), 1);
function CloudArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React128.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React128.createElement("title", {
    id: titleId
  }, title) : null, React128.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3.75a6 6 0 0 0-5.98 6.496A5.25 5.25 0 0 0 6.75 20.25H18a4.5 4.5 0 0 0 2.206-8.423 3.75 3.75 0 0 0-4.133-4.303A6.001 6.001 0 0 0 10.5 3.75Zm2.03 5.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72v4.94a.75.75 0 0 0 1.5 0v-4.94l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef128 = React128.forwardRef(CloudArrowUpIcon);
var CloudArrowUpIcon_default = ForwardRef128;

// node_modules/@heroicons/react/24/solid/esm/CloudIcon.js
var React129 = __toESM(require_react(), 1);
function CloudIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React129.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React129.createElement("title", {
    id: titleId
  }, title) : null, React129.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 9.75a6 6 0 0 1 11.573-2.226 3.75 3.75 0 0 1 4.133 4.303A4.5 4.5 0 0 1 18 20.25H6.75a5.25 5.25 0 0 1-2.23-10.004 6.072 6.072 0 0 1-.02-.496Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef129 = React129.forwardRef(CloudIcon);
var CloudIcon_default = ForwardRef129;

// node_modules/@heroicons/react/24/solid/esm/CodeBracketSquareIcon.js
var React130 = __toESM(require_react(), 1);
function CodeBracketSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React130.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React130.createElement("title", {
    id: titleId
  }, title) : null, React130.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm14.25 6a.75.75 0 0 1-.22.53l-2.25 2.25a.75.75 0 1 1-1.06-1.06L15.44 12l-1.72-1.72a.75.75 0 1 1 1.06-1.06l2.25 2.25c.141.14.22.331.22.53Zm-10.28-.53a.75.75 0 0 0 0 1.06l2.25 2.25a.75.75 0 1 0 1.06-1.06L8.56 12l1.72-1.72a.75.75 0 1 0-1.06-1.06l-2.25 2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef130 = React130.forwardRef(CodeBracketSquareIcon);
var CodeBracketSquareIcon_default = ForwardRef130;

// node_modules/@heroicons/react/24/solid/esm/CodeBracketIcon.js
var React131 = __toESM(require_react(), 1);
function CodeBracketIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React131.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React131.createElement("title", {
    id: titleId
  }, title) : null, React131.createElement("path", {
    fillRule: "evenodd",
    d: "M14.447 3.026a.75.75 0 0 1 .527.921l-4.5 16.5a.75.75 0 0 1-1.448-.394l4.5-16.5a.75.75 0 0 1 .921-.527ZM16.72 6.22a.75.75 0 0 1 1.06 0l5.25 5.25a.75.75 0 0 1 0 1.06l-5.25 5.25a.75.75 0 1 1-1.06-1.06L21.44 12l-4.72-4.72a.75.75 0 0 1 0-1.06Zm-9.44 0a.75.75 0 0 1 0 1.06L2.56 12l4.72 4.72a.75.75 0 0 1-1.06 1.06L.97 12.53a.75.75 0 0 1 0-1.06l5.25-5.25a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef131 = React131.forwardRef(CodeBracketIcon);
var CodeBracketIcon_default = ForwardRef131;

// node_modules/@heroicons/react/24/solid/esm/Cog6ToothIcon.js
var React132 = __toESM(require_react(), 1);
function Cog6ToothIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React132.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React132.createElement("title", {
    id: titleId
  }, title) : null, React132.createElement("path", {
    fillRule: "evenodd",
    d: "M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 0 0-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 0 0-2.282.819l-.922 1.597a1.875 1.875 0 0 0 .432 2.385l.84.692c.**************.154.43a7.598 7.598 0 0 0 0 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 0 0-.432 2.385l.922 1.597a1.875 1.875 0 0 0 2.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 0 0 2.28-.819l.923-1.597a1.875 1.875 0 0 0-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 0 0 0-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 0 0-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 0 0-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 0 0-1.85-1.567h-1.843ZM12 15.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 0 0 0 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef132 = React132.forwardRef(Cog6ToothIcon);
var Cog6ToothIcon_default = ForwardRef132;

// node_modules/@heroicons/react/24/solid/esm/Cog8ToothIcon.js
var React133 = __toESM(require_react(), 1);
function Cog8ToothIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React133.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React133.createElement("title", {
    id: titleId
  }, title) : null, React133.createElement("path", {
    fillRule: "evenodd",
    d: "M11.828 2.25c-.916 0-1.699.663-1.85 1.567l-.091.549a.798.798 0 0 1-.517.608 7.45 7.45 0 0 0-.478.198.798.798 0 0 1-.796-.064l-.453-.324a1.875 1.875 0 0 0-2.416.2l-.243.243a1.875 1.875 0 0 0-.2 2.416l.324.453a.798.798 0 0 1 .064.796 7.448 7.448 0 0 0-.198.478.798.798 0 0 1-.608.517l-.55.092a1.875 1.875 0 0 0-1.566 1.849v.344c0 .916.663 1.699 1.567 1.85l.549.091c.281.047.508.25.608.517.06.162.127.321.198.478a.798.798 0 0 1-.064.796l-.324.453a1.875 1.875 0 0 0 .2 2.416l.243.243c.648.648 1.67.733 2.416.2l.453-.324a.798.798 0 0 1 .796-.064c.157.071.316.137.478.198.267.1.47.327.517.608l.092.55c.15.903.932 1.566 1.849 1.566h.344c.916 0 1.699-.663 1.85-1.567l.091-.549a.798.798 0 0 1 .517-.608 7.52 7.52 0 0 0 .478-.198.798.798 0 0 1 .796.064l.453.324a1.875 1.875 0 0 0 2.416-.2l.243-.243c.648-.648.733-1.67.2-2.416l-.324-.453a.798.798 0 0 1-.064-.796c.071-.157.137-.316.198-.478.1-.267.327-.47.608-.517l.55-.091a1.875 1.875 0 0 0 1.566-1.85v-.344c0-.916-.663-1.699-1.567-1.85l-.549-.091a.798.798 0 0 1-.608-.517 7.507 7.507 0 0 0-.198-.478.798.798 0 0 1 .064-.796l.324-.453a1.875 1.875 0 0 0-.2-2.416l-.243-.243a1.875 1.875 0 0 0-2.416-.2l-.453.324a.798.798 0 0 1-.796.064 7.462 7.462 0 0 0-.478-.198.798.798 0 0 1-.517-.608l-.091-.55a1.875 1.875 0 0 0-1.85-1.566h-.344ZM12 15.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 0 0 0 7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef133 = React133.forwardRef(Cog8ToothIcon);
var Cog8ToothIcon_default = ForwardRef133;

// node_modules/@heroicons/react/24/solid/esm/CogIcon.js
var React134 = __toESM(require_react(), 1);
function CogIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React134.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React134.createElement("title", {
    id: titleId
  }, title) : null, React134.createElement("path", {
    d: "M17.004 10.407c.138.435-.216.842-.672.842h-3.465a.75.75 0 0 1-.65-.375l-1.732-3c-.229-.396-.053-.907.393-1.004a5.252 5.252 0 0 1 6.126 3.537ZM8.12 8.464c.307-.338.838-.235 1.066.16l1.732 3a.75.75 0 0 1 0 .75l-1.732 3c-.229.397-.76.5-1.067.161A5.23 5.23 0 0 1 6.75 12a5.23 5.23 0 0 1 1.37-3.536ZM10.878 17.13c-.447-.098-.623-.608-.394-1.004l1.733-3.002a.75.75 0 0 1 .65-.375h3.465c.457 0 .81.407.672.842a5.252 5.252 0 0 1-6.126 3.539Z"
  }), React134.createElement("path", {
    fillRule: "evenodd",
    d: "M21 12.75a.75.75 0 1 0 0-1.5h-.783a8.22 8.22 0 0 0-.237-1.357l.734-.267a.75.75 0 1 0-.513-1.41l-.735.268a8.24 8.24 0 0 0-.689-1.192l.6-.503a.75.75 0 1 0-.964-1.149l-.6.504a8.3 8.3 0 0 0-1.054-.885l.391-.678a.75.75 0 1 0-1.299-.75l-.39.676a8.188 8.188 0 0 0-1.295-.47l.136-.77a.75.75 0 0 0-1.477-.26l-.136.77a8.36 8.36 0 0 0-1.377 0l-.136-.77a.75.75 0 1 0-1.477.26l.136.77c-.448.121-.88.28-1.294.47l-.39-.676a.75.75 0 0 0-1.3.75l.392.678a8.29 8.29 0 0 0-1.054.885l-.6-.504a.75.75 0 1 0-.965 1.149l.6.503a8.243 8.243 0 0 0-.689 1.192L3.8 8.216a.75.75 0 1 0-.513 1.41l.735.267a8.222 8.222 0 0 0-.238 1.356h-.783a.75.75 0 0 0 0 1.5h.783c.042.464.122.917.238 1.356l-.735.268a.75.75 0 0 0 .513 1.41l.735-.268c.197.417.428.816.69 1.191l-.6.504a.75.75 0 0 0 .963 1.15l.601-.505c.326.323.679.62 1.054.885l-.392.68a.75.75 0 0 0 1.3.75l.39-.679c.414.192.847.35 1.294.471l-.136.77a.75.75 0 0 0 1.477.261l.137-.772a8.332 8.332 0 0 0 1.376 0l.136.772a.75.75 0 1 0 1.477-.26l-.136-.771a8.19 8.19 0 0 0 1.294-.47l.391.677a.75.75 0 0 0 1.3-.75l-.393-.679a8.29 8.29 0 0 0 1.054-.885l.601.504a.75.75 0 0 0 .964-1.15l-.6-.503c.261-.375.492-.774.69-1.191l.735.267a.75.75 0 1 0 .512-1.41l-.734-.267c.115-.439.195-.892.237-1.356h.784Zm-2.657-3.06a6.744 6.744 0 0 0-1.19-2.053 6.784 6.784 0 0 0-1.82-1.51A6.705 6.705 0 0 0 12 5.25a6.8 6.8 0 0 0-1.225.11 6.7 6.7 0 0 0-2.15.793 6.784 6.784 0 0 0-2.952 3.489.76.76 0 0 1-.036.098A6.74 6.74 0 0 0 5.251 12a6.74 6.74 0 0 0 3.366 5.842l.009.005a6.704 6.704 0 0 0 2.18.798l.022.003a6.792 6.792 0 0 0 2.368-.004 6.704 6.704 0 0 0 2.205-.811 6.785 6.785 0 0 0 1.762-1.484l.009-.01.009-.01a6.743 6.743 0 0 0 1.18-2.066c.253-.707.39-1.469.39-2.263a6.74 6.74 0 0 0-.408-2.309Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef134 = React134.forwardRef(CogIcon);
var CogIcon_default = ForwardRef134;

// node_modules/@heroicons/react/24/solid/esm/CommandLineIcon.js
var React135 = __toESM(require_react(), 1);
function CommandLineIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React135.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React135.createElement("title", {
    id: titleId
  }, title) : null, React135.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 6a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V6Zm3.97.97a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 0 1-1.06-1.06l1.72-1.72-1.72-1.72a.75.75 0 0 1 0-1.06Zm4.28 4.28a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef135 = React135.forwardRef(CommandLineIcon);
var CommandLineIcon_default = ForwardRef135;

// node_modules/@heroicons/react/24/solid/esm/ComputerDesktopIcon.js
var React136 = __toESM(require_react(), 1);
function ComputerDesktopIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React136.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React136.createElement("title", {
    id: titleId
  }, title) : null, React136.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 5.25a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3V15a3 3 0 0 1-3 3h-3v.257c0 .597.237 1.17.659 1.591l.621.622a.75.75 0 0 1-.53 1.28h-9a.75.75 0 0 1-.53-1.28l.621-.622a2.25 2.25 0 0 0 .659-1.59V18h-3a3 3 0 0 1-3-3V5.25Zm1.5 0v7.5a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5v-7.5a1.5 1.5 0 0 0-1.5-1.5H5.25a1.5 1.5 0 0 0-1.5 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef136 = React136.forwardRef(ComputerDesktopIcon);
var ComputerDesktopIcon_default = ForwardRef136;

// node_modules/@heroicons/react/24/solid/esm/CpuChipIcon.js
var React137 = __toESM(require_react(), 1);
function CpuChipIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React137.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React137.createElement("title", {
    id: titleId
  }, title) : null, React137.createElement("path", {
    d: "M16.5 7.5h-9v9h9v-9Z"
  }), React137.createElement("path", {
    fillRule: "evenodd",
    d: "M8.25 2.25A.75.75 0 0 1 9 3v.75h2.25V3a.75.75 0 0 1 1.5 0v.75H15V3a.75.75 0 0 1 1.5 0v.75h.75a3 3 0 0 1 3 3v.75H21A.75.75 0 0 1 21 9h-.75v2.25H21a.75.75 0 0 1 0 1.5h-.75V15H21a.75.75 0 0 1 0 1.5h-.75v.75a3 3 0 0 1-3 3h-.75V21a.75.75 0 0 1-1.5 0v-.75h-2.25V21a.75.75 0 0 1-1.5 0v-.75H9V21a.75.75 0 0 1-1.5 0v-.75h-.75a3 3 0 0 1-3-3v-.75H3A.75.75 0 0 1 3 15h.75v-2.25H3a.75.75 0 0 1 0-1.5h.75V9H3a.75.75 0 0 1 0-1.5h.75v-.75a3 3 0 0 1 3-3h.75V3a.75.75 0 0 1 .75-.75ZM6 6.75A.75.75 0 0 1 6.75 6h10.5a.75.75 0 0 1 .75.75v10.5a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V6.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef137 = React137.forwardRef(CpuChipIcon);
var CpuChipIcon_default = ForwardRef137;

// node_modules/@heroicons/react/24/solid/esm/CreditCardIcon.js
var React138 = __toESM(require_react(), 1);
function CreditCardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React138.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React138.createElement("title", {
    id: titleId
  }, title) : null, React138.createElement("path", {
    d: "M4.5 3.75a3 3 0 0 0-3 3v.75h21v-.75a3 3 0 0 0-3-3h-15Z"
  }), React138.createElement("path", {
    fillRule: "evenodd",
    d: "M22.5 9.75h-21v7.5a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-7.5Zm-18 3.75a.75.75 0 0 1 .75-.75h6a.75.75 0 0 1 0 1.5h-6a.75.75 0 0 1-.75-.75Zm.75 2.25a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef138 = React138.forwardRef(CreditCardIcon);
var CreditCardIcon_default = ForwardRef138;

// node_modules/@heroicons/react/24/solid/esm/CubeTransparentIcon.js
var React139 = __toESM(require_react(), 1);
function CubeTransparentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React139.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React139.createElement("title", {
    id: titleId
  }, title) : null, React139.createElement("path", {
    fillRule: "evenodd",
    d: "M11.622 1.602a.75.75 0 0 1 .756 0l2.25 1.313a.75.75 0 0 1-.756 1.295L12 3.118 10.128 4.21a.75.75 0 1 1-.756-1.295l2.25-1.313ZM5.898 5.81a.75.75 0 0 1-.27 1.025l-1.14.665 1.14.665a.75.75 0 1 1-.756 1.295L3.75 8.806v.944a.75.75 0 0 1-1.5 0V7.5a.75.75 0 0 1 .372-.648l2.25-1.312a.75.75 0 0 1 1.026.27Zm12.204 0a.75.75 0 0 1 1.026-.27l2.25 1.312a.75.75 0 0 1 .372.648v2.25a.75.75 0 0 1-1.5 0v-.944l-1.122.654a.75.75 0 1 1-.756-1.295l1.14-.665-1.14-.665a.75.75 0 0 1-.27-1.025Zm-9 5.25a.75.75 0 0 1 1.026-.27L12 11.882l1.872-1.092a.75.75 0 1 1 .756 1.295l-1.878 1.096V15a.75.75 0 0 1-1.5 0v-1.82l-1.878-1.095a.75.75 0 0 1-.27-1.025ZM3 13.5a.75.75 0 0 1 .75.75v1.82l1.878 1.095a.75.75 0 1 1-.756 1.295l-2.25-1.312a.75.75 0 0 1-.372-.648v-2.25A.75.75 0 0 1 3 13.5Zm18 0a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.372.648l-2.25 1.312a.75.75 0 1 1-.756-1.295l1.878-1.096V14.25a.75.75 0 0 1 .75-.75Zm-9 5.25a.75.75 0 0 1 .75.75v.944l1.122-.654a.75.75 0 1 1 .756 1.295l-2.25 1.313a.75.75 0 0 1-.756 0l-2.25-1.313a.75.75 0 1 1 .756-1.295l1.122.654V19.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef139 = React139.forwardRef(CubeTransparentIcon);
var CubeTransparentIcon_default = ForwardRef139;

// node_modules/@heroicons/react/24/solid/esm/CubeIcon.js
var React140 = __toESM(require_react(), 1);
function CubeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React140.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React140.createElement("title", {
    id: titleId
  }, title) : null, React140.createElement("path", {
    d: "M12.378 1.602a.75.75 0 0 0-.756 0L3 6.632l9 5.25 9-5.25-8.622-5.03ZM21.75 7.93l-9 5.25v9l8.628-5.032a.75.75 0 0 0 .372-.648V7.93ZM11.25 22.18v-9l-9-5.25v8.57a.75.75 0 0 0 .372.648l8.628 5.033Z"
  }));
}
var ForwardRef140 = React140.forwardRef(CubeIcon);
var CubeIcon_default = ForwardRef140;

// node_modules/@heroicons/react/24/solid/esm/CurrencyBangladeshiIcon.js
var React141 = __toESM(require_react(), 1);
function CurrencyBangladeshiIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React141.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React141.createElement("title", {
    id: titleId
  }, title) : null, React141.createElement("path", {
    fillRule: "evenodd",
    d: "M12 21.75c5.385 0 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25 2.25 6.615 2.25 12s4.365 9.75 9.75 9.75ZM10.5 7.963a1.5 1.5 0 0 0-2.17-1.341l-.415.207a.75.75 0 0 0 .67 1.342L9 7.963V9.75h-.75a.75.75 0 1 0 0 1.5H9v4.688c0 .563.26 1.198.867 1.525A4.501 4.501 0 0 0 16.41 14.4c.199-.977-.636-1.649-1.415-1.649h-.745a.75.75 0 1 0 0 1.5h.656a3.002 3.002 0 0 1-4.327 1.893.113.113 0 0 1-.045-.051.336.336 0 0 1-.034-.154V11.25h5.25a.75.75 0 0 0 0-1.5H10.5V7.963Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef141 = React141.forwardRef(CurrencyBangladeshiIcon);
var CurrencyBangladeshiIcon_default = ForwardRef141;

// node_modules/@heroicons/react/24/solid/esm/CurrencyDollarIcon.js
var React142 = __toESM(require_react(), 1);
function CurrencyDollarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React142.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React142.createElement("title", {
    id: titleId
  }, title) : null, React142.createElement("path", {
    d: "M10.464 8.746c.227-.18.497-.311.786-.394v2.795a2.252 2.252 0 0 1-.786-.393c-.394-.313-.546-.681-.546-1.004 0-.323.152-.691.546-1.004ZM12.75 15.662v-2.824c.347.085.664.228.921.421.427.32.579.686.579.991 0 .305-.152.671-.579.991a2.534 2.534 0 0 1-.921.42Z"
  }), React142.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 6a.75.75 0 0 0-1.5 0v.816a3.836 3.836 0 0 0-1.72.756c-.712.566-1.112 1.35-1.112 2.178 0 .829.4 1.612 1.113 2.178.502.4 1.102.647 1.719.756v2.978a2.536 2.536 0 0 1-.921-.421l-.879-.66a.75.75 0 0 0-.9 1.2l.879.66c.533.4 1.169.645 1.821.75V18a.75.75 0 0 0 1.5 0v-.81a4.124 4.124 0 0 0 1.821-.749c.745-.559 1.179-1.344 1.179-2.191 0-.847-.434-1.632-1.179-2.191a4.122 4.122 0 0 0-1.821-.75V8.354c.29.082.559.213.786.393l.415.33a.75.75 0 0 0 .933-1.175l-.415-.33a3.836 3.836 0 0 0-1.719-.755V6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef142 = React142.forwardRef(CurrencyDollarIcon);
var CurrencyDollarIcon_default = ForwardRef142;

// node_modules/@heroicons/react/24/solid/esm/CurrencyEuroIcon.js
var React143 = __toESM(require_react(), 1);
function CurrencyEuroIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React143.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React143.createElement("title", {
    id: titleId
  }, title) : null, React143.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.902 7.098a3.75 3.75 0 0 1 3.903-.884.75.75 0 1 0 .498-1.415A5.25 5.25 0 0 0 8.005 9.75H7.5a.75.75 0 0 0 0 1.5h.054a5.281 5.281 0 0 0 0 1.5H7.5a.75.75 0 0 0 0 1.5h.505a5.25 5.25 0 0 0 6.494 2.701.75.75 0 1 0-.498-1.415 3.75 3.75 0 0 1-4.252-1.286h3.001a.75.75 0 0 0 0-1.5H9.075a3.77 3.77 0 0 1 0-1.5h3.675a.75.75 0 0 0 0-1.5h-3c.105-.14.221-.274.348-.402Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef143 = React143.forwardRef(CurrencyEuroIcon);
var CurrencyEuroIcon_default = ForwardRef143;

// node_modules/@heroicons/react/24/solid/esm/CurrencyPoundIcon.js
var React144 = __toESM(require_react(), 1);
function CurrencyPoundIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React144.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React144.createElement("title", {
    id: titleId
  }, title) : null, React144.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM9.763 9.51a2.25 2.25 0 0 1 3.828-1.351.75.75 0 0 0 1.06-1.06 3.75 3.75 0 0 0-6.38 2.252c-.033.307 0 .595.032.822l.154 1.077H8.25a.75.75 0 0 0 0 1.5h.421l.138.964a3.75 3.75 0 0 1-.358 2.208l-.122.242a.75.75 0 0 0 .908 1.047l1.539-.512a1.5 1.5 0 0 1 .948 0l.655.218a3 3 0 0 0 2.29-.163l.666-.333a.75.75 0 1 0-.67-1.342l-.667.333a1.5 1.5 0 0 1-1.145.082l-.654-.218a3 3 0 0 0-1.898 0l-.06.02a5.25 5.25 0 0 0 .053-1.794l-.108-.752H12a.75.75 0 0 0 0-1.5H9.972l-.184-1.29a1.863 1.863 0 0 1-.025-.45Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef144 = React144.forwardRef(CurrencyPoundIcon);
var CurrencyPoundIcon_default = ForwardRef144;

// node_modules/@heroicons/react/24/solid/esm/CurrencyRupeeIcon.js
var React145 = __toESM(require_react(), 1);
function CurrencyRupeeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React145.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React145.createElement("title", {
    id: titleId
  }, title) : null, React145.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM9 7.5A.75.75 0 0 0 9 9h1.5c.98 0 1.813.626 2.122 1.5H9A.75.75 0 0 0 9 12h3.622a2.251 2.251 0 0 1-2.122 1.5H9a.75.75 0 0 0-.53 1.28l3 3a.75.75 0 1 0 1.06-1.06L10.8 14.988A3.752 3.752 0 0 0 14.175 12H15a.75.75 0 0 0 0-1.5h-.825A3.733 3.733 0 0 0 13.5 9H15a.75.75 0 0 0 0-1.5H9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef145 = React145.forwardRef(CurrencyRupeeIcon);
var CurrencyRupeeIcon_default = ForwardRef145;

// node_modules/@heroicons/react/24/solid/esm/CurrencyYenIcon.js
var React146 = __toESM(require_react(), 1);
function CurrencyYenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React146.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React146.createElement("title", {
    id: titleId
  }, title) : null, React146.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM9.624 7.084a.75.75 0 0 0-1.248.832l2.223 3.334H9a.75.75 0 0 0 0 1.5h2.25v1.5H9a.75.75 0 0 0 0 1.5h2.25v1.5a.75.75 0 0 0 1.5 0v-1.5H15a.75.75 0 0 0 0-1.5h-2.25v-1.5H15a.75.75 0 0 0 0-1.5h-1.599l2.223-3.334a.75.75 0 1 0-1.248-.832L12 10.648 9.624 7.084Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef146 = React146.forwardRef(CurrencyYenIcon);
var CurrencyYenIcon_default = ForwardRef146;

// node_modules/@heroicons/react/24/solid/esm/CursorArrowRaysIcon.js
var React147 = __toESM(require_react(), 1);
function CursorArrowRaysIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React147.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React147.createElement("title", {
    id: titleId
  }, title) : null, React147.createElement("path", {
    fillRule: "evenodd",
    d: "M12 1.5a.75.75 0 0 1 .75.75V4.5a.75.75 0 0 1-1.5 0V2.25A.75.75 0 0 1 12 1.5ZM5.636 4.136a.75.75 0 0 1 1.06 0l1.592 1.591a.75.75 0 0 1-1.061 1.06l-1.591-1.59a.75.75 0 0 1 0-1.061Zm12.728 0a.75.75 0 0 1 0 1.06l-1.591 1.592a.75.75 0 0 1-1.06-1.061l1.59-1.591a.75.75 0 0 1 1.061 0Zm-6.816 4.496a.75.75 0 0 1 .82.311l5.228 7.917a.75.75 0 0 1-.777 1.148l-2.097-.43 1.045 3.9a.75.75 0 0 1-1.45.388l-1.044-3.899-1.601 1.42a.75.75 0 0 1-1.247-.606l.569-9.47a.75.75 0 0 1 .554-.68ZM3 10.5a.75.75 0 0 1 .75-.75H6a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 10.5Zm14.25 0a.75.75 0 0 1 .75-.75h2.25a.75.75 0 0 1 0 1.5H18a.75.75 0 0 1-.75-.75Zm-8.962 3.712a.75.75 0 0 1 0 1.061l-1.591 1.591a.75.75 0 1 1-1.061-1.06l1.591-1.592a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef147 = React147.forwardRef(CursorArrowRaysIcon);
var CursorArrowRaysIcon_default = ForwardRef147;

// node_modules/@heroicons/react/24/solid/esm/CursorArrowRippleIcon.js
var React148 = __toESM(require_react(), 1);
function CursorArrowRippleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React148.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React148.createElement("title", {
    id: titleId
  }, title) : null, React148.createElement("path", {
    fillRule: "evenodd",
    d: "M17.303 5.197A7.5 7.5 0 0 0 6.697 15.803a.75.75 0 0 1-1.061 1.061A9 9 0 1 1 21 10.5a.75.75 0 0 1-1.5 0c0-1.92-.732-3.839-2.197-5.303Zm-2.121 2.121a4.5 4.5 0 0 0-6.364 6.364.75.75 0 1 1-1.06 1.06A6 6 0 1 1 18 10.5a.75.75 0 0 1-1.5 0c0-1.153-.44-2.303-1.318-3.182Zm-3.634 1.314a.75.75 0 0 1 .82.311l5.228 7.917a.75.75 0 0 1-.777 1.148l-2.097-.43 1.045 3.9a.75.75 0 0 1-1.45.388l-1.044-3.899-1.601 1.42a.75.75 0 0 1-1.247-.606l.569-9.47a.75.75 0 0 1 .554-.68Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef148 = React148.forwardRef(CursorArrowRippleIcon);
var CursorArrowRippleIcon_default = ForwardRef148;

// node_modules/@heroicons/react/24/solid/esm/DevicePhoneMobileIcon.js
var React149 = __toESM(require_react(), 1);
function DevicePhoneMobileIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React149.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React149.createElement("title", {
    id: titleId
  }, title) : null, React149.createElement("path", {
    d: "M10.5 18.75a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z"
  }), React149.createElement("path", {
    fillRule: "evenodd",
    d: "M8.625.75A3.375 3.375 0 0 0 5.25 4.125v15.75a3.375 3.375 0 0 0 3.375 3.375h6.75a3.375 3.375 0 0 0 3.375-3.375V4.125A3.375 3.375 0 0 0 15.375.75h-6.75ZM7.5 4.125C7.5 3.504 8.004 3 8.625 3H9.75v.375c0 .621.504 1.125 1.125 1.125h2.25c.621 0 1.125-.504 1.125-1.125V3h1.125c.621 0 1.125.504 1.125 1.125v15.75c0 .621-.504 1.125-1.125 1.125h-6.75A1.125 1.125 0 0 1 7.5 19.875V4.125Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef149 = React149.forwardRef(DevicePhoneMobileIcon);
var DevicePhoneMobileIcon_default = ForwardRef149;

// node_modules/@heroicons/react/24/solid/esm/DeviceTabletIcon.js
var React150 = __toESM(require_react(), 1);
function DeviceTabletIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React150.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React150.createElement("title", {
    id: titleId
  }, title) : null, React150.createElement("path", {
    d: "M10.5 18a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z"
  }), React150.createElement("path", {
    fillRule: "evenodd",
    d: "M7.125 1.5A3.375 3.375 0 0 0 3.75 4.875v14.25A3.375 3.375 0 0 0 7.125 22.5h9.75a3.375 3.375 0 0 0 3.375-3.375V4.875A3.375 3.375 0 0 0 16.875 1.5h-9.75ZM6 4.875c0-.621.504-1.125 1.125-1.125h9.75c.621 0 1.125.504 1.125 1.125v14.25c0 .621-.504 1.125-1.125 1.125h-9.75A1.125 1.125 0 0 1 6 19.125V4.875Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef150 = React150.forwardRef(DeviceTabletIcon);
var DeviceTabletIcon_default = ForwardRef150;

// node_modules/@heroicons/react/24/solid/esm/DivideIcon.js
var React151 = __toESM(require_react(), 1);
function DivideIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React151.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React151.createElement("title", {
    id: titleId
  }, title) : null, React151.createElement("path", {
    fillRule: "evenodd",
    d: "M10.874 5.248a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm-7.125 6.75a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75Zm7.125 6.753a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef151 = React151.forwardRef(DivideIcon);
var DivideIcon_default = ForwardRef151;

// node_modules/@heroicons/react/24/solid/esm/DocumentArrowDownIcon.js
var React152 = __toESM(require_react(), 1);
function DocumentArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React152.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React152.createElement("title", {
    id: titleId
  }, title) : null, React152.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875Zm5.845 17.03a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V12a.75.75 0 0 0-1.5 0v4.19l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3Z",
    clipRule: "evenodd"
  }), React152.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef152 = React152.forwardRef(DocumentArrowDownIcon);
var DocumentArrowDownIcon_default = ForwardRef152;

// node_modules/@heroicons/react/24/solid/esm/DocumentArrowUpIcon.js
var React153 = __toESM(require_react(), 1);
function DocumentArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React153.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React153.createElement("title", {
    id: titleId
  }, title) : null, React153.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875Zm6.905 9.97a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l1.72-1.72V18a.75.75 0 0 0 1.5 0v-4.19l1.72 1.72a.75.75 0 1 0 1.06-1.06l-3-3Z",
    clipRule: "evenodd"
  }), React153.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef153 = React153.forwardRef(DocumentArrowUpIcon);
var DocumentArrowUpIcon_default = ForwardRef153;

// node_modules/@heroicons/react/24/solid/esm/DocumentChartBarIcon.js
var React154 = __toESM(require_react(), 1);
function DocumentChartBarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React154.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React154.createElement("title", {
    id: titleId
  }, title) : null, React154.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875ZM9.75 17.25a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-.75Zm2.25-3a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-1.5 0v-3a.75.75 0 0 1 .75-.75Zm3.75-1.5a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-5.25Z",
    clipRule: "evenodd"
  }), React154.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef154 = React154.forwardRef(DocumentChartBarIcon);
var DocumentChartBarIcon_default = ForwardRef154;

// node_modules/@heroicons/react/24/solid/esm/DocumentCheckIcon.js
var React155 = __toESM(require_react(), 1);
function DocumentCheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React155.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React155.createElement("title", {
    id: titleId
  }, title) : null, React155.createElement("path", {
    fillRule: "evenodd",
    d: "M9 1.5H5.625c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5Zm6.61 10.936a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 14.47a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",
    clipRule: "evenodd"
  }), React155.createElement("path", {
    d: "M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z"
  }));
}
var ForwardRef155 = React155.forwardRef(DocumentCheckIcon);
var DocumentCheckIcon_default = ForwardRef155;

// node_modules/@heroicons/react/24/solid/esm/DocumentCurrencyBangladeshiIcon.js
var React156 = __toESM(require_react(), 1);
function DocumentCurrencyBangladeshiIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React156.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React156.createElement("title", {
    id: titleId
  }, title) : null, React156.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3.375c0-1.036.84-1.875 1.875-1.875H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375Zm10.5 1.875a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Zm-3.75 5.56c0-1.336-1.616-2.005-2.56-1.06l-.22.22a.75.75 0 0 0 1.06 1.06l.22-.22v1.94h-.75a.75.75 0 0 0 0 1.5H9v3c0 .671.307 1.453 1.068 1.815a4.5 4.5 0 0 0 5.993-2.123c.233-.487.14-1-.136-1.37A1.459 1.459 0 0 0 14.757 15h-.507a.75.75 0 0 0 0 1.5h.349a2.999 2.999 0 0 1-3.887 1.21c-.091-.043-.212-.186-.212-.46v-3h5.25a.75.75 0 1 0 0-1.5H10.5v-1.94Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef156 = React156.forwardRef(DocumentCurrencyBangladeshiIcon);
var DocumentCurrencyBangladeshiIcon_default = ForwardRef156;

// node_modules/@heroicons/react/24/solid/esm/DocumentCurrencyDollarIcon.js
var React157 = __toESM(require_react(), 1);
function DocumentCurrencyDollarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React157.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React157.createElement("title", {
    id: titleId
  }, title) : null, React157.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3.375c0-1.036.84-1.875 1.875-1.875H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375Zm10.5 1.875a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25ZM12 10.5a.75.75 0 0 1 .75.75v.028a9.727 9.727 0 0 1 1.687.28.75.75 0 1 1-.374 1.452 8.207 8.207 0 0 0-1.313-.226v1.68l.969.332c.67.23 1.281.85 1.281 1.704 0 .158-.007.314-.02.468-.083.931-.83 1.582-1.669 1.695a9.776 9.776 0 0 1-.561.059v.028a.75.75 0 0 1-1.5 0v-.029a9.724 9.724 0 0 1-1.687-.278.75.75 0 0 1 .374-1.453c.425.11.864.186 1.313.226v-1.68l-.968-.332C9.612 14.974 9 14.354 9 13.5c0-.158.007-.314.02-.468.083-.931.831-1.582 1.67-1.694.185-.025.372-.045.56-.06v-.028a.75.75 0 0 1 .75-.75Zm-1.11 2.324c.119-.016.239-.03.36-.04v1.166l-.482-.165c-.208-.072-.268-.211-.268-.285 0-.113.005-.225.015-.336.013-.146.14-.309.374-.34Zm1.86 4.392V16.05l.482.165c.208.072.268.211.268.285 0 .113-.005.225-.015.336-.012.146-.14.309-.374.34-.12.016-.24.03-.361.04Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef157 = React157.forwardRef(DocumentCurrencyDollarIcon);
var DocumentCurrencyDollarIcon_default = ForwardRef157;

// node_modules/@heroicons/react/24/solid/esm/DocumentCurrencyEuroIcon.js
var React158 = __toESM(require_react(), 1);
function DocumentCurrencyEuroIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React158.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React158.createElement("title", {
    id: titleId
  }, title) : null, React158.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3.375c0-1.036.84-1.875 1.875-1.875H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375Zm7.464 9.442c.459-.573 1.019-.817 1.536-.817.517 0 1.077.244 1.536.817a.75.75 0 1 0 1.171-.937c-.713-.892-1.689-1.38-2.707-1.38-1.018 0-1.994.488-2.707 1.38a4.61 4.61 0 0 0-.705 1.245H8.25a.75.75 0 0 0 0 1.5h.763c-.017.25-.017.5 0 .75H8.25a.75.75 0 0 0 0 1.5h1.088c.17.449.406.87.705 1.245.713.892 1.689 1.38 2.707 1.38 1.018 0 1.994-.488 2.707-1.38a.75.75 0 0 0-1.171-.937c-.459.573-1.019.817-1.536.817-.517 0-1.077-.244-1.536-.817-.078-.098-.15-.2-.215-.308h1.751a.75.75 0 0 0 0-1.5h-2.232a3.965 3.965 0 0 1 0-.75h2.232a.75.75 0 0 0 0-1.5H11c.065-.107.136-.21.214-.308Z",
    clipRule: "evenodd"
  }), React158.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef158 = React158.forwardRef(DocumentCurrencyEuroIcon);
var DocumentCurrencyEuroIcon_default = ForwardRef158;

// node_modules/@heroicons/react/24/solid/esm/DocumentCurrencyPoundIcon.js
var React159 = __toESM(require_react(), 1);
function DocumentCurrencyPoundIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React159.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React159.createElement("title", {
    id: titleId
  }, title) : null, React159.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3.375c0-1.036.84-1.875 1.875-1.875H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375Zm10.5 1.875a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Zm-3.674 9.583a2.249 2.249 0 0 1 3.765-*********** 0 0 0 1.06-1.06A3.75 3.75 0 0 0 9.076 15H8.25a.75.75 0 0 0 0 1.5h1.156a3.75 3.75 0 0 1-.206 1.559l-.156.439a.75.75 0 0 0 1.042.923l.439-.22a2.113 2.113 0 0 1 1.613-.115 3.613 3.613 0 0 0 2.758-.196l.44-.22a.75.75 0 1 0-.671-1.341l-.44.22a2.113 2.113 0 0 1-1.613.114 3.612 3.612 0 0 0-1.745-.134c.048-.341.062-.686.042-1.029H12a.75.75 0 0 0 0-1.5h-1.379l-.045-.167Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef159 = React159.forwardRef(DocumentCurrencyPoundIcon);
var DocumentCurrencyPoundIcon_default = ForwardRef159;

// node_modules/@heroicons/react/24/solid/esm/DocumentCurrencyRupeeIcon.js
var React160 = __toESM(require_react(), 1);
function DocumentCurrencyRupeeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React160.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React160.createElement("title", {
    id: titleId
  }, title) : null, React160.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3.375c0-1.036.84-1.875 1.875-1.875H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375Zm10.5 1.875a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Zm-4.5 5.25a.75.75 0 0 0 0 1.5h.375c.769 0 1.43.463 1.719 1.125H9.75a.75.75 0 0 0 0 1.5h2.094a1.875 1.875 0 0 1-1.719 1.125H9.75a.75.75 0 0 0-.53 1.28l2.25 2.25a.75.75 0 0 0 1.06-1.06l-1.193-1.194a3.382 3.382 0 0 0 2.08-2.401h.833a.75.75 0 0 0 0-1.5h-.834A3.357 3.357 0 0 0 12.932 12h1.318a.75.75 0 0 0 0-1.5H10.5c-.04 0-.08.003-.12.01a3.425 3.425 0 0 0-.255-.01H9.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef160 = React160.forwardRef(DocumentCurrencyRupeeIcon);
var DocumentCurrencyRupeeIcon_default = ForwardRef160;

// node_modules/@heroicons/react/24/solid/esm/DocumentCurrencyYenIcon.js
var React161 = __toESM(require_react(), 1);
function DocumentCurrencyYenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React161.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React161.createElement("title", {
    id: titleId
  }, title) : null, React161.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3.375c0-1.036.84-1.875 1.875-1.875H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375Zm10.5 1.875a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Zm-3.9 5.55a.75.75 0 0 0-1.2.9l1.912 2.55H9.75a.75.75 0 0 0 0 1.5h1.5v.75h-1.5a.75.75 0 0 0 0 1.5h1.5v.75a.75.75 0 1 0 1.5 0V18h1.5a.75.75 0 1 0 0-1.5h-1.5v-.75h1.5a.75.75 0 1 0 0-1.5h-1.313l1.913-2.55a.75.75 0 1 0-1.2-.9L12 13l-1.65-2.2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef161 = React161.forwardRef(DocumentCurrencyYenIcon);
var DocumentCurrencyYenIcon_default = ForwardRef161;

// node_modules/@heroicons/react/24/solid/esm/DocumentDuplicateIcon.js
var React162 = __toESM(require_react(), 1);
function DocumentDuplicateIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React162.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React162.createElement("title", {
    id: titleId
  }, title) : null, React162.createElement("path", {
    d: "M7.5 3.375c0-1.036.84-1.875 1.875-1.875h.375a3.75 3.75 0 0 1 3.75 3.75v1.875C13.5 8.161 14.34 9 15.375 9h1.875A3.75 3.75 0 0 1 21 12.75v3.375C21 17.16 20.16 18 19.125 18h-9.75A1.875 1.875 0 0 1 7.5 16.125V3.375Z"
  }), React162.createElement("path", {
    d: "M15 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 17.25 7.5h-1.875A.375.375 0 0 1 15 7.125V5.25ZM4.875 6H6v10.125A3.375 3.375 0 0 0 9.375 19.5H16.5v1.125c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V7.875C3 6.839 3.84 6 4.875 6Z"
  }));
}
var ForwardRef162 = React162.forwardRef(DocumentDuplicateIcon);
var DocumentDuplicateIcon_default = ForwardRef162;

// node_modules/@heroicons/react/24/solid/esm/DocumentMagnifyingGlassIcon.js
var React163 = __toESM(require_react(), 1);
function DocumentMagnifyingGlassIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React163.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React163.createElement("title", {
    id: titleId
  }, title) : null, React163.createElement("path", {
    d: "M11.625 16.5a1.875 1.875 0 1 0 0-3.75 1.875 1.875 0 0 0 0 3.75Z"
  }), React163.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875Zm6 16.5c.66 0 1.277-.19 1.797-.518l1.048 1.048a.75.75 0 0 0 1.06-1.06l-1.047-1.048A3.375 3.375 0 1 0 11.625 18Z",
    clipRule: "evenodd"
  }), React163.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef163 = React163.forwardRef(DocumentMagnifyingGlassIcon);
var DocumentMagnifyingGlassIcon_default = ForwardRef163;

// node_modules/@heroicons/react/24/solid/esm/DocumentMinusIcon.js
var React164 = __toESM(require_react(), 1);
function DocumentMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React164.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React164.createElement("title", {
    id: titleId
  }, title) : null, React164.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875ZM9.75 14.25a.75.75 0 0 0 0 1.5H15a.75.75 0 0 0 0-1.5H9.75Z",
    clipRule: "evenodd"
  }), React164.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef164 = React164.forwardRef(DocumentMinusIcon);
var DocumentMinusIcon_default = ForwardRef164;

// node_modules/@heroicons/react/24/solid/esm/DocumentPlusIcon.js
var React165 = __toESM(require_react(), 1);
function DocumentPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React165.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React165.createElement("title", {
    id: titleId
  }, title) : null, React165.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5H9a3.75 3.75 0 0 1 3.75 3.75v1.875c0 1.036.84 1.875 1.875 1.875H16.5a3.75 3.75 0 0 1 3.75 3.75v7.875c0 1.035-.84 1.875-1.875 1.875H5.625a1.875 1.875 0 0 1-1.875-1.875V3.375c0-1.036.84-1.875 1.875-1.875ZM12.75 12a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V18a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V12Z",
    clipRule: "evenodd"
  }), React165.createElement("path", {
    d: "M14.25 5.25a5.23 5.23 0 0 0-1.279-3.434 9.768 9.768 0 0 1 6.963 6.963A5.23 5.23 0 0 0 16.5 7.5h-1.875a.375.375 0 0 1-.375-.375V5.25Z"
  }));
}
var ForwardRef165 = React165.forwardRef(DocumentPlusIcon);
var DocumentPlusIcon_default = ForwardRef165;

// node_modules/@heroicons/react/24/solid/esm/DocumentTextIcon.js
var React166 = __toESM(require_react(), 1);
function DocumentTextIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React166.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React166.createElement("title", {
    id: titleId
  }, title) : null, React166.createElement("path", {
    fillRule: "evenodd",
    d: "M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625ZM7.5 15a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 7.5 15Zm.75 2.25a.75.75 0 0 0 0 1.5H12a.75.75 0 0 0 0-1.5H8.25Z",
    clipRule: "evenodd"
  }), React166.createElement("path", {
    d: "M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z"
  }));
}
var ForwardRef166 = React166.forwardRef(DocumentTextIcon);
var DocumentTextIcon_default = ForwardRef166;

// node_modules/@heroicons/react/24/solid/esm/DocumentIcon.js
var React167 = __toESM(require_react(), 1);
function DocumentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React167.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React167.createElement("title", {
    id: titleId
  }, title) : null, React167.createElement("path", {
    d: "M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z"
  }), React167.createElement("path", {
    d: "M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z"
  }));
}
var ForwardRef167 = React167.forwardRef(DocumentIcon);
var DocumentIcon_default = ForwardRef167;

// node_modules/@heroicons/react/24/solid/esm/EllipsisHorizontalCircleIcon.js
var React168 = __toESM(require_react(), 1);
function EllipsisHorizontalCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React168.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React168.createElement("title", {
    id: titleId
  }, title) : null, React168.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm0 8.625a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25ZM15.375 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0ZM7.5 10.875a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef168 = React168.forwardRef(EllipsisHorizontalCircleIcon);
var EllipsisHorizontalCircleIcon_default = ForwardRef168;

// node_modules/@heroicons/react/24/solid/esm/EllipsisHorizontalIcon.js
var React169 = __toESM(require_react(), 1);
function EllipsisHorizontalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React169.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React169.createElement("title", {
    id: titleId
  }, title) : null, React169.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 12a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm6 0a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm6 0a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef169 = React169.forwardRef(EllipsisHorizontalIcon);
var EllipsisHorizontalIcon_default = ForwardRef169;

// node_modules/@heroicons/react/24/solid/esm/EllipsisVerticalIcon.js
var React170 = __toESM(require_react(), 1);
function EllipsisVerticalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React170.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React170.createElement("title", {
    id: titleId
  }, title) : null, React170.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Zm0 6a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef170 = React170.forwardRef(EllipsisVerticalIcon);
var EllipsisVerticalIcon_default = ForwardRef170;

// node_modules/@heroicons/react/24/solid/esm/EnvelopeOpenIcon.js
var React171 = __toESM(require_react(), 1);
function EnvelopeOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React171.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React171.createElement("title", {
    id: titleId
  }, title) : null, React171.createElement("path", {
    d: "M19.5 22.5a3 3 0 0 0 3-3v-8.174l-6.879 4.022 3.485 1.876a.75.75 0 1 1-.712 1.321l-5.683-3.06a1.5 1.5 0 0 0-1.422 0l-5.683 3.06a.75.75 0 0 1-.712-1.32l3.485-1.877L1.5 11.326V19.5a3 3 0 0 0 3 3h15Z"
  }), React171.createElement("path", {
    d: "M1.5 9.589v-.745a3 3 0 0 1 1.578-2.642l7.5-4.038a3 3 0 0 1 2.844 0l7.5 4.038A3 3 0 0 1 22.5 8.844v.745l-8.426 4.926-.652-.351a3 3 0 0 0-2.844 0l-.652.351L1.5 9.589Z"
  }));
}
var ForwardRef171 = React171.forwardRef(EnvelopeOpenIcon);
var EnvelopeOpenIcon_default = ForwardRef171;

// node_modules/@heroicons/react/24/solid/esm/EnvelopeIcon.js
var React172 = __toESM(require_react(), 1);
function EnvelopeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React172.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React172.createElement("title", {
    id: titleId
  }, title) : null, React172.createElement("path", {
    d: "M1.5 8.67v8.58a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V8.67l-8.928 5.493a3 3 0 0 1-3.144 0L1.5 8.67Z"
  }), React172.createElement("path", {
    d: "M22.5 6.908V6.75a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3v.158l9.714 5.978a1.5 1.5 0 0 0 1.572 0L22.5 6.908Z"
  }));
}
var ForwardRef172 = React172.forwardRef(EnvelopeIcon);
var EnvelopeIcon_default = ForwardRef172;

// node_modules/@heroicons/react/24/solid/esm/EqualsIcon.js
var React173 = __toESM(require_react(), 1);
function EqualsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React173.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React173.createElement("title", {
    id: titleId
  }, title) : null, React173.createElement("path", {
    fillRule: "evenodd",
    d: "M3.748 8.248a.75.75 0 0 1 .75-.75h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75ZM3.748 15.75a.75.75 0 0 1 .75-.751h15a.75.75 0 0 1 0 1.5h-15a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef173 = React173.forwardRef(EqualsIcon);
var EqualsIcon_default = ForwardRef173;

// node_modules/@heroicons/react/24/solid/esm/ExclamationCircleIcon.js
var React174 = __toESM(require_react(), 1);
function ExclamationCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React174.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React174.createElement("title", {
    id: titleId
  }, title) : null, React174.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef174 = React174.forwardRef(ExclamationCircleIcon);
var ExclamationCircleIcon_default = ForwardRef174;

// node_modules/@heroicons/react/24/solid/esm/ExclamationTriangleIcon.js
var React175 = __toESM(require_react(), 1);
function ExclamationTriangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React175.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React175.createElement("title", {
    id: titleId
  }, title) : null, React175.createElement("path", {
    fillRule: "evenodd",
    d: "M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75Zm0 8.25a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef175 = React175.forwardRef(ExclamationTriangleIcon);
var ExclamationTriangleIcon_default = ForwardRef175;

// node_modules/@heroicons/react/24/solid/esm/EyeDropperIcon.js
var React176 = __toESM(require_react(), 1);
function EyeDropperIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React176.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React176.createElement("title", {
    id: titleId
  }, title) : null, React176.createElement("path", {
    fillRule: "evenodd",
    d: "M16.098 2.598a3.75 3.75 0 1 1 3.622 6.275l-1.72.46V12a.75.75 0 0 1-.22.53l-.75.75a.75.75 0 0 1-1.06 0l-.97-.97-7.94 7.94a2.56 2.56 0 0 1-1.81.75 1.06 1.06 0 0 0-.75.31l-.97.97a.75.75 0 0 1-1.06 0l-.75-.75a.75.75 0 0 1 0-1.06l.97-.97a1.06 1.06 0 0 0 .31-.75c0-.68.27-1.33.75-1.81L11.69 9l-.97-.97a.75.75 0 0 1 0-1.06l.75-.75A.75.75 0 0 1 12 6h2.666l.461-1.72c.165-.617.49-1.2.971-1.682Zm-3.348 7.463L4.81 18a1.06 1.06 0 0 0-.31.75c0 .318-.06.63-.172.922a2.56 2.56 0 0 1 .922-.172c.281 0 .551-.112.75-.31l7.94-7.94-1.19-1.19Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef176 = React176.forwardRef(EyeDropperIcon);
var EyeDropperIcon_default = ForwardRef176;

// node_modules/@heroicons/react/24/solid/esm/EyeSlashIcon.js
var React177 = __toESM(require_react(), 1);
function EyeSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React177.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React177.createElement("title", {
    id: titleId
  }, title) : null, React177.createElement("path", {
    d: "M3.53 2.47a.75.75 0 0 0-1.06 1.06l18 18a.75.75 0 1 0 1.06-1.06l-18-18ZM22.676 12.553a11.249 11.249 0 0 1-2.631 4.31l-3.099-3.099a5.25 5.25 0 0 0-6.71-6.71L7.759 4.577a11.217 11.217 0 0 1 4.242-.827c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113Z"
  }), React177.createElement("path", {
    d: "M15.75 12c0 .18-.013.357-.037.53l-4.244-4.243A3.75 3.75 0 0 1 15.75 12ZM12.53 15.713l-4.243-4.244a3.75 3.75 0 0 0 4.244 4.243Z"
  }), React177.createElement("path", {
    d: "M6.75 12c0-.619.107-1.213.304-1.764l-3.1-3.1a11.25 11.25 0 0 0-2.63 4.31c-.12.362-.12.752 0 1.114 1.489 4.467 5.704 7.69 10.675 7.69 1.5 0 2.933-.294 4.242-.827l-2.477-2.477A5.25 5.25 0 0 1 6.75 12Z"
  }));
}
var ForwardRef177 = React177.forwardRef(EyeSlashIcon);
var EyeSlashIcon_default = ForwardRef177;

// node_modules/@heroicons/react/24/solid/esm/EyeIcon.js
var React178 = __toESM(require_react(), 1);
function EyeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React178.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React178.createElement("title", {
    id: titleId
  }, title) : null, React178.createElement("path", {
    d: "M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
  }), React178.createElement("path", {
    fillRule: "evenodd",
    d: "M1.323 11.447C2.811 6.976 7.028 3.75 12.001 3.75c4.97 0 9.185 3.223 10.675 7.69.12.362.12.752 0 1.113-1.487 4.471-5.705 7.697-10.677 7.697-4.97 0-9.186-3.223-10.675-7.69a1.762 1.762 0 0 1 0-1.113ZM17.25 12a5.25 5.25 0 1 1-10.5 0 5.25 5.25 0 0 1 10.5 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef178 = React178.forwardRef(EyeIcon);
var EyeIcon_default = ForwardRef178;

// node_modules/@heroicons/react/24/solid/esm/FaceFrownIcon.js
var React179 = __toESM(require_react(), 1);
function FaceFrownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React179.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React179.createElement("title", {
    id: titleId
  }, title) : null, React179.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-2.625 6c-.54 0-.828.419-.936.634a1.96 1.96 0 0 0-.189.866c0 .298.059.605.189.866.108.215.395.634.936.634.54 0 .828-.419.936-.634.13-.26.189-.568.189-.866 0-.298-.059-.605-.189-.866-.108-.215-.395-.634-.936-.634Zm4.314.634c.108-.215.395-.634.936-.634.54 0 .828.419.936.634.13.26.189.568.189.866 0 .298-.059.605-.189.866-.108.215-.395.634-.936.634-.54 0-.828-.419-.936-.634a1.96 1.96 0 0 1-.189-.866c0-.298.059-.605.189-.866Zm-4.34 7.964a.75.75 0 0 1-1.061-1.06 5.236 5.236 0 0 1 3.73-1.538 5.236 5.236 0 0 1 3.695 1.538.75.75 0 1 1-1.061 1.06 3.736 3.736 0 0 0-2.639-1.098 3.736 3.736 0 0 0-2.664 1.098Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef179 = React179.forwardRef(FaceFrownIcon);
var FaceFrownIcon_default = ForwardRef179;

// node_modules/@heroicons/react/24/solid/esm/FaceSmileIcon.js
var React180 = __toESM(require_react(), 1);
function FaceSmileIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React180.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React180.createElement("title", {
    id: titleId
  }, title) : null, React180.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-2.625 6c-.54 0-.828.419-.936.634a1.96 1.96 0 0 0-.189.866c0 .298.059.605.189.866.108.215.395.634.936.634.54 0 .828-.419.936-.634.13-.26.189-.568.189-.866 0-.298-.059-.605-.189-.866-.108-.215-.395-.634-.936-.634Zm4.314.634c.108-.215.395-.634.936-.634.54 0 .828.419.936.634.13.26.189.568.189.866 0 .298-.059.605-.189.866-.108.215-.395.634-.936.634-.54 0-.828-.419-.936-.634a1.96 1.96 0 0 1-.189-.866c0-.298.059-.605.189-.866Zm2.023 6.828a.75.75 0 1 0-1.06-1.06 3.75 3.75 0 0 1-5.304 0 .75.75 0 0 0-1.06 1.06 5.25 5.25 0 0 0 7.424 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef180 = React180.forwardRef(FaceSmileIcon);
var FaceSmileIcon_default = ForwardRef180;

// node_modules/@heroicons/react/24/solid/esm/FilmIcon.js
var React181 = __toESM(require_react(), 1);
function FilmIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React181.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React181.createElement("title", {
    id: titleId
  }, title) : null, React181.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 18.375V5.625Zm1.5 0v1.5c0 .207.168.375.375.375h1.5a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-1.5A.375.375 0 0 0 3 5.625Zm16.125-.375a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h1.5A.375.375 0 0 0 21 7.125v-1.5a.375.375 0 0 0-.375-.375h-1.5ZM21 9.375A.375.375 0 0 0 20.625 9h-1.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 0 0 .375-.375v-1.5Zm0 3.75a.375.375 0 0 0-.375-.375h-1.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 0 0 .375-.375v-1.5Zm0 3.75a.375.375 0 0 0-.375-.375h-1.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h1.5a.375.375 0 0 0 .375-.375v-1.5ZM4.875 18.75a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-1.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h1.5ZM3.375 15h1.5a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-1.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375Zm0-3.75h1.5a.375.375 0 0 0 .375-.375v-1.5A.375.375 0 0 0 4.875 9h-1.5A.375.375 0 0 0 3 9.375v1.5c0 .207.168.375.375.375Zm4.125 0a.75.75 0 0 0 0 1.5h9a.75.75 0 0 0 0-1.5h-9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef181 = React181.forwardRef(FilmIcon);
var FilmIcon_default = ForwardRef181;

// node_modules/@heroicons/react/24/solid/esm/FingerPrintIcon.js
var React182 = __toESM(require_react(), 1);
function FingerPrintIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React182.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React182.createElement("title", {
    id: titleId
  }, title) : null, React182.createElement("path", {
    fillRule: "evenodd",
    d: "M12 3.75a6.715 6.715 0 0 0-3.722 *********** 0 1 1-.828-1.25 8.25 8.25 0 0 1 12.8 6.883c0 3.014-.574 5.897-1.62 8.543a.75.75 0 0 1-1.395-.551A21.69 21.69 0 0 0 18.75 10.5 6.75 6.75 0 0 0 12 3.75ZM6.157 5.739a.75.75 0 0 1 .21 1.04A6.715 6.715 0 0 0 5.25 10.5c0 1.613-.463 3.12-1.265 4.393a.75.75 0 0 1-1.27-.8A6.715 6.715 0 0 0 3.75 10.5c0-1.68.503-3.246 1.367-4.55a.75.75 0 0 1 1.04-.211ZM12 7.5a3 3 0 0 0-3 3c0 3.1-1.176 5.927-3.105 8.056a.75.75 0 1 1-1.112-1.008A10.459 10.459 0 0 0 7.5 10.5a4.5 4.5 0 1 1 9 0c0 .547-.022 1.09-.067 1.626a.75.75 0 0 1-1.495-.123c.041-.495.062-.996.062-1.503a3 3 0 0 0-3-3Zm0 2.25a.75.75 0 0 1 .75.75c0 3.908-1.424 7.485-3.781 10.238a.75.75 0 0 1-1.14-.975A14.19 14.19 0 0 0 11.25 10.5a.75.75 0 0 1 .75-.75Zm3.239 5.183a.75.75 0 0 1 .515.927 19.417 19.417 0 0 1-2.585 5.544.75.75 0 0 1-1.243-.84 17.915 17.915 0 0 0 2.386-*********** 0 0 1 .927-.515Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef182 = React182.forwardRef(FingerPrintIcon);
var FingerPrintIcon_default = ForwardRef182;

// node_modules/@heroicons/react/24/solid/esm/FireIcon.js
var React183 = __toESM(require_react(), 1);
function FireIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React183.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React183.createElement("title", {
    id: titleId
  }, title) : null, React183.createElement("path", {
    fillRule: "evenodd",
    d: "M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef183 = React183.forwardRef(FireIcon);
var FireIcon_default = ForwardRef183;

// node_modules/@heroicons/react/24/solid/esm/FlagIcon.js
var React184 = __toESM(require_react(), 1);
function FlagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React184.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React184.createElement("title", {
    id: titleId
  }, title) : null, React184.createElement("path", {
    fillRule: "evenodd",
    d: "M3 2.25a.75.75 0 0 1 .75.75v.54l1.838-.46a9.75 9.75 0 0 1 6.725.738l.108.054A8.25 8.25 0 0 0 18 4.524l3.11-.732a.75.75 0 0 1 .917.81 47.784 47.784 0 0 0 .005 10.337.75.75 0 0 1-.574.812l-3.114.733a9.75 9.75 0 0 1-6.594-.77l-.108-.054a8.25 8.25 0 0 0-5.69-.625l-2.202.55V21a.75.75 0 0 1-1.5 0V3A.75.75 0 0 1 3 2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef184 = React184.forwardRef(FlagIcon);
var FlagIcon_default = ForwardRef184;

// node_modules/@heroicons/react/24/solid/esm/FolderArrowDownIcon.js
var React185 = __toESM(require_react(), 1);
function FolderArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React185.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React185.createElement("title", {
    id: titleId
  }, title) : null, React185.createElement("path", {
    fillRule: "evenodd",
    d: "M19.5 21a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-5.379a.75.75 0 0 1-.53-.22L11.47 3.66A2.25 2.25 0 0 0 9.879 3H4.5a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h15Zm-6.75-10.5a.75.75 0 0 0-1.5 0v4.19l-1.72-1.72a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72V10.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef185 = React185.forwardRef(FolderArrowDownIcon);
var FolderArrowDownIcon_default = ForwardRef185;

// node_modules/@heroicons/react/24/solid/esm/FolderMinusIcon.js
var React186 = __toESM(require_react(), 1);
function FolderMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React186.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React186.createElement("title", {
    id: titleId
  }, title) : null, React186.createElement("path", {
    fillRule: "evenodd",
    d: "M19.5 21a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-5.379a.75.75 0 0 1-.53-.22L11.47 3.66A2.25 2.25 0 0 0 9.879 3H4.5a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h15ZM9 12.75a.75.75 0 0 0 0 1.5h6a.75.75 0 0 0 0-1.5H9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef186 = React186.forwardRef(FolderMinusIcon);
var FolderMinusIcon_default = ForwardRef186;

// node_modules/@heroicons/react/24/solid/esm/FolderOpenIcon.js
var React187 = __toESM(require_react(), 1);
function FolderOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React187.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React187.createElement("title", {
    id: titleId
  }, title) : null, React187.createElement("path", {
    d: "M19.906 9c.382 0 .749.057 1.094.162V9a3 3 0 0 0-3-3h-3.879a.75.75 0 0 1-.53-.22L11.47 3.66A2.25 2.25 0 0 0 9.879 3H6a3 3 0 0 0-3 3v3.162A3.756 3.756 0 0 1 4.094 9h15.812ZM4.094 10.5a2.25 2.25 0 0 0-2.227 2.568l.857 6A2.25 2.25 0 0 0 4.951 21H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-2.227-2.568H4.094Z"
  }));
}
var ForwardRef187 = React187.forwardRef(FolderOpenIcon);
var FolderOpenIcon_default = ForwardRef187;

// node_modules/@heroicons/react/24/solid/esm/FolderPlusIcon.js
var React188 = __toESM(require_react(), 1);
function FolderPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React188.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React188.createElement("title", {
    id: titleId
  }, title) : null, React188.createElement("path", {
    fillRule: "evenodd",
    d: "M19.5 21a3 3 0 0 0 3-3V9a3 3 0 0 0-3-3h-5.379a.75.75 0 0 1-.53-.22L11.47 3.66A2.25 2.25 0 0 0 9.879 3H4.5a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h15Zm-6.75-10.5a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25v2.25a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V10.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef188 = React188.forwardRef(FolderPlusIcon);
var FolderPlusIcon_default = ForwardRef188;

// node_modules/@heroicons/react/24/solid/esm/FolderIcon.js
var React189 = __toESM(require_react(), 1);
function FolderIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React189.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React189.createElement("title", {
    id: titleId
  }, title) : null, React189.createElement("path", {
    d: "M19.5 21a3 3 0 0 0 3-3v-4.5a3 3 0 0 0-3-3h-15a3 3 0 0 0-3 3V18a3 3 0 0 0 3 3h15ZM1.5 10.146V6a3 3 0 0 1 3-3h5.379a2.25 2.25 0 0 1 1.59.659l2.122 2.121c.14.141.331.22.53.22H19.5a3 3 0 0 1 3 3v1.146A4.483 4.483 0 0 0 19.5 9h-15a4.483 4.483 0 0 0-3 1.146Z"
  }));
}
var ForwardRef189 = React189.forwardRef(FolderIcon);
var FolderIcon_default = ForwardRef189;

// node_modules/@heroicons/react/24/solid/esm/ForwardIcon.js
var React190 = __toESM(require_react(), 1);
function ForwardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React190.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React190.createElement("title", {
    id: titleId
  }, title) : null, React190.createElement("path", {
    d: "M5.055 7.06C3.805 6.347 2.25 7.25 2.25 8.69v8.122c0 1.44 1.555 2.343 2.805 1.628L12 14.471v2.34c0 1.44 1.555 2.343 2.805 1.628l7.108-4.061c1.26-.72 1.26-2.536 0-3.256l-7.108-4.061C13.555 6.346 12 7.249 12 8.689v2.34L5.055 7.061Z"
  }));
}
var ForwardRef190 = React190.forwardRef(ForwardIcon);
var ForwardIcon_default = ForwardRef190;

// node_modules/@heroicons/react/24/solid/esm/FunnelIcon.js
var React191 = __toESM(require_react(), 1);
function FunnelIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React191.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React191.createElement("title", {
    id: titleId
  }, title) : null, React191.createElement("path", {
    fillRule: "evenodd",
    d: "M3.792 2.938A49.069 49.069 0 0 1 12 2.25c2.797 0 5.54.236 8.209.688a1.857 1.857 0 0 1 1.541 1.836v1.044a3 3 0 0 1-.879 2.121l-6.182 6.182a1.5 1.5 0 0 0-.439 1.061v2.927a3 3 0 0 1-1.658 2.684l-1.757.878A.75.75 0 0 1 9.75 21v-5.818a1.5 1.5 0 0 0-.44-1.06L3.13 7.938a3 3 0 0 1-.879-2.121V4.774c0-.897.64-1.683 1.542-1.836Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef191 = React191.forwardRef(FunnelIcon);
var FunnelIcon_default = ForwardRef191;

// node_modules/@heroicons/react/24/solid/esm/GifIcon.js
var React192 = __toESM(require_react(), 1);
function GifIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React192.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React192.createElement("title", {
    id: titleId
  }, title) : null, React192.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 3.75a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V6.75a3 3 0 0 0-3-3h-15Zm9 4.5a.75.75 0 0 0-1.5 0v7.5a.75.75 0 0 0 1.5 0v-7.5Zm1.5 0a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 0 1.5H16.5v2.25H18a.75.75 0 0 1 0 1.5h-1.5v3a.75.75 0 0 1-1.5 0v-7.5ZM6.636 9.78c.404-.575.867-.78 1.25-.78s.846.205 1.25.78a.75.75 0 0 0 1.228-.863C9.738 8.027 8.853 7.5 7.886 7.5c-.966 0-1.852.527-2.478 1.417-.62.882-.908 2-.908 3.083 0 1.083.288 2.201.909 3.083.625.89 1.51 1.417 2.477 1.417.967 0 1.852-.527 2.478-1.417a.75.75 0 0 0 .136-.431V12a.75.75 0 0 0-.75-.75h-1.5a.75.75 0 0 0 0 1.5H9v1.648c-.37.44-.774.602-1.114.602-.383 0-.846-.205-1.25-.78C6.226 13.638 6 12.837 6 12c0-.837.226-1.638.636-2.22Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef192 = React192.forwardRef(GifIcon);
var GifIcon_default = ForwardRef192;

// node_modules/@heroicons/react/24/solid/esm/GiftTopIcon.js
var React193 = __toESM(require_react(), 1);
function GiftTopIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React193.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React193.createElement("title", {
    id: titleId
  }, title) : null, React193.createElement("path", {
    d: "M11.25 3v4.046a3 3 0 0 0-4.277 4.204H1.5v-6A2.25 2.25 0 0 1 3.75 3h7.5ZM12.75 3v4.011a3 3 0 0 1 4.239 4.239H22.5v-6A2.25 2.25 0 0 0 20.25 3h-7.5ZM22.5 12.75h-8.983a4.125 4.125 0 0 0 4.108 ********** 0 0 1 0 1.5 5.623 5.623 0 0 1-4.875-2.817V21h7.5a2.25 2.25 0 0 0 2.25-2.25v-6ZM11.25 21v-5.817A5.623 5.623 0 0 1 6.375 18a.75.75 0 0 1 0-1.5 4.126 4.126 0 0 0 4.108-3.75H1.5v6A2.25 2.25 0 0 0 3.75 21h7.5Z"
  }), React193.createElement("path", {
    d: "M11.085 10.354c.03.297.038.575.036.805a7.484 7.484 0 0 1-.805-.036c-.833-.084-1.677-.325-2.195-.843a1.5 1.5 0 0 1 2.122-2.12c.517.517.759 1.36.842 2.194ZM12.877 10.354c-.03.297-.038.575-.036.805.23.002.508-.006.805-.036.833-.084 1.677-.325 2.195-.843A1.5 1.5 0 0 0 13.72 8.16c-.518.518-.76 1.362-.843 2.194Z"
  }));
}
var ForwardRef193 = React193.forwardRef(GiftTopIcon);
var GiftTopIcon_default = ForwardRef193;

// node_modules/@heroicons/react/24/solid/esm/GiftIcon.js
var React194 = __toESM(require_react(), 1);
function GiftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React194.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React194.createElement("title", {
    id: titleId
  }, title) : null, React194.createElement("path", {
    d: "M9.375 3a1.875 1.875 0 0 0 0 3.75h1.875v4.5H3.375A1.875 1.875 0 0 1 1.5 9.375v-.75c0-1.036.84-1.875 1.875-1.875h3.193A3.375 3.375 0 0 1 12 2.753a3.375 3.375 0 0 1 5.432 3.997h3.943c1.035 0 1.875.84 1.875 1.875v.75c0 1.036-.84 1.875-1.875 1.875H12.75v-4.5h1.875a1.875 1.875 0 1 0-1.875-1.875V6.75h-1.5V4.875C11.25 3.839 10.41 3 9.375 3ZM11.25 12.75H3v6.75a2.25 2.25 0 0 0 2.25 2.25h6v-9ZM12.75 12.75v9h6.75a2.25 2.25 0 0 0 2.25-2.25v-6.75h-9Z"
  }));
}
var ForwardRef194 = React194.forwardRef(GiftIcon);
var GiftIcon_default = ForwardRef194;

// node_modules/@heroicons/react/24/solid/esm/GlobeAltIcon.js
var React195 = __toESM(require_react(), 1);
function GlobeAltIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React195.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React195.createElement("title", {
    id: titleId
  }, title) : null, React195.createElement("path", {
    d: "M21.721 12.752a9.711 9.711 0 0 0-.945-5.003 12.754 12.754 0 0 1-4.339 2.708 18.991 18.991 0 0 1-.214 4.772 17.165 17.165 0 0 0 5.498-2.477ZM14.634 15.55a17.324 17.324 0 0 0 .332-4.647c-.952.227-1.945.347-2.966.347-1.021 0-2.014-.12-2.966-.347a17.515 17.515 0 0 0 .332 4.647 17.385 17.385 0 0 0 5.268 0ZM9.772 17.119a18.963 18.963 0 0 0 4.456 0A17.182 17.182 0 0 1 12 21.724a17.18 17.18 0 0 1-2.228-4.605ZM7.777 15.23a18.87 18.87 0 0 1-.214-4.774 12.753 12.753 0 0 1-4.34-2.708 9.711 9.711 0 0 0-.944 5.004 17.165 17.165 0 0 0 5.498 2.477ZM21.356 14.752a9.765 9.765 0 0 1-7.478 6.817 18.64 18.64 0 0 0 1.988-4.718 18.627 18.627 0 0 0 5.49-2.098ZM2.644 14.752c1.682.971 3.53 1.688 5.49 2.099a18.64 18.64 0 0 0 1.988 4.718 9.765 9.765 0 0 1-7.478-6.816ZM13.878 2.43a9.755 9.755 0 0 1 6.116 3.986 11.267 11.267 0 0 1-3.746 2.504 18.63 18.63 0 0 0-2.37-6.49ZM12 2.276a17.152 17.152 0 0 1 2.805 7.121c-.897.23-1.837.353-2.805.353-.968 0-1.908-.122-2.805-.353A17.151 17.151 0 0 1 12 2.276ZM10.122 2.43a18.629 18.629 0 0 0-2.37 6.49 11.266 11.266 0 0 1-3.746-2.504 9.754 9.754 0 0 1 6.116-3.985Z"
  }));
}
var ForwardRef195 = React195.forwardRef(GlobeAltIcon);
var GlobeAltIcon_default = ForwardRef195;

// node_modules/@heroicons/react/24/solid/esm/GlobeAmericasIcon.js
var React196 = __toESM(require_react(), 1);
function GlobeAmericasIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React196.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React196.createElement("title", {
    id: titleId
  }, title) : null, React196.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM6.262 6.072a8.25 8.25 0 1 0 10.562-.766 4.5 4.5 0 0 1-1.318 1.357L14.25 7.5l.165.33a.809.809 0 0 1-1.086 1.085l-.604-.302a1.125 1.125 0 0 0-1.298.21l-.132.131c-.439.44-.439 1.152 0 1.591l.296.296c.256.257.622.374.98.314l1.17-.195c.323-.054.654.036.905.245l1.33 1.108c.32.267.46.694.358 1.1a8.7 8.7 0 0 1-2.288 4.04l-.723.724a1.125 1.125 0 0 1-1.298.21l-.153-.076a1.125 1.125 0 0 1-.622-1.006v-1.089c0-.298-.119-.585-.33-.796l-1.347-1.347a1.125 1.125 0 0 1-.21-1.298L9.75 12l-1.64-1.64a6 6 0 0 1-1.676-3.257l-.172-1.03Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef196 = React196.forwardRef(GlobeAmericasIcon);
var GlobeAmericasIcon_default = ForwardRef196;

// node_modules/@heroicons/react/24/solid/esm/GlobeAsiaAustraliaIcon.js
var React197 = __toESM(require_react(), 1);
function GlobeAsiaAustraliaIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React197.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React197.createElement("title", {
    id: titleId
  }, title) : null, React197.createElement("path", {
    d: "M15.75 8.25a.75.75 0 0 1 .75.75c0 1.12-.492 2.126-1.27 2.812a.75.75 0 1 1-.992-1.124A2.243 2.243 0 0 0 15 9a.75.75 0 0 1 .75-.75Z"
  }), React197.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM4.575 15.6a8.25 8.25 0 0 0 9.348 4.425 1.966 1.966 0 0 0-1.84-1.275.983.983 0 0 1-.97-.822l-.073-.437c-.094-.565.25-1.11.8-1.267l.99-.282c.427-.123.783-.418.982-.816l.036-.073a1.453 1.453 0 0 1 2.328-.377L16.5 15h.628a2.25 2.25 0 0 1 1.983 1.186 8.25 8.25 0 0 0-6.345-12.4c.044.262.18.503.389.676l1.068.89c.442.369.535 1.01.216 1.49l-.51.766a2.25 2.25 0 0 1-1.161.886l-.143.048a1.107 1.107 0 0 0-.57 1.664c.369.555.169 1.307-.427 1.605L9 13.125l.423 1.059a.956.956 0 0 1-1.652.928l-.679-.906a1.125 1.125 0 0 0-1.906.172L4.575 15.6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef197 = React197.forwardRef(GlobeAsiaAustraliaIcon);
var GlobeAsiaAustraliaIcon_default = ForwardRef197;

// node_modules/@heroicons/react/24/solid/esm/GlobeEuropeAfricaIcon.js
var React198 = __toESM(require_react(), 1);
function GlobeEuropeAfricaIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React198.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React198.createElement("title", {
    id: titleId
  }, title) : null, React198.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM8.547 4.505a8.25 8.25 0 1 0 11.672 8.214l-.46-.46a2.252 2.252 0 0 1-.422-.586l-1.08-2.16a.414.414 0 0 0-.663-.107.827.827 0 0 1-.812.21l-1.273-.363a.89.89 0 0 0-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.211.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 0 1-1.81 1.025 1.055 1.055 0 0 1-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.654-.261a2.25 2.25 0 0 1-1.384-2.46l.007-.042a2.25 2.25 0 0 1 .29-.787l.09-.15a2.25 2.25 0 0 1 2.37-1.048l1.178.236a1.125 1.125 0 0 0 1.302-.795l.208-.73a1.125 1.125 0 0 0-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 0 1-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 0 1-1.458-1.137l1.279-2.132Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef198 = React198.forwardRef(GlobeEuropeAfricaIcon);
var GlobeEuropeAfricaIcon_default = ForwardRef198;

// node_modules/@heroicons/react/24/solid/esm/H1Icon.js
var React199 = __toESM(require_react(), 1);
function H1Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React199.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React199.createElement("title", {
    id: titleId
  }, title) : null, React199.createElement("path", {
    fillRule: "evenodd",
    d: "M2.243 3.743a.75.75 0 0 1 .75.75v6.75h9v-6.75a.75.75 0 1 1 1.5 0v15.002a.75.75 0 1 1-1.5 0v-6.751h-9v6.75a.75.75 0 1 1-1.5 0v-15a.75.75 0 0 1 .75-.75Zm17.605 4.964a.75.75 0 0 1 .396.661v9.376h1.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5h1.5V10.77l-1.084.722a.75.75 0 1 1-.832-1.248l2.25-1.5a.75.75 0 0 1 .77-.037Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef199 = React199.forwardRef(H1Icon);
var H1Icon_default = ForwardRef199;

// node_modules/@heroicons/react/24/solid/esm/H2Icon.js
var React200 = __toESM(require_react(), 1);
function H2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React200.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React200.createElement("title", {
    id: titleId
  }, title) : null, React200.createElement("path", {
    fillRule: "evenodd",
    d: "M2.246 3.743a.75.75 0 0 1 .75.75v6.75h9v-6.75a.75.75 0 0 1 1.5 0v15.002a.75.75 0 1 1-1.5 0v-6.751h-9v6.75a.75.75 0 1 1-1.5 0v-15a.75.75 0 0 1 .75-.75ZM18.75 10.5c-.727 0-1.441.054-2.138.16a.75.75 0 1 1-.223-1.484 15.867 15.867 0 0 1 3.635-.125c1.149.092 2.153.923 2.348 2.115.084.516.128 1.045.128 1.584 0 1.065-.676 1.927-1.531 2.354l-2.89 1.445a1.5 1.5 0 0 0-.829 1.342v.86h4.5a.75.75 0 1 1 0 1.5H16.5a.75.75 0 0 1-.75-.75v-1.61a3 3 0 0 1 1.659-2.684l2.89-1.445c.447-.223.701-.62.701-1.012a8.32 8.32 0 0 0-.108-1.342c-.075-.457-.47-.82-.987-.862a14.45 14.45 0 0 0-1.155-.046Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef200 = React200.forwardRef(H2Icon);
var H2Icon_default = ForwardRef200;

// node_modules/@heroicons/react/24/solid/esm/H3Icon.js
var React201 = __toESM(require_react(), 1);
function H3Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React201.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React201.createElement("title", {
    id: titleId
  }, title) : null, React201.createElement("path", {
    fillRule: "evenodd",
    d: "M12.749 3.743a.75.75 0 0 1 .75.75v15.002a.75.75 0 1 1-1.5 0v-6.75H2.997v6.75a.75.75 0 0 1-1.5 0V4.494a.75.75 0 1 1 1.5 0v6.75H12v-6.75a.75.75 0 0 1 .75-.75ZM18.75 10.5c-.727 0-1.441.055-2.139.16a.75.75 0 1 1-.223-1.483 15.87 15.87 0 0 1 3.82-.11c.95.088 1.926.705 2.168 1.794a5.265 5.265 0 0 1-.579 3.765 5.265 5.265 0 0 1 .578 3.765c-.24 1.088-1.216 1.706-2.167 1.793a15.942 15.942 0 0 1-3.82-.109.75.75 0 0 1 .223-1.483 14.366 14.366 0 0 0 3.46.099c.467-.043.773-.322.84-.624a3.768 3.768 0 0 0-.413-2.691H18a.75.75 0 0 1 0-1.5h2.498a3.768 3.768 0 0 0 .413-2.69c-.067-.303-.373-.582-.84-.625-.435-.04-.876-.06-1.321-.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef201 = React201.forwardRef(H3Icon);
var H3Icon_default = ForwardRef201;

// node_modules/@heroicons/react/24/solid/esm/HandRaisedIcon.js
var React202 = __toESM(require_react(), 1);
function HandRaisedIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React202.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React202.createElement("title", {
    id: titleId
  }, title) : null, React202.createElement("path", {
    d: "M10.5 1.875a1.125 1.125 0 0 1 2.25 0v8.219c.517.162 1.02.382 1.5.659V3.375a1.125 1.125 0 0 1 2.25 0v10.937a4.505 4.505 0 0 0-3.25 2.373 8.963 8.963 0 0 1 4-.935A.75.75 0 0 0 18 15v-2.266a3.368 3.368 0 0 1 .988-2.37 1.125 1.125 0 0 1 1.591 1.59 1.118 1.118 0 0 0-.329.79v3.006h-.005a6 6 0 0 1-1.752 4.007l-1.736 1.736a6 6 0 0 1-4.242 1.757H10.5a7.5 7.5 0 0 1-7.5-7.5V6.375a1.125 1.125 0 0 1 2.25 0v5.519c.46-.452.965-.832 1.5-1.141V3.375a1.125 1.125 0 0 1 2.25 0v6.526c.495-.1.997-.151 1.5-.151V1.875Z"
  }));
}
var ForwardRef202 = React202.forwardRef(HandRaisedIcon);
var HandRaisedIcon_default = ForwardRef202;

// node_modules/@heroicons/react/24/solid/esm/HandThumbDownIcon.js
var React203 = __toESM(require_react(), 1);
function HandThumbDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React203.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React203.createElement("title", {
    id: titleId
  }, title) : null, React203.createElement("path", {
    d: "M15.73 5.5h1.035A7.465 7.465 0 0 1 18 9.625a7.465 7.465 0 0 1-1.235 4.125h-.148c-.806 0-1.534.446-2.031 1.08a9.04 9.04 0 0 1-2.861 2.4c-.723.384-1.35.956-1.653 1.715a4.499 4.499 0 0 0-.322 1.672v.633A.75.75 0 0 1 9 22a2.25 2.25 0 0 1-2.25-2.25c0-1.152.26-2.243.723-3.218.266-.558-.107-1.282-.725-1.282H3.622c-1.026 0-1.945-.694-2.054-1.715A12.137 12.137 0 0 1 1.5 12.25c0-2.848.992-5.464 2.649-7.521C4.537 4.247 5.136 4 5.754 4H9.77a4.5 4.5 0 0 1 1.423.23l3.114 1.04a4.5 4.5 0 0 0 1.423.23ZM21.669 14.023c.536-1.362.831-2.845.831-4.398 0-1.22-.182-2.398-.52-3.507-.26-.85-1.084-1.368-1.973-1.368H19.1c-.445 0-.72.498-.523.898.591 1.2.924 2.55.924 3.977a8.958 8.958 0 0 1-1.302 4.666c-.245.403.028.959.5.959h1.053c.832 0 1.612-.453 1.918-1.227Z"
  }));
}
var ForwardRef203 = React203.forwardRef(HandThumbDownIcon);
var HandThumbDownIcon_default = ForwardRef203;

// node_modules/@heroicons/react/24/solid/esm/HandThumbUpIcon.js
var React204 = __toESM(require_react(), 1);
function HandThumbUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React204.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React204.createElement("title", {
    id: titleId
  }, title) : null, React204.createElement("path", {
    d: "M7.493 18.5c-.425 0-.82-.236-.975-.632A7.48 7.48 0 0 1 6 15.125c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 0 1 2.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 0 0 .322-1.672V2.75A.75.75 0 0 1 15 2a2.25 2.25 0 0 1 2.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 0 1-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 0 0-1.423-.23h-.777ZM2.331 10.727a11.969 11.969 0 0 0-.831 4.398 12 12 0 0 0 .52 3.507C2.28 19.482 3.105 20 3.994 20H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 0 1-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227Z"
  }));
}
var ForwardRef204 = React204.forwardRef(HandThumbUpIcon);
var HandThumbUpIcon_default = ForwardRef204;

// node_modules/@heroicons/react/24/solid/esm/HashtagIcon.js
var React205 = __toESM(require_react(), 1);
function HashtagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React205.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React205.createElement("title", {
    id: titleId
  }, title) : null, React205.createElement("path", {
    fillRule: "evenodd",
    d: "M11.097 1.515a.75.75 0 0 1 .589.882L10.666 7.5h4.47l1.079-5.397a.75.75 0 1 1 1.47.294L16.665 7.5h3.585a.75.75 0 0 1 0 1.5h-3.885l-1.2 6h3.585a.75.75 0 0 1 0 1.5h-3.885l-1.08 5.397a.75.75 0 1 1-1.47-.294l1.02-5.103h-4.47l-1.08 5.397a.75.75 0 1 1-1.47-.294l1.02-5.103H3.75a.75.75 0 0 1 0-1.5h3.885l1.2-6H5.25a.75.75 0 0 1 0-1.5h3.885l1.08-5.397a.75.75 0 0 1 .882-.588ZM10.365 9l-1.2 6h4.47l1.2-6h-4.47Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef205 = React205.forwardRef(HashtagIcon);
var HashtagIcon_default = ForwardRef205;

// node_modules/@heroicons/react/24/solid/esm/HeartIcon.js
var React206 = __toESM(require_react(), 1);
function HeartIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React206.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React206.createElement("title", {
    id: titleId
  }, title) : null, React206.createElement("path", {
    d: "m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z"
  }));
}
var ForwardRef206 = React206.forwardRef(HeartIcon);
var HeartIcon_default = ForwardRef206;

// node_modules/@heroicons/react/24/solid/esm/HomeModernIcon.js
var React207 = __toESM(require_react(), 1);
function HomeModernIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React207.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React207.createElement("title", {
    id: titleId
  }, title) : null, React207.createElement("path", {
    d: "M19.006 3.705a.75.75 0 1 0-.512-1.41L6 6.838V3a.75.75 0 0 0-.75-.75h-1.5A.75.75 0 0 0 3 3v4.93l-1.006.365a.75.75 0 0 0 .512 1.41l16.5-6Z"
  }), React207.createElement("path", {
    fillRule: "evenodd",
    d: "M3.019 11.114 18 5.667v3.421l4.006 1.457a.75.75 0 1 1-.512 1.41l-.494-.18v8.475h.75a.75.75 0 0 1 0 1.5H2.25a.75.75 0 0 1 0-1.5H3v-9.129l.019-.007ZM18 20.25v-9.566l1.5.546v9.02H18Zm-9-6a.75.75 0 0 0-.75.75v4.5c0 .414.336.75.75.75h3a.75.75 0 0 0 .75-.75V15a.75.75 0 0 0-.75-.75H9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef207 = React207.forwardRef(HomeModernIcon);
var HomeModernIcon_default = ForwardRef207;

// node_modules/@heroicons/react/24/solid/esm/HomeIcon.js
var React208 = __toESM(require_react(), 1);
function HomeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React208.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React208.createElement("title", {
    id: titleId
  }, title) : null, React208.createElement("path", {
    d: "M11.47 3.841a.75.75 0 0 1 1.06 0l8.69 8.69a.75.75 0 1 0 1.06-1.061l-8.689-8.69a2.25 2.25 0 0 0-3.182 0l-8.69 8.69a.75.75 0 1 0 1.061 1.06l8.69-8.689Z"
  }), React208.createElement("path", {
    d: "m12 5.432 8.159 8.159c.************.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 0-.75-.75h-3a.75.75 0 0 0-.75.75V21a.75.75 0 0 1-.75.75H5.625a1.875 1.875 0 0 1-1.875-1.875v-6.198a2.29 2.29 0 0 0 .091-.086L12 5.432Z"
  }));
}
var ForwardRef208 = React208.forwardRef(HomeIcon);
var HomeIcon_default = ForwardRef208;

// node_modules/@heroicons/react/24/solid/esm/IdentificationIcon.js
var React209 = __toESM(require_react(), 1);
function IdentificationIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React209.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React209.createElement("title", {
    id: titleId
  }, title) : null, React209.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 3.75a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V6.75a3 3 0 0 0-3-3h-15Zm4.125 3a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5Zm-3.873 8.703a4.126 4.126 0 0 1 7.746 0 .75.75 0 0 1-.351.92 7.47 7.47 0 0 1-3.522.877 7.47 7.47 0 0 1-3.522-.877.75.75 0 0 1-.351-.92ZM15 8.25a.75.75 0 0 0 0 1.5h3.75a.75.75 0 0 0 0-1.5H15ZM14.25 12a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H15a.75.75 0 0 1-.75-.75Zm.75 2.25a.75.75 0 0 0 0 1.5h3.75a.75.75 0 0 0 0-1.5H15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef209 = React209.forwardRef(IdentificationIcon);
var IdentificationIcon_default = ForwardRef209;

// node_modules/@heroicons/react/24/solid/esm/InboxArrowDownIcon.js
var React210 = __toESM(require_react(), 1);
function InboxArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React210.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React210.createElement("title", {
    id: titleId
  }, title) : null, React210.createElement("path", {
    fillRule: "evenodd",
    d: "M5.478 5.559A1.5 1.5 0 0 1 6.912 4.5H9A.75.75 0 0 0 9 3H6.912a3 3 0 0 0-2.868 2.118l-2.411 7.838a3 3 0 0 0-.133.882V18a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-4.162c0-.299-.045-.596-.133-.882l-2.412-7.838A3 3 0 0 0 17.088 3H15a.75.75 0 0 0 0 1.5h2.088a1.5 1.5 0 0 1 1.434 1.059l2.213 7.191H17.89a3 3 0 0 0-2.684 1.658l-.256.513a1.5 1.5 0 0 1-1.342.829h-3.218a1.5 1.5 0 0 1-1.342-.83l-.256-.512a3 3 0 0 0-2.684-1.658H3.265l2.213-7.191Z",
    clipRule: "evenodd"
  }), React210.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25a.75.75 0 0 1 .75.75v6.44l1.72-1.72a.75.75 0 1 1 1.06 1.06l-3 3a.75.75 0 0 1-1.06 0l-3-3a.75.75 0 0 1 1.06-1.06l1.72 1.72V3a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef210 = React210.forwardRef(InboxArrowDownIcon);
var InboxArrowDownIcon_default = ForwardRef210;

// node_modules/@heroicons/react/24/solid/esm/InboxStackIcon.js
var React211 = __toESM(require_react(), 1);
function InboxStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React211.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React211.createElement("title", {
    id: titleId
  }, title) : null, React211.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 9.832v1.793c0 1.036.84 1.875 1.875 1.875h17.25c1.035 0 1.875-.84 1.875-1.875V9.832a3 3 0 0 0-.722-1.952l-3.285-3.832A3 3 0 0 0 16.215 3h-8.43a3 3 0 0 0-2.278 1.048L2.222 7.88A3 3 0 0 0 1.5 9.832ZM7.785 4.5a1.5 1.5 0 0 0-1.139.524L3.881 8.25h3.165a3 3 0 0 1 2.496 1.336l.164.246a1.5 1.5 0 0 0 1.248.668h2.092a1.5 1.5 0 0 0 1.248-.668l.164-.246a3 3 0 0 1 2.496-1.336h3.165l-2.765-3.226a1.5 1.5 0 0 0-1.139-.524h-8.43Z",
    clipRule: "evenodd"
  }), React211.createElement("path", {
    d: "M2.813 15c-.725 0-1.313.588-1.313 1.313V18a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-1.688c0-.724-.588-1.312-1.313-1.312h-4.233a3 3 0 0 0-2.496 1.336l-.164.246a1.5 1.5 0 0 1-1.248.668h-2.092a1.5 1.5 0 0 1-1.248-.668l-.164-.246A3 3 0 0 0 7.046 15H2.812Z"
  }));
}
var ForwardRef211 = React211.forwardRef(InboxStackIcon);
var InboxStackIcon_default = ForwardRef211;

// node_modules/@heroicons/react/24/solid/esm/InboxIcon.js
var React212 = __toESM(require_react(), 1);
function InboxIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React212.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React212.createElement("title", {
    id: titleId
  }, title) : null, React212.createElement("path", {
    fillRule: "evenodd",
    d: "M6.912 3a3 3 0 0 0-2.868 2.118l-2.411 7.838a3 3 0 0 0-.133.882V18a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3v-4.162c0-.299-.045-.596-.133-.882l-2.412-7.838A3 3 0 0 0 17.088 3H6.912Zm13.823 9.75-2.213-7.191A1.5 1.5 0 0 0 17.088 4.5H6.912a1.5 1.5 0 0 0-1.434 1.059L3.265 12.75H6.11a3 3 0 0 1 2.684 1.658l.256.513a1.5 1.5 0 0 0 1.342.829h3.218a1.5 1.5 0 0 0 1.342-.83l.256-.512a3 3 0 0 1 2.684-1.658h2.844Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef212 = React212.forwardRef(InboxIcon);
var InboxIcon_default = ForwardRef212;

// node_modules/@heroicons/react/24/solid/esm/InformationCircleIcon.js
var React213 = __toESM(require_react(), 1);
function InformationCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React213.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React213.createElement("title", {
    id: titleId
  }, title) : null, React213.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm8.706-1.442c1.146-.573 2.437.463 2.126 1.706l-.709 2.836.042-.02a.75.75 0 0 1 .67 1.34l-.04.022c-1.147.573-2.438-.463-2.127-1.706l.71-2.836-.042.02a.75.75 0 1 1-.671-1.34l.041-.022ZM12 9a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef213 = React213.forwardRef(InformationCircleIcon);
var InformationCircleIcon_default = ForwardRef213;

// node_modules/@heroicons/react/24/solid/esm/ItalicIcon.js
var React214 = __toESM(require_react(), 1);
function ItalicIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React214.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React214.createElement("title", {
    id: titleId
  }, title) : null, React214.createElement("path", {
    fillRule: "evenodd",
    d: "M10.497 3.744a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-3.275l-5.357 15.002h2.632a.75.75 0 1 1 0 1.5h-7.5a.75.75 0 1 1 0-1.5h3.275l5.357-15.002h-2.632a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef214 = React214.forwardRef(ItalicIcon);
var ItalicIcon_default = ForwardRef214;

// node_modules/@heroicons/react/24/solid/esm/KeyIcon.js
var React215 = __toESM(require_react(), 1);
function KeyIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React215.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React215.createElement("title", {
    id: titleId
  }, title) : null, React215.createElement("path", {
    fillRule: "evenodd",
    d: "M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef215 = React215.forwardRef(KeyIcon);
var KeyIcon_default = ForwardRef215;

// node_modules/@heroicons/react/24/solid/esm/LanguageIcon.js
var React216 = __toESM(require_react(), 1);
function LanguageIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React216.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React216.createElement("title", {
    id: titleId
  }, title) : null, React216.createElement("path", {
    fillRule: "evenodd",
    d: "M9 2.25a.75.75 0 0 1 .75.75v1.506a49.384 49.384 0 0 1 5.343.371.75.75 0 1 1-.186 1.489c-.66-.083-1.323-.151-1.99-.206a18.67 18.67 0 0 1-2.97 6.323c.318.384.65.753 1 1.107a.75.75 0 0 1-1.07 1.052A18.902 18.902 0 0 1 9 13.687a18.823 18.823 0 0 1-5.656 4.482.75.75 0 0 1-.688-1.333 17.323 17.323 0 0 0 5.396-4.353A18.72 18.72 0 0 1 5.89 8.598a.75.75 0 0 1 1.388-.568A17.21 17.21 0 0 0 9 11.224a17.168 17.168 0 0 0 2.391-5.165 48.04 48.04 0 0 0-8.298.307.75.75 0 0 1-.186-1.489 49.159 49.159 0 0 1 5.343-.371V3A.75.75 0 0 1 9 2.25ZM15.75 9a.75.75 0 0 1 .68.433l5.25 11.25a.75.75 0 1 1-1.36.634l-1.198-2.567h-6.744l-1.198 2.567a.75.75 0 0 1-1.36-.634l5.25-11.25A.75.75 0 0 1 15.75 9Zm-2.672 8.25h5.344l-2.672-5.726-2.672 5.726Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef216 = React216.forwardRef(LanguageIcon);
var LanguageIcon_default = ForwardRef216;

// node_modules/@heroicons/react/24/solid/esm/LifebuoyIcon.js
var React217 = __toESM(require_react(), 1);
function LifebuoyIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React217.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React217.createElement("title", {
    id: titleId
  }, title) : null, React217.createElement("path", {
    fillRule: "evenodd",
    d: "M19.449 8.448 16.388 11a4.52 4.52 0 0 1 0 2.002l3.061 2.55a8.275 8.275 0 0 0 0-7.103ZM15.552 19.45 13 16.388a4.52 4.52 0 0 1-2.002 0l-2.55 3.061a8.275 8.275 0 0 0 7.103 0ZM4.55 15.552 7.612 13a4.52 4.52 0 0 1 0-2.002L4.551 8.45a8.275 8.275 0 0 0 0 7.103ZM8.448 4.55 11 7.612a4.52 4.52 0 0 1 2.002 0l2.55-3.061a8.275 8.275 0 0 0-7.103 0Zm8.657-.86a9.776 9.776 0 0 1 1.79 1.415 9.776 9.776 0 0 1 1.414 1.788 9.764 9.764 0 0 1 0 10.211 9.777 9.777 0 0 1-1.415 1.79 9.777 9.777 0 0 1-1.788 1.414 9.764 9.764 0 0 1-10.212 0 9.776 9.776 0 0 1-1.788-1.415 9.776 9.776 0 0 1-1.415-1.788 9.764 9.764 0 0 1 0-10.212 9.774 9.774 0 0 1 1.415-1.788A9.774 9.774 0 0 1 6.894 3.69a9.764 9.764 0 0 1 10.211 0ZM14.121 9.88a2.985 2.985 0 0 0-1.11-.704 3.015 3.015 0 0 0-2.022 0 2.985 2.985 0 0 0-1.11.704c-.326.325-.56.705-.704 1.11a3.015 3.015 0 0 0 0 2.022c.144.405.378.785.704 1.11.325.326.705.56 1.11.704.652.233 1.37.233 2.022 0a2.985 2.985 0 0 0 1.11-.704c.326-.325.56-.705.704-1.11a3.016 3.016 0 0 0 0-2.022 2.985 2.985 0 0 0-.704-1.11Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef217 = React217.forwardRef(LifebuoyIcon);
var LifebuoyIcon_default = ForwardRef217;

// node_modules/@heroicons/react/24/solid/esm/LightBulbIcon.js
var React218 = __toESM(require_react(), 1);
function LightBulbIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React218.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React218.createElement("title", {
    id: titleId
  }, title) : null, React218.createElement("path", {
    d: "M12 .75a8.25 8.25 0 0 0-4.135 15.39c.686.398 1.115 1.008 1.134 1.623a.75.75 0 0 0 .577.706c.352.083.71.148 1.074.195.323.041.6-.218.6-.544v-4.661a6.714 6.714 0 0 1-.937-.171.75.75 0 1 1 .374-1.453 5.261 5.261 0 0 0 2.626 0 .75.75 0 1 1 .374 1.452 6.712 6.712 0 0 1-.937.172v4.66c0 .327.277.586.6.545.364-.047.722-.112 1.074-.195a.75.75 0 0 0 .577-.706c.02-.615.448-1.225 1.134-1.623A8.25 8.25 0 0 0 12 .75Z"
  }), React218.createElement("path", {
    fillRule: "evenodd",
    d: "M9.013 19.9a.75.75 0 0 1 .877-.597 11.319 11.319 0 0 0 4.22 0 .75.75 0 1 1 .28 1.473 12.819 12.819 0 0 1-4.78 0 .75.75 0 0 1-.597-.876ZM9.754 22.344a.75.75 0 0 1 .824-.668 13.682 13.682 0 0 0 2.844 0 .75.75 0 1 1 .156 1.492 15.156 15.156 0 0 1-3.156 0 .75.75 0 0 1-.668-.824Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef218 = React218.forwardRef(LightBulbIcon);
var LightBulbIcon_default = ForwardRef218;

// node_modules/@heroicons/react/24/solid/esm/LinkSlashIcon.js
var React219 = __toESM(require_react(), 1);
function LinkSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React219.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React219.createElement("title", {
    id: titleId
  }, title) : null, React219.createElement("path", {
    fillRule: "evenodd",
    d: "M19.892 4.09a3.75 3.75 0 0 0-5.303 0l-4.5 4.5c-.074.074-.144.15-.21.229l4.965 4.966a3.75 3.75 0 0 0-1.986-4.428.75.75 0 0 1 .646-1.353 5.253 5.253 0 0 1 2.502 6.944l5.515 5.515a.75.75 0 0 1-1.061 1.06l-18-18.001A.75.75 0 0 1 3.521 2.46l5.294 5.295a5.31 5.31 0 0 1 .213-.227l4.5-4.5a5.25 5.25 0 1 1 7.425 7.425l-1.757 1.757a.75.75 0 1 1-1.06-1.06l1.756-1.757a3.75 3.75 0 0 0 0-5.304ZM5.846 11.773a.75.75 0 0 1 0 1.06l-1.757 1.758a3.75 3.75 0 0 0 5.303 5.304l3.129-3.13a.75.75 0 1 1 1.06 1.061l-3.128 3.13a5.25 5.25 0 1 1-7.425-7.426l1.757-1.757a.75.75 0 0 1 1.061 0Zm2.401.26a.75.75 0 0 1 .957.458c.18.512.474.992.885 1.403.31.311.661.555 1.035.733a.75.75 0 0 1-.647 1.354 5.244 5.244 0 0 1-1.449-1.026 5.232 5.232 0 0 1-1.24-1.965.75.75 0 0 1 .46-.957Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef219 = React219.forwardRef(LinkSlashIcon);
var LinkSlashIcon_default = ForwardRef219;

// node_modules/@heroicons/react/24/solid/esm/LinkIcon.js
var React220 = __toESM(require_react(), 1);
function LinkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React220.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React220.createElement("title", {
    id: titleId
  }, title) : null, React220.createElement("path", {
    fillRule: "evenodd",
    d: "M19.902 4.098a3.75 3.75 0 0 0-5.304 0l-4.5 4.5a3.75 3.75 0 0 0 1.035 *********** 0 0 1-.646 1.353 5.25 5.25 0 0 1-1.449-8.45l4.5-4.5a5.25 5.25 0 1 1 7.424 7.424l-1.757 1.757a.75.75 0 1 1-1.06-1.06l1.757-1.757a3.75 3.75 0 0 0 0-5.304Zm-7.389 4.267a.75.75 0 0 1 1-.353 5.25 5.25 0 0 1 1.449 8.45l-4.5 4.5a5.25 5.25 0 1 1-7.424-7.424l1.757-1.757a.75.75 0 1 1 1.06 1.06l-1.757 1.757a3.75 3.75 0 1 0 5.304 5.304l4.5-4.5a3.75 3.75 0 0 0-1.035-*********** 0 0 1-.354-1Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef220 = React220.forwardRef(LinkIcon);
var LinkIcon_default = ForwardRef220;

// node_modules/@heroicons/react/24/solid/esm/ListBulletIcon.js
var React221 = __toESM(require_react(), 1);
function ListBulletIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React221.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React221.createElement("title", {
    id: titleId
  }, title) : null, React221.createElement("path", {
    fillRule: "evenodd",
    d: "M2.625 6.75a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm4.875 0A.75.75 0 0 1 8.25 6h12a.75.75 0 0 1 0 1.5h-12a.75.75 0 0 1-.75-.75ZM2.625 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0ZM7.5 12a.75.75 0 0 1 .75-.75h12a.75.75 0 0 1 0 1.5h-12A.75.75 0 0 1 7.5 12Zm-4.875 5.25a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm4.875 0a.75.75 0 0 1 .75-.75h12a.75.75 0 0 1 0 1.5h-12a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef221 = React221.forwardRef(ListBulletIcon);
var ListBulletIcon_default = ForwardRef221;

// node_modules/@heroicons/react/24/solid/esm/LockClosedIcon.js
var React222 = __toESM(require_react(), 1);
function LockClosedIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React222.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React222.createElement("title", {
    id: titleId
  }, title) : null, React222.createElement("path", {
    fillRule: "evenodd",
    d: "M12 1.5a5.25 5.25 0 0 0-5.25 5.25v3a3 3 0 0 0-3 3v6.75a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3v-6.75a3 3 0 0 0-3-3v-3c0-2.9-2.35-5.25-5.25-5.25Zm3.75 8.25v-3a3.75 3.75 0 1 0-7.5 0v3h7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef222 = React222.forwardRef(LockClosedIcon);
var LockClosedIcon_default = ForwardRef222;

// node_modules/@heroicons/react/24/solid/esm/LockOpenIcon.js
var React223 = __toESM(require_react(), 1);
function LockOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React223.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React223.createElement("title", {
    id: titleId
  }, title) : null, React223.createElement("path", {
    d: "M18 1.5c2.9 0 5.25 2.35 5.25 5.25v3.75a.75.75 0 0 1-1.5 0V6.75a3.75 3.75 0 1 0-7.5 0v3a3 3 0 0 1 3 3v6.75a3 3 0 0 1-3 3H3.75a3 3 0 0 1-3-3v-6.75a3 3 0 0 1 3-3h9v-3c0-2.9 2.35-5.25 5.25-5.25Z"
  }));
}
var ForwardRef223 = React223.forwardRef(LockOpenIcon);
var LockOpenIcon_default = ForwardRef223;

// node_modules/@heroicons/react/24/solid/esm/MagnifyingGlassCircleIcon.js
var React224 = __toESM(require_react(), 1);
function MagnifyingGlassCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React224.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React224.createElement("title", {
    id: titleId
  }, title) : null, React224.createElement("path", {
    d: "M8.25 10.875a2.625 2.625 0 1 1 5.25 0 2.625 2.625 0 0 1-5.25 0Z"
  }), React224.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.125 4.5a4.125 4.125 0 1 0 2.338 7.524l2.007 2.006a.75.75 0 1 0 1.06-1.06l-2.006-2.007a4.125 4.125 0 0 0-3.399-6.463Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef224 = React224.forwardRef(MagnifyingGlassCircleIcon);
var MagnifyingGlassCircleIcon_default = ForwardRef224;

// node_modules/@heroicons/react/24/solid/esm/MagnifyingGlassMinusIcon.js
var React225 = __toESM(require_react(), 1);
function MagnifyingGlassMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React225.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React225.createElement("title", {
    id: titleId
  }, title) : null, React225.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.69 4.69a.75.75 0 1 1-1.06 1.06l-4.69-4.69A8.25 8.25 0 0 1 2.25 10.5Zm4.5 0a.75.75 0 0 1 .75-.75h6a.75.75 0 0 1 0 1.5h-6a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef225 = React225.forwardRef(MagnifyingGlassMinusIcon);
var MagnifyingGlassMinusIcon_default = ForwardRef225;

// node_modules/@heroicons/react/24/solid/esm/MagnifyingGlassPlusIcon.js
var React226 = __toESM(require_react(), 1);
function MagnifyingGlassPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React226.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React226.createElement("title", {
    id: titleId
  }, title) : null, React226.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.69 4.69a.75.75 0 1 1-1.06 1.06l-4.69-4.69A8.25 8.25 0 0 1 2.25 10.5Zm8.25-3.75a.75.75 0 0 1 .75.75v2.25h2.25a.75.75 0 0 1 0 1.5h-2.25v2.25a.75.75 0 0 1-1.5 0v-2.25H7.5a.75.75 0 0 1 0-1.5h2.25V7.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef226 = React226.forwardRef(MagnifyingGlassPlusIcon);
var MagnifyingGlassPlusIcon_default = ForwardRef226;

// node_modules/@heroicons/react/24/solid/esm/MagnifyingGlassIcon.js
var React227 = __toESM(require_react(), 1);
function MagnifyingGlassIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React227.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React227.createElement("title", {
    id: titleId
  }, title) : null, React227.createElement("path", {
    fillRule: "evenodd",
    d: "M10.5 3.75a6.75 6.75 0 1 0 0 13.5 6.75 6.75 0 0 0 0-13.5ZM2.25 10.5a8.25 8.25 0 1 1 14.59 5.28l4.69 4.69a.75.75 0 1 1-1.06 1.06l-4.69-4.69A8.25 8.25 0 0 1 2.25 10.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef227 = React227.forwardRef(MagnifyingGlassIcon);
var MagnifyingGlassIcon_default = ForwardRef227;

// node_modules/@heroicons/react/24/solid/esm/MapPinIcon.js
var React228 = __toESM(require_react(), 1);
function MapPinIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React228.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React228.createElement("title", {
    id: titleId
  }, title) : null, React228.createElement("path", {
    fillRule: "evenodd",
    d: "m11.54 22.351.07.04.028.016a.76.76 0 0 0 .723 0l.028-.015.071-.041a16.975 16.975 0 0 0 1.144-.742 19.58 19.58 0 0 0 2.683-2.282c1.944-1.99 3.963-4.98 3.963-8.827a8.25 8.25 0 0 0-16.5 0c0 3.846 2.02 6.837 3.963 8.827a19.58 19.58 0 0 0 2.682 2.282 16.975 16.975 0 0 0 1.145.742ZM12 13.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef228 = React228.forwardRef(MapPinIcon);
var MapPinIcon_default = ForwardRef228;

// node_modules/@heroicons/react/24/solid/esm/MapIcon.js
var React229 = __toESM(require_react(), 1);
function MapIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React229.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React229.createElement("title", {
    id: titleId
  }, title) : null, React229.createElement("path", {
    fillRule: "evenodd",
    d: "M8.161 2.58a1.875 1.875 0 0 1 1.678 0l4.993 2.498c.**************.336 0l3.869-1.935A1.875 1.875 0 0 1 21.75 4.82v12.485c0 .71-.401 1.36-1.037 1.677l-4.875 2.437a1.875 1.875 0 0 1-1.676 0l-4.994-2.497a.375.375 0 0 0-.336 0l-3.868 1.935A1.875 1.875 0 0 1 2.25 19.18V6.695c0-.71.401-1.36 1.036-1.677l4.875-2.437ZM9 6a.75.75 0 0 1 .75.75V15a.75.75 0 0 1-1.5 0V6.75A.75.75 0 0 1 9 6Zm6.75 3a.75.75 0 0 0-1.5 0v8.25a.75.75 0 0 0 1.5 0V9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef229 = React229.forwardRef(MapIcon);
var MapIcon_default = ForwardRef229;

// node_modules/@heroicons/react/24/solid/esm/MegaphoneIcon.js
var React230 = __toESM(require_react(), 1);
function MegaphoneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React230.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React230.createElement("title", {
    id: titleId
  }, title) : null, React230.createElement("path", {
    d: "M16.881 4.345A23.112 23.112 0 0 1 8.25 6H7.5a5.25 5.25 0 0 0-.88 10.427 21.593 21.593 0 0 0 1.378 3.94c.464 1.004 1.674 1.32 2.582.796l.657-.379c.88-.508 1.165-1.593.772-2.468a17.116 17.116 0 0 1-.628-1.607c1.918.258 3.76.75 5.5 1.446A21.727 21.727 0 0 0 18 11.25c0-2.414-.393-4.735-1.119-6.905ZM18.26 3.74a23.22 23.22 0 0 1 1.24 7.51 23.22 23.22 0 0 1-1.41 7.992.75.75 0 1 0 1.409.516 24.555 24.555 0 0 0 1.415-6.43 2.992 2.992 0 0 0 .836-2.078c0-.807-.319-1.54-.836-2.078a24.65 24.65 0 0 0-1.415-6.43.75.75 0 1 0-1.409.516c.059.16.116.321.17.483Z"
  }));
}
var ForwardRef230 = React230.forwardRef(MegaphoneIcon);
var MegaphoneIcon_default = ForwardRef230;

// node_modules/@heroicons/react/24/solid/esm/MicrophoneIcon.js
var React231 = __toESM(require_react(), 1);
function MicrophoneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React231.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React231.createElement("title", {
    id: titleId
  }, title) : null, React231.createElement("path", {
    d: "M8.25 4.5a3.75 3.75 0 1 1 7.5 0v8.25a3.75 3.75 0 1 1-7.5 0V4.5Z"
  }), React231.createElement("path", {
    d: "M6 10.5a.75.75 0 0 1 .75.75v1.5a5.25 5.25 0 1 0 10.5 0v-1.5a.75.75 0 0 1 1.5 0v1.5a6.751 6.751 0 0 1-6 6.709v2.291h3a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3v-2.291a6.751 6.751 0 0 1-6-6.709v-1.5A.75.75 0 0 1 6 10.5Z"
  }));
}
var ForwardRef231 = React231.forwardRef(MicrophoneIcon);
var MicrophoneIcon_default = ForwardRef231;

// node_modules/@heroicons/react/24/solid/esm/MinusCircleIcon.js
var React232 = __toESM(require_react(), 1);
function MinusCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React232.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React232.createElement("title", {
    id: titleId
  }, title) : null, React232.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm3 10.5a.75.75 0 0 0 0-1.5H9a.75.75 0 0 0 0 1.5h6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef232 = React232.forwardRef(MinusCircleIcon);
var MinusCircleIcon_default = ForwardRef232;

// node_modules/@heroicons/react/24/solid/esm/MinusSmallIcon.js
var React233 = __toESM(require_react(), 1);
function MinusSmallIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React233.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React233.createElement("title", {
    id: titleId
  }, title) : null, React233.createElement("path", {
    fillRule: "evenodd",
    d: "M5.25 12a.75.75 0 0 1 .75-.75h12a.75.75 0 0 1 0 1.5H6a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef233 = React233.forwardRef(MinusSmallIcon);
var MinusSmallIcon_default = ForwardRef233;

// node_modules/@heroicons/react/24/solid/esm/MinusIcon.js
var React234 = __toESM(require_react(), 1);
function MinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React234.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React234.createElement("title", {
    id: titleId
  }, title) : null, React234.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 12a.75.75 0 0 1 .75-.75h14a.75.75 0 0 1 0 1.5H5a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef234 = React234.forwardRef(MinusIcon);
var MinusIcon_default = ForwardRef234;

// node_modules/@heroicons/react/24/solid/esm/MoonIcon.js
var React235 = __toESM(require_react(), 1);
function MoonIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React235.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React235.createElement("title", {
    id: titleId
  }, title) : null, React235.createElement("path", {
    fillRule: "evenodd",
    d: "M9.528 1.718a.75.75 0 0 1 .162.819A8.97 8.97 0 0 0 9 6a9 9 0 0 0 9 9 8.97 8.97 0 0 0 3.463-.69.75.75 0 0 1 .981.98 10.503 10.503 0 0 1-9.694 6.46c-5.799 0-10.5-4.7-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 0 1 .818.162Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef235 = React235.forwardRef(MoonIcon);
var MoonIcon_default = ForwardRef235;

// node_modules/@heroicons/react/24/solid/esm/MusicalNoteIcon.js
var React236 = __toESM(require_react(), 1);
function MusicalNoteIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React236.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React236.createElement("title", {
    id: titleId
  }, title) : null, React236.createElement("path", {
    fillRule: "evenodd",
    d: "M19.952 1.651a.75.75 0 0 1 .298.599V16.303a3 3 0 0 1-2.176 2.884l-1.32.377a2.553 2.553 0 1 1-1.403-4.909l2.311-.66a1.5 1.5 0 0 0 1.088-1.442V6.994l-9 2.572v9.737a3 3 0 0 1-2.176 2.884l-1.32.377a2.553 2.553 0 1 1-1.402-4.909l2.31-.66a1.5 1.5 0 0 0 1.088-1.442V5.25a.75.75 0 0 1 .544-.721l10.5-3a.75.75 0 0 1 .658.122Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef236 = React236.forwardRef(MusicalNoteIcon);
var MusicalNoteIcon_default = ForwardRef236;

// node_modules/@heroicons/react/24/solid/esm/NewspaperIcon.js
var React237 = __toESM(require_react(), 1);
function NewspaperIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React237.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React237.createElement("title", {
    id: titleId
  }, title) : null, React237.createElement("path", {
    fillRule: "evenodd",
    d: "M4.125 3C3.089 3 2.25 3.84 2.25 4.875V18a3 3 0 0 0 3 3h15a3 3 0 0 1-3-3V4.875C17.25 3.839 16.41 3 15.375 3H4.125ZM12 9.75a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5H12Zm-.75-2.25a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5H12a.75.75 0 0 1-.75-.75ZM6 12.75a.75.75 0 0 0 0 1.5h7.5a.75.75 0 0 0 0-1.5H6Zm-.75 3.75a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5H6a.75.75 0 0 1-.75-.75ZM6 6.75a.75.75 0 0 0-.75.75v3c0 .414.336.75.75.75h3a.75.75 0 0 0 .75-.75v-3A.75.75 0 0 0 9 6.75H6Z",
    clipRule: "evenodd"
  }), React237.createElement("path", {
    d: "M18.75 6.75h1.875c.621 0 1.125.504 1.125 1.125V18a1.5 1.5 0 0 1-3 0V6.75Z"
  }));
}
var ForwardRef237 = React237.forwardRef(NewspaperIcon);
var NewspaperIcon_default = ForwardRef237;

// node_modules/@heroicons/react/24/solid/esm/NoSymbolIcon.js
var React238 = __toESM(require_react(), 1);
function NoSymbolIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React238.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React238.createElement("title", {
    id: titleId
  }, title) : null, React238.createElement("path", {
    fillRule: "evenodd",
    d: "m6.72 5.66 11.62 11.62A8.25 8.25 0 0 0 6.72 5.66Zm10.56 12.68L5.66 6.72a8.25 8.25 0 0 0 11.62 11.62ZM5.105 5.106c3.807-3.808 9.98-3.808 13.788 0 3.808 3.807 3.808 9.98 0 13.788-3.807 3.808-9.98 3.808-13.788 0-3.808-3.807-3.808-9.98 0-13.788Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef238 = React238.forwardRef(NoSymbolIcon);
var NoSymbolIcon_default = ForwardRef238;

// node_modules/@heroicons/react/24/solid/esm/NumberedListIcon.js
var React239 = __toESM(require_react(), 1);
function NumberedListIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React239.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React239.createElement("title", {
    id: titleId
  }, title) : null, React239.createElement("path", {
    fillRule: "evenodd",
    d: "M7.491 5.992a.75.75 0 0 1 .75-.75h12a.75.75 0 1 1 0 1.5h-12a.75.75 0 0 1-.75-.75ZM7.49 11.995a.75.75 0 0 1 .75-.75h12a.75.75 0 0 1 0 1.5h-12a.75.75 0 0 1-.75-.75ZM7.491 17.994a.75.75 0 0 1 .75-.75h12a.75.75 0 1 1 0 1.5h-12a.75.75 0 0 1-.75-.75ZM2.24 3.745a.75.75 0 0 1 .75-.75h1.125a.75.75 0 0 1 .75.75v3h.375a.75.75 0 0 1 0 1.5H2.99a.75.75 0 0 1 0-1.5h.375v-2.25H2.99a.75.75 0 0 1-.75-.75ZM2.79 10.602a.75.75 0 0 1 0-1.06 1.875 1.875 0 1 1 2.652 2.651l-.55.55h.35a.75.75 0 0 1 0 1.5h-2.16a.75.75 0 0 1-.53-1.281l1.83-1.83a.375.375 0 0 0-.53-.53.75.75 0 0 1-1.062 0ZM2.24 15.745a.75.75 0 0 1 .75-.75h1.125a1.875 1.875 0 0 1 1.501 2.999 1.875 1.875 0 0 1-1.501 3H2.99a.75.75 0 0 1 0-1.501h1.125a.375.375 0 0 0 .036-.748H3.74a.75.75 0 0 1-.75-.75v-.002a.75.75 0 0 1 .75-.75h.411a.375.375 0 0 0-.036-.748H2.99a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef239 = React239.forwardRef(NumberedListIcon);
var NumberedListIcon_default = ForwardRef239;

// node_modules/@heroicons/react/24/solid/esm/PaintBrushIcon.js
var React240 = __toESM(require_react(), 1);
function PaintBrushIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React240.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React240.createElement("title", {
    id: titleId
  }, title) : null, React240.createElement("path", {
    fillRule: "evenodd",
    d: "M20.599 1.5c-.376 0-.743.111-1.055.32l-5.08 3.385a18.747 18.747 0 0 0-3.471 2.987 10.04 10.04 0 0 1 4.815 4.815 18.748 18.748 0 0 0 2.987-3.472l3.386-5.079A1.902 1.902 0 0 0 20.599 1.5Zm-8.3 14.025a18.76 18.76 0 0 0 1.896-1.207 8.026 8.026 0 0 0-4.513-4.513A18.75 18.75 0 0 0 8.475 11.7l-.278.5a5.26 5.26 0 0 1 3.601 3.602l.502-.278ZM6.75 13.5A3.75 3.75 0 0 0 3 17.25a1.5 1.5 0 0 1-1.601 1.497.75.75 0 0 0-.7 1.123 5.25 5.25 0 0 0 9.8-2.62 3.75 3.75 0 0 0-3.75-3.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef240 = React240.forwardRef(PaintBrushIcon);
var PaintBrushIcon_default = ForwardRef240;

// node_modules/@heroicons/react/24/solid/esm/PaperAirplaneIcon.js
var React241 = __toESM(require_react(), 1);
function PaperAirplaneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React241.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React241.createElement("title", {
    id: titleId
  }, title) : null, React241.createElement("path", {
    d: "M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
  }));
}
var ForwardRef241 = React241.forwardRef(PaperAirplaneIcon);
var PaperAirplaneIcon_default = ForwardRef241;

// node_modules/@heroicons/react/24/solid/esm/PaperClipIcon.js
var React242 = __toESM(require_react(), 1);
function PaperClipIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React242.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React242.createElement("title", {
    id: titleId
  }, title) : null, React242.createElement("path", {
    fillRule: "evenodd",
    d: "M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef242 = React242.forwardRef(PaperClipIcon);
var PaperClipIcon_default = ForwardRef242;

// node_modules/@heroicons/react/24/solid/esm/PauseCircleIcon.js
var React243 = __toESM(require_react(), 1);
function PauseCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React243.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React243.createElement("title", {
    id: titleId
  }, title) : null, React243.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12ZM9 8.25a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75h.75a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75H9Zm5.25 0a.75.75 0 0 0-.75.75v6c0 .414.336.75.75.75H15a.75.75 0 0 0 .75-.75V9a.75.75 0 0 0-.75-.75h-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef243 = React243.forwardRef(PauseCircleIcon);
var PauseCircleIcon_default = ForwardRef243;

// node_modules/@heroicons/react/24/solid/esm/PauseIcon.js
var React244 = __toESM(require_react(), 1);
function PauseIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React244.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React244.createElement("title", {
    id: titleId
  }, title) : null, React244.createElement("path", {
    fillRule: "evenodd",
    d: "M6.75 5.25a.75.75 0 0 1 .75-.75H9a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V5.25Zm7.5 0A.75.75 0 0 1 15 4.5h1.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75.75H15a.75.75 0 0 1-.75-.75V5.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef244 = React244.forwardRef(PauseIcon);
var PauseIcon_default = ForwardRef244;

// node_modules/@heroicons/react/24/solid/esm/PencilSquareIcon.js
var React245 = __toESM(require_react(), 1);
function PencilSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React245.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React245.createElement("title", {
    id: titleId
  }, title) : null, React245.createElement("path", {
    d: "M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32l8.4-8.4Z"
  }), React245.createElement("path", {
    d: "M5.25 5.25a3 3 0 0 0-3 3v10.5a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3V13.5a.75.75 0 0 0-1.5 0v5.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5V8.25a1.5 1.5 0 0 1 1.5-1.5h5.25a.75.75 0 0 0 0-1.5H5.25Z"
  }));
}
var ForwardRef245 = React245.forwardRef(PencilSquareIcon);
var PencilSquareIcon_default = ForwardRef245;

// node_modules/@heroicons/react/24/solid/esm/PencilIcon.js
var React246 = __toESM(require_react(), 1);
function PencilIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React246.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React246.createElement("title", {
    id: titleId
  }, title) : null, React246.createElement("path", {
    d: "M21.731 2.269a2.625 2.625 0 0 0-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 0 0 0-3.712ZM19.513 8.199l-3.712-3.712-12.15 12.15a5.25 5.25 0 0 0-1.32 2.214l-.8 2.685a.75.75 0 0 0 .933.933l2.685-.8a5.25 5.25 0 0 0 2.214-1.32L19.513 8.2Z"
  }));
}
var ForwardRef246 = React246.forwardRef(PencilIcon);
var PencilIcon_default = ForwardRef246;

// node_modules/@heroicons/react/24/solid/esm/PercentBadgeIcon.js
var React247 = __toESM(require_react(), 1);
function PercentBadgeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React247.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React247.createElement("title", {
    id: titleId
  }, title) : null, React247.createElement("path", {
    fillRule: "evenodd",
    d: "M11.99 2.243a4.49 4.49 0 0 0-3.398 1.55 4.49 4.49 0 0 0-3.497 1.306 4.491 4.491 0 0 0-1.307 3.498 4.491 4.491 0 0 0-1.548 3.397c0 1.357.6 2.573 1.548 3.397a4.491 4.491 0 0 0 1.307 3.498 4.49 4.49 0 0 0 3.498 1.307 4.49 4.49 0 0 0 3.397 1.549 4.49 4.49 0 0 0 3.397-1.549 4.49 4.49 0 0 0 3.497-1.307 4.491 4.491 0 0 0 1.306-3.497 4.491 4.491 0 0 0 1.55-3.398c0-1.357-.601-2.573-1.549-3.397a4.491 4.491 0 0 0-1.307-3.498 4.49 4.49 0 0 0-3.498-1.307 4.49 4.49 0 0 0-3.396-1.549Zm3.53 7.28a.75.75 0 0 0-1.06-1.06l-6 6a.75.75 0 1 0 1.06 1.06l6-6Zm-5.78-.905a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Zm4.5 4.5a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef247 = React247.forwardRef(PercentBadgeIcon);
var PercentBadgeIcon_default = ForwardRef247;

// node_modules/@heroicons/react/24/solid/esm/PhoneArrowDownLeftIcon.js
var React248 = __toESM(require_react(), 1);
function PhoneArrowDownLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React248.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React248.createElement("title", {
    id: titleId
  }, title) : null, React248.createElement("path", {
    fillRule: "evenodd",
    d: "M19.5 9.75a.75.75 0 0 1-.75.75h-4.5a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 1 1.5 0v2.69l4.72-4.72a.75.75 0 1 1 1.06 1.06L16.06 9h2.69a.75.75 0 0 1 .75.75Z",
    clipRule: "evenodd"
  }), React248.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef248 = React248.forwardRef(PhoneArrowDownLeftIcon);
var PhoneArrowDownLeftIcon_default = ForwardRef248;

// node_modules/@heroicons/react/24/solid/esm/PhoneArrowUpRightIcon.js
var React249 = __toESM(require_react(), 1);
function PhoneArrowUpRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React249.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React249.createElement("title", {
    id: titleId
  }, title) : null, React249.createElement("path", {
    fillRule: "evenodd",
    d: "M15 3.75a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0V5.56l-4.72 4.72a.75.75 0 1 1-1.06-1.06l4.72-4.72h-2.69a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }), React249.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef249 = React249.forwardRef(PhoneArrowUpRightIcon);
var PhoneArrowUpRightIcon_default = ForwardRef249;

// node_modules/@heroicons/react/24/solid/esm/PhoneXMarkIcon.js
var React250 = __toESM(require_react(), 1);
function PhoneXMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React250.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React250.createElement("title", {
    id: titleId
  }, title) : null, React250.createElement("path", {
    fillRule: "evenodd",
    d: "M15.22 3.22a.75.75 0 0 1 1.06 0L18 4.94l1.72-1.72a.75.75 0 1 1 1.06 1.06L19.06 6l1.72 1.72a.75.75 0 0 1-1.06 1.06L18 7.06l-1.72 1.72a.75.75 0 1 1-1.06-1.06L16.94 6l-1.72-1.72a.75.75 0 0 1 0-1.06ZM1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef250 = React250.forwardRef(PhoneXMarkIcon);
var PhoneXMarkIcon_default = ForwardRef250;

// node_modules/@heroicons/react/24/solid/esm/PhoneIcon.js
var React251 = __toESM(require_react(), 1);
function PhoneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React251.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React251.createElement("title", {
    id: titleId
  }, title) : null, React251.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 4.5a3 3 0 0 1 3-3h1.372c.86 0 1.61.586 1.819 1.42l1.105 4.423a1.875 1.875 0 0 1-.694 1.955l-1.293.97c-.135.101-.164.249-.126.352a11.285 11.285 0 0 0 6.697 6.697c.**************.352-.126l.97-1.293a1.875 1.875 0 0 1 1.955-.694l4.423 1.105c.834.209 1.42.959 1.42 1.82V19.5a3 3 0 0 1-3 3h-2.25C8.552 22.5 1.5 15.448 1.5 6.75V4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef251 = React251.forwardRef(PhoneIcon);
var PhoneIcon_default = ForwardRef251;

// node_modules/@heroicons/react/24/solid/esm/PhotoIcon.js
var React252 = __toESM(require_react(), 1);
function PhotoIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React252.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React252.createElement("title", {
    id: titleId
  }, title) : null, React252.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 6a2.25 2.25 0 0 1 2.25-2.25h16.5A2.25 2.25 0 0 1 22.5 6v12a2.25 2.25 0 0 1-2.25 2.25H3.75A2.25 2.25 0 0 1 1.5 18V6ZM3 16.06V18c0 .414.336.75.75.75h16.5A.75.75 0 0 0 21 18v-1.94l-2.69-2.689a1.5 1.5 0 0 0-2.12 0l-.88.879.97.97a.75.75 0 1 1-1.06 1.06l-5.16-5.159a1.5 1.5 0 0 0-2.12 0L3 16.061Zm10.125-7.81a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef252 = React252.forwardRef(PhotoIcon);
var PhotoIcon_default = ForwardRef252;

// node_modules/@heroicons/react/24/solid/esm/PlayCircleIcon.js
var React253 = __toESM(require_react(), 1);
function PlayCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React253.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React253.createElement("title", {
    id: titleId
  }, title) : null, React253.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm14.024-.983a1.125 1.125 0 0 1 0 1.966l-5.603 3.113A1.125 1.125 0 0 1 9 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef253 = React253.forwardRef(PlayCircleIcon);
var PlayCircleIcon_default = ForwardRef253;

// node_modules/@heroicons/react/24/solid/esm/PlayPauseIcon.js
var React254 = __toESM(require_react(), 1);
function PlayPauseIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React254.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React254.createElement("title", {
    id: titleId
  }, title) : null, React254.createElement("path", {
    d: "M15 6.75a.75.75 0 0 0-.75.75V18a.75.75 0 0 0 .75.75h.75a.75.75 0 0 0 .75-.75V7.5a.75.75 0 0 0-.75-.75H15ZM20.25 6.75a.75.75 0 0 0-.75.75V18c0 .414.336.75.75.75H21a.75.75 0 0 0 .75-.75V7.5a.75.75 0 0 0-.75-.75h-.75ZM5.055 7.06C3.805 6.347 2.25 7.25 2.25 8.69v8.122c0 1.44 1.555 2.343 2.805 1.628l7.108-4.061c1.26-.72 1.26-2.536 0-3.256L5.055 7.061Z"
  }));
}
var ForwardRef254 = React254.forwardRef(PlayPauseIcon);
var PlayPauseIcon_default = ForwardRef254;

// node_modules/@heroicons/react/24/solid/esm/PlayIcon.js
var React255 = __toESM(require_react(), 1);
function PlayIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React255.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React255.createElement("title", {
    id: titleId
  }, title) : null, React255.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef255 = React255.forwardRef(PlayIcon);
var PlayIcon_default = ForwardRef255;

// node_modules/@heroicons/react/24/solid/esm/PlusCircleIcon.js
var React256 = __toESM(require_react(), 1);
function PlusCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React256.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React256.createElement("title", {
    id: titleId
  }, title) : null, React256.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 9a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V15a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef256 = React256.forwardRef(PlusCircleIcon);
var PlusCircleIcon_default = ForwardRef256;

// node_modules/@heroicons/react/24/solid/esm/PlusSmallIcon.js
var React257 = __toESM(require_react(), 1);
function PlusSmallIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React257.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React257.createElement("title", {
    id: titleId
  }, title) : null, React257.createElement("path", {
    fillRule: "evenodd",
    d: "M12 5.25a.75.75 0 0 1 .75.75v5.25H18a.75.75 0 0 1 0 1.5h-5.25V18a.75.75 0 0 1-1.5 0v-5.25H6a.75.75 0 0 1 0-1.5h5.25V6a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef257 = React257.forwardRef(PlusSmallIcon);
var PlusSmallIcon_default = ForwardRef257;

// node_modules/@heroicons/react/24/solid/esm/PlusIcon.js
var React258 = __toESM(require_react(), 1);
function PlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React258.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React258.createElement("title", {
    id: titleId
  }, title) : null, React258.createElement("path", {
    fillRule: "evenodd",
    d: "M12 3.75a.75.75 0 0 1 .75.75v6.75h6.75a.75.75 0 0 1 0 1.5h-6.75v6.75a.75.75 0 0 1-1.5 0v-6.75H4.5a.75.75 0 0 1 0-1.5h6.75V4.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef258 = React258.forwardRef(PlusIcon);
var PlusIcon_default = ForwardRef258;

// node_modules/@heroicons/react/24/solid/esm/PowerIcon.js
var React259 = __toESM(require_react(), 1);
function PowerIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React259.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React259.createElement("title", {
    id: titleId
  }, title) : null, React259.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25a.75.75 0 0 1 .75.75v9a.75.75 0 0 1-1.5 0V3a.75.75 0 0 1 .75-.75ZM6.166 5.106a.75.75 0 0 1 0 1.06 8.25 8.25 0 1 0 11.668 0 .75.75 0 1 1 1.06-1.06c3.808 3.807 3.808 9.98 0 13.788-3.807 3.808-9.98 3.808-13.788 0-3.808-3.807-3.808-9.98 0-13.788a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef259 = React259.forwardRef(PowerIcon);
var PowerIcon_default = ForwardRef259;

// node_modules/@heroicons/react/24/solid/esm/PresentationChartBarIcon.js
var React260 = __toESM(require_react(), 1);
function PresentationChartBarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React260.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React260.createElement("title", {
    id: titleId
  }, title) : null, React260.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 2.25a.75.75 0 0 0 0 1.5H3v10.5a3 3 0 0 0 3 3h1.21l-1.172 3.513a.75.75 0 0 0 1.424.474l.329-.987h8.418l.33.987a.75.75 0 0 0 1.422-.474l-1.17-3.513H18a3 3 0 0 0 3-3V3.75h.75a.75.75 0 0 0 0-1.5H2.25Zm6.04 16.5.5-1.5h6.42l.5 1.5H8.29Zm7.46-12a.75.75 0 0 0-1.5 0v6a.75.75 0 0 0 1.5 0v-6Zm-3 2.25a.75.75 0 0 0-1.5 0v3.75a.75.75 0 0 0 1.5 0V9Zm-3 2.25a.75.75 0 0 0-1.5 0v1.5a.75.75 0 0 0 1.5 0v-1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef260 = React260.forwardRef(PresentationChartBarIcon);
var PresentationChartBarIcon_default = ForwardRef260;

// node_modules/@heroicons/react/24/solid/esm/PresentationChartLineIcon.js
var React261 = __toESM(require_react(), 1);
function PresentationChartLineIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React261.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React261.createElement("title", {
    id: titleId
  }, title) : null, React261.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 2.25a.75.75 0 0 0 0 1.5H3v10.5a3 3 0 0 0 3 3h1.21l-1.172 3.513a.75.75 0 0 0 1.424.474l.329-.987h8.418l.33.987a.75.75 0 0 0 1.422-.474l-1.17-3.513H18a3 3 0 0 0 3-3V3.75h.75a.75.75 0 0 0 0-1.5H2.25Zm6.54 15h6.42l.5 1.5H8.29l.5-1.5Zm8.085-8.995a.75.75 0 1 0-.75-1.299 12.81 12.81 0 0 0-3.558 3.05L11.03 8.47a.75.75 0 0 0-1.06 0l-3 3a.75.75 0 1 0 1.06 1.06l2.47-2.47 1.617 1.618a.75.75 0 0 0 1.146-.102 11.312 11.312 0 0 1 3.612-3.321Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef261 = React261.forwardRef(PresentationChartLineIcon);
var PresentationChartLineIcon_default = ForwardRef261;

// node_modules/@heroicons/react/24/solid/esm/PrinterIcon.js
var React262 = __toESM(require_react(), 1);
function PrinterIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React262.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React262.createElement("title", {
    id: titleId
  }, title) : null, React262.createElement("path", {
    fillRule: "evenodd",
    d: "M7.875 1.5C6.839 1.5 6 2.34 6 3.375v2.99c-.426.053-.851.11-1.274.174-1.454.218-2.476 1.483-2.476 2.917v6.294a3 3 0 0 0 3 3h.27l-.155 1.705A1.875 1.875 0 0 0 7.232 22.5h9.536a1.875 1.875 0 0 0 1.867-2.045l-.155-1.705h.27a3 3 0 0 0 3-3V9.456c0-1.434-1.022-2.7-2.476-2.917A48.716 48.716 0 0 0 18 6.366V3.375c0-1.036-.84-1.875-1.875-1.875h-8.25ZM16.5 6.205v-2.83A.375.375 0 0 0 16.125 3h-8.25a.375.375 0 0 0-.375.375v2.83a49.353 49.353 0 0 1 9 0Zm-.217 8.265c.178.018.317.16.333.337l.526 5.784a.375.375 0 0 1-.374.409H7.232a.375.375 0 0 1-.374-.409l.526-5.784a.373.373 0 0 1 .333-.337 41.741 41.741 0 0 1 8.566 0Zm.967-3.97a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H18a.75.75 0 0 1-.75-.75V10.5ZM15 9.75a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V10.5a.75.75 0 0 0-.75-.75H15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef262 = React262.forwardRef(PrinterIcon);
var PrinterIcon_default = ForwardRef262;

// node_modules/@heroicons/react/24/solid/esm/PuzzlePieceIcon.js
var React263 = __toESM(require_react(), 1);
function PuzzlePieceIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React263.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React263.createElement("title", {
    id: titleId
  }, title) : null, React263.createElement("path", {
    d: "M11.25 5.337c0-.355-.186-.676-.401-.959a1.647 1.647 0 0 1-.349-1.003c0-1.036 1.007-1.875 2.25-1.875S15 2.34 15 3.375c0 .369-.128.713-.349 1.003-.215.283-.401.604-.401.959 0 .332.278.598.61.578 1.91-.114 3.79-.342 5.632-.676a.75.75 0 0 1 .878.645 49.17 49.17 0 0 1 .376 5.452.657.657 0 0 1-.66.664c-.354 0-.675-.186-.958-.401a1.647 1.647 0 0 0-1.003-.349c-1.035 0-1.875 1.007-1.875 2.25s.84 2.25 1.875 2.25c.369 0 .713-.128 1.003-.349.283-.215.604-.401.959-.401.31 0 .557.262.534.571a48.774 48.774 0 0 1-.595 4.845.75.75 0 0 1-.61.61c-1.82.317-3.673.533-5.555.642a.58.58 0 0 1-.611-.581c0-.355.186-.676.401-.959.221-.29.349-.634.349-1.003 0-1.035-1.007-1.875-2.25-1.875s-2.25.84-2.25 1.875c0 .369.128.713.349 1.003.215.283.401.604.401.959a.641.641 0 0 1-.658.643 49.118 49.118 0 0 1-4.708-.36.75.75 0 0 1-.645-.878c.293-1.614.504-3.257.629-4.924A.53.53 0 0 0 5.337 15c-.355 0-.676.186-.959.401-.29.221-.634.349-1.003.349-1.036 0-1.875-1.007-1.875-2.25s.84-2.25 1.875-2.25c.369 0 .713.128 1.003.349.283.215.604.401.959.401a.656.656 0 0 0 .659-.663 47.703 47.703 0 0 0-.31-4.82.75.75 0 0 1 .83-.832c1.343.155 2.703.254 4.077.294a.64.64 0 0 0 .657-.642Z"
  }));
}
var ForwardRef263 = React263.forwardRef(PuzzlePieceIcon);
var PuzzlePieceIcon_default = ForwardRef263;

// node_modules/@heroicons/react/24/solid/esm/QrCodeIcon.js
var React264 = __toESM(require_react(), 1);
function QrCodeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React264.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React264.createElement("title", {
    id: titleId
  }, title) : null, React264.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4.875C3 3.839 3.84 3 4.875 3h4.5c1.036 0 1.875.84 1.875 1.875v4.5c0 1.036-.84 1.875-1.875 1.875h-4.5A1.875 1.875 0 0 1 3 9.375v-4.5ZM4.875 4.5a.375.375 0 0 0-.375.375v4.5c0 .207.168.375.375.375h4.5a.375.375 0 0 0 .375-.375v-4.5a.375.375 0 0 0-.375-.375h-4.5Zm7.875.375c0-1.036.84-1.875 1.875-1.875h4.5C20.16 3 21 3.84 21 4.875v4.5c0 1.036-.84 1.875-1.875 1.875h-4.5a1.875 1.875 0 0 1-1.875-1.875v-4.5Zm1.875-.375a.375.375 0 0 0-.375.375v4.5c0 .207.168.375.375.375h4.5a.375.375 0 0 0 .375-.375v-4.5a.375.375 0 0 0-.375-.375h-4.5ZM6 6.75A.75.75 0 0 1 6.75 6h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75A.75.75 0 0 1 6 7.5v-.75Zm9.75 0A.75.75 0 0 1 16.5 6h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75ZM3 14.625c0-1.036.84-1.875 1.875-1.875h4.5c1.036 0 1.875.84 1.875 1.875v4.5c0 1.035-.84 1.875-1.875 1.875h-4.5A1.875 1.875 0 0 1 3 19.125v-4.5Zm1.875-.375a.375.375 0 0 0-.375.375v4.5c0 .207.168.375.375.375h4.5a.375.375 0 0 0 .375-.375v-4.5a.375.375 0 0 0-.375-.375h-4.5Zm7.875-.75a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75Zm6 0a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75ZM6 16.5a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75Zm9.75 0a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75Zm-3 3a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75Zm6 0a.75.75 0 0 1 .75-.75h.75a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef264 = React264.forwardRef(QrCodeIcon);
var QrCodeIcon_default = ForwardRef264;

// node_modules/@heroicons/react/24/solid/esm/QuestionMarkCircleIcon.js
var React265 = __toESM(require_react(), 1);
function QuestionMarkCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React265.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React265.createElement("title", {
    id: titleId
  }, title) : null, React265.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm11.378-3.917c-.89-.777-2.366-.777-3.255 0a.75.75 0 0 1-.988-1.129c1.454-1.272 3.776-1.272 5.23 0 1.513 1.324 1.513 3.518 0 4.842a3.75 3.75 0 0 1-.837.552c-.676.328-1.028.774-1.028 1.152v.75a.75.75 0 0 1-1.5 0v-.75c0-1.279 1.06-2.107 1.875-2.502.182-.088.351-.199.503-.331.83-.727.83-1.857 0-2.584ZM12 18a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef265 = React265.forwardRef(QuestionMarkCircleIcon);
var QuestionMarkCircleIcon_default = ForwardRef265;

// node_modules/@heroicons/react/24/solid/esm/QueueListIcon.js
var React266 = __toESM(require_react(), 1);
function QueueListIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React266.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React266.createElement("title", {
    id: titleId
  }, title) : null, React266.createElement("path", {
    d: "M5.625 3.75a2.625 2.625 0 1 0 0 5.25h12.75a2.625 2.625 0 0 0 0-5.25H5.625ZM3.75 11.25a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75ZM3 15.75a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75ZM3.75 18.75a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75Z"
  }));
}
var ForwardRef266 = React266.forwardRef(QueueListIcon);
var QueueListIcon_default = ForwardRef266;

// node_modules/@heroicons/react/24/solid/esm/RadioIcon.js
var React267 = __toESM(require_react(), 1);
function RadioIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React267.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React267.createElement("title", {
    id: titleId
  }, title) : null, React267.createElement("path", {
    fillRule: "evenodd",
    d: "M20.432 4.103a.75.75 0 0 0-.364-1.456L4.128 6.632l-.2.033C2.498 6.904 1.5 8.158 1.5 9.574v9.176a3 3 0 0 0 3 3h15a3 3 0 0 0 3-3V9.574c0-1.416-.997-2.67-2.429-2.909a49.017 49.017 0 0 0-7.255-.658l7.616-1.904Zm-9.585 8.56a.75.75 0 0 1 0 1.06l-.005.006a.75.75 0 0 1-1.06 0l-.006-.006a.75.75 0 0 1 0-1.06l.005-.005a.75.75 0 0 1 1.06 0l.006.005ZM9.781 15.85a.75.75 0 0 0 1.061 0l.005-.005a.75.75 0 0 0 0-1.061l-.005-.005a.75.75 0 0 0-1.06 0l-.006.005a.75.75 0 0 0 0 1.06l.005.006Zm-1.055-1.066a.75.75 0 0 1 0 1.06l-.005.006a.75.75 0 0 1-1.061 0l-.005-.005a.75.75 0 0 1 0-1.06l.005-.006a.75.75 0 0 1 1.06 0l.006.005ZM7.66 13.73a.75.75 0 0 0 1.061 0l.005-.006a.75.75 0 0 0 0-1.06l-.005-.006a.75.75 0 0 0-1.06 0l-.006.006a.75.75 0 0 0 0 1.06l.005.006ZM9.255 9.75a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75V10.5a.75.75 0 0 1 .75-.75h.008Zm3.624 3.28a.75.75 0 0 0 .275-1.025L13.15 12a.75.75 0 0 0-1.025-.275l-.006.004a.75.75 0 0 0-.275 1.024l.004.007a.75.75 0 0 0 1.025.274l.006-.003Zm-1.38 5.126a.75.75 0 0 1-1.024-.275l-.004-.006a.75.75 0 0 1 .275-1.025l.006-.004a.75.75 0 0 1 1.025.275l.004.007a.75.75 0 0 1-.275 1.024l-.006.004Zm.282-6.776a.75.75 0 0 0-.274-1.025l-.007-.003a.75.75 0 0 0-1.024.274l-.004.007a.75.75 0 0 0 .274 1.024l.007.004a.75.75 0 0 0 1.024-.275l.004-.006Zm1.369 5.129a.75.75 0 0 1-1.025.274l-.006-.004a.75.75 0 0 1-.275-1.024l.004-.007a.75.75 0 0 1 1.025-.274l.006.004a.75.75 0 0 1 .275 1.024l-.004.007Zm-.145-1.502a.75.75 0 0 0 .75-.75v-.007a.75.75 0 0 0-.75-.75h-.008a.75.75 0 0 0-.75.75v.007c0 .415.336.75.75.75h.008Zm-3.75 2.243a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75V18a.75.75 0 0 1 .75-.75h.008Zm-2.871-.47a.75.75 0 0 0 .274-1.025l-.003-.006a.75.75 0 0 0-1.025-.275l-.006.004a.75.75 0 0 0-.275 1.024l.004.007a.75.75 0 0 0 1.024.274l.007-.003Zm1.366-5.12a.75.75 0 0 1-1.025-.274l-.004-.006a.75.75 0 0 1 .275-1.025l.006-.004a.75.75 0 0 1 1.025.275l.004.006a.75.75 0 0 1-.275 1.025l-.006.004Zm.281 6.215a.75.75 0 0 0-.275-1.024l-.006-.004a.75.75 0 0 0-1.025.274l-.003.007a.75.75 0 0 0 .274 1.024l.007.004a.75.75 0 0 0 1.024-.274l.004-.007Zm-1.376-5.116a.75.75 0 0 1-1.025.274l-.006-.003a.75.75 0 0 1-.275-1.025l.004-.007a.75.75 0 0 1 1.025-.274l.006.004a.75.75 0 0 1 .275 1.024l-.004.007Zm-1.15 2.248a.75.75 0 0 0 .75-.75v-.007a.75.75 0 0 0-.75-.75h-.008a.75.75 0 0 0-.75.75v.007c0 .415.336.75.75.75h.008ZM17.25 10.5a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm1.5 6a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef267 = React267.forwardRef(RadioIcon);
var RadioIcon_default = ForwardRef267;

// node_modules/@heroicons/react/24/solid/esm/ReceiptPercentIcon.js
var React268 = __toESM(require_react(), 1);
function ReceiptPercentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React268.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React268.createElement("title", {
    id: titleId
  }, title) : null, React268.createElement("path", {
    fillRule: "evenodd",
    d: "M12 1.5c-1.921 0-3.816.111-5.68.327-1.497.174-2.57 1.46-2.57 2.93V21.75a.75.75 0 0 0 1.029.696l3.471-1.388 3.472 1.388a.75.75 0 0 0 .556 0l3.472-1.388 3.471 1.388a.75.75 0 0 0 1.029-.696V4.757c0-1.47-1.073-2.756-2.57-2.93A49.255 49.255 0 0 0 12 1.5Zm3.53 7.28a.75.75 0 0 0-1.06-1.06l-6 6a.75.75 0 1 0 1.06 1.06l6-6ZM8.625 9a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Zm5.625 3.375a1.125 1.125 0 1 0 0 2.25 1.125 1.125 0 0 0 0-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef268 = React268.forwardRef(ReceiptPercentIcon);
var ReceiptPercentIcon_default = ForwardRef268;

// node_modules/@heroicons/react/24/solid/esm/ReceiptRefundIcon.js
var React269 = __toESM(require_react(), 1);
function ReceiptRefundIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React269.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React269.createElement("title", {
    id: titleId
  }, title) : null, React269.createElement("path", {
    fillRule: "evenodd",
    d: "M12 1.5c-1.921 0-3.816.111-5.68.327-1.497.174-2.57 1.46-2.57 2.93V21.75a.75.75 0 0 0 1.029.696l3.471-1.388 3.472 1.388a.75.75 0 0 0 .556 0l3.472-1.388 3.471 1.388a.75.75 0 0 0 1.029-.696V4.757c0-1.47-1.073-2.756-2.57-2.93A49.255 49.255 0 0 0 12 1.5Zm-.97 6.53a.75.75 0 1 0-1.06-1.06L7.72 9.22a.75.75 0 0 0 0 1.06l2.25 2.25a.75.75 0 1 0 1.06-1.06l-.97-.97h3.065a1.875 1.875 0 0 1 0 3.75H12a.75.75 0 0 0 0 1.5h1.125a3.375 3.375 0 1 0 0-6.75h-3.064l.97-.97Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef269 = React269.forwardRef(ReceiptRefundIcon);
var ReceiptRefundIcon_default = ForwardRef269;

// node_modules/@heroicons/react/24/solid/esm/RectangleGroupIcon.js
var React270 = __toESM(require_react(), 1);
function RectangleGroupIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React270.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React270.createElement("title", {
    id: titleId
  }, title) : null, React270.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 7.125c0-1.036.84-1.875 1.875-1.875h6c1.036 0 1.875.84 1.875 1.875v3.75c0 1.036-.84 1.875-1.875 1.875h-6A1.875 1.875 0 0 1 1.5 10.875v-3.75Zm12 1.5c0-1.036.84-1.875 1.875-1.875h5.25c1.035 0 1.875.84 1.875 1.875v8.25c0 1.035-.84 1.875-1.875 1.875h-5.25a1.875 1.875 0 0 1-1.875-1.875v-8.25ZM3 16.125c0-1.036.84-1.875 1.875-1.875h5.25c1.036 0 1.875.84 1.875 1.875v2.25c0 1.035-.84 1.875-1.875 1.875h-5.25A1.875 1.875 0 0 1 3 18.375v-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef270 = React270.forwardRef(RectangleGroupIcon);
var RectangleGroupIcon_default = ForwardRef270;

// node_modules/@heroicons/react/24/solid/esm/RectangleStackIcon.js
var React271 = __toESM(require_react(), 1);
function RectangleStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React271.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React271.createElement("title", {
    id: titleId
  }, title) : null, React271.createElement("path", {
    d: "M5.566 4.657A4.505 4.505 0 0 1 6.75 4.5h10.5c.41 0 .806.055 1.183.157A3 3 0 0 0 15.75 3h-7.5a3 3 0 0 0-2.684 1.657ZM2.25 12a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3v-6ZM5.25 7.5c-.41 0-.806.055-1.184.157A3 3 0 0 1 6.75 6h10.5a3 3 0 0 1 2.683 1.657A4.505 4.505 0 0 0 18.75 7.5H5.25Z"
  }));
}
var ForwardRef271 = React271.forwardRef(RectangleStackIcon);
var RectangleStackIcon_default = ForwardRef271;

// node_modules/@heroicons/react/24/solid/esm/RocketLaunchIcon.js
var React272 = __toESM(require_react(), 1);
function RocketLaunchIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React272.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React272.createElement("title", {
    id: titleId
  }, title) : null, React272.createElement("path", {
    fillRule: "evenodd",
    d: "M9.315 7.584C12.195 3.883 16.695 1.5 21.75 1.5a.75.75 0 0 1 .75.75c0 5.056-2.383 9.555-6.084 12.436A6.75 6.75 0 0 1 9.75 22.5a.75.75 0 0 1-.75-.75v-4.131A15.838 15.838 0 0 1 6.382 15H2.25a.75.75 0 0 1-.75-.75 6.75 6.75 0 0 1 7.815-6.666ZM15 6.75a2.25 2.25 0 1 0 0 4.5 2.25 2.25 0 0 0 0-4.5Z",
    clipRule: "evenodd"
  }), React272.createElement("path", {
    d: "M5.26 17.242a.75.75 0 1 0-.897-1.203 5.243 5.243 0 0 0-2.05 *********** 0 0 0 .625.627 5.243 5.243 0 0 0 5.022-*********** 0 1 0-1.202-.897 3.744 3.744 0 0 1-3.008 1.51c0-1.23.592-2.323 1.51-3.008Z"
  }));
}
var ForwardRef272 = React272.forwardRef(RocketLaunchIcon);
var RocketLaunchIcon_default = ForwardRef272;

// node_modules/@heroicons/react/24/solid/esm/RssIcon.js
var React273 = __toESM(require_react(), 1);
function RssIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React273.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React273.createElement("title", {
    id: titleId
  }, title) : null, React273.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 4.5a.75.75 0 0 1 .75-.75h.75c8.284 0 15 6.716 15 15v.75a.75.75 0 0 1-.75.75h-.75a.75.75 0 0 1-.75-.75v-.75C18 11.708 12.292 6 5.25 6H4.5a.75.75 0 0 1-.75-.75V4.5Zm0 6.75a.75.75 0 0 1 .75-.75h.75a8.25 8.25 0 0 1 8.25 8.25v.75a.75.75 0 0 1-.75.75H12a.75.75 0 0 1-.75-.75v-.75a6 6 0 0 0-6-6H4.5a.75.75 0 0 1-.75-.75v-.75Zm0 7.5a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef273 = React273.forwardRef(RssIcon);
var RssIcon_default = ForwardRef273;

// node_modules/@heroicons/react/24/solid/esm/ScaleIcon.js
var React274 = __toESM(require_react(), 1);
function ScaleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React274.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React274.createElement("title", {
    id: titleId
  }, title) : null, React274.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25a.75.75 0 0 1 .75.75v.756a49.106 49.106 0 0 1 9.152 1 .75.75 0 0 1-.152 1.485h-1.918l2.474 10.124a.75.75 0 0 1-.375.84A6.723 6.723 0 0 1 18.75 18a6.723 6.723 0 0 1-3.181-.795.75.75 0 0 1-.375-.84l2.474-10.124H12.75v13.28c1.293.076 2.534.343 3.697.776a.75.75 0 0 1-.262 1.453h-8.37a.75.75 0 0 1-.262-1.453c1.162-.433 2.404-.7 3.697-.775V6.24H6.332l2.474 10.124a.75.75 0 0 1-.375.84A6.723 6.723 0 0 1 5.25 18a6.723 6.723 0 0 1-3.181-.795.75.75 0 0 1-.375-.84L4.168 6.241H2.25a.75.75 0 0 1-.152-1.485 49.105 49.105 0 0 1 9.152-1V3a.75.75 0 0 1 .75-.75Zm4.878 13.543 1.872-7.662 1.872 7.662h-3.744Zm-9.756 0L5.25 8.131l-1.872 7.662h3.744Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef274 = React274.forwardRef(ScaleIcon);
var ScaleIcon_default = ForwardRef274;

// node_modules/@heroicons/react/24/solid/esm/ScissorsIcon.js
var React275 = __toESM(require_react(), 1);
function ScissorsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React275.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React275.createElement("title", {
    id: titleId
  }, title) : null, React275.createElement("path", {
    fillRule: "evenodd",
    d: "M8.128 9.155a3.751 3.751 0 1 1 .713-1.321l1.136.656a.75.75 0 0 1 .222 1.104l-.006.007a.75.75 0 0 1-1.032.157 1.421 1.421 0 0 0-.113-.072l-.92-.531Zm-4.827-3.53a2.25 2.25 0 0 1 3.994 2.063.756.756 0 0 0-.122.23 2.25 2.25 0 0 1-3.872-2.293ZM13.348 8.272a5.073 5.073 0 0 0-3.428 3.57 5.08 5.08 0 0 0-.165 1.202 1.415 1.415 0 0 1-.707 1.201l-.96.554a3.751 3.751 0 1 0 .734 1.309l13.729-7.926a.75.75 0 0 0-.181-1.374l-.803-.215a5.25 5.25 0 0 0-2.894.05l-5.325 1.629Zm-9.223 7.03a2.25 2.25 0 1 0 2.25 3.897 2.25 2.25 0 0 0-2.25-3.897ZM12 12.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5Z",
    clipRule: "evenodd"
  }), React275.createElement("path", {
    d: "M16.372 12.615a.75.75 0 0 1 .75 0l5.43 3.135a.75.75 0 0 1-.182 1.374l-.802.215a5.25 5.25 0 0 1-2.894-.051l-5.147-1.574a.75.75 0 0 1-.156-1.367l3-1.732Z"
  }));
}
var ForwardRef275 = React275.forwardRef(ScissorsIcon);
var ScissorsIcon_default = ForwardRef275;

// node_modules/@heroicons/react/24/solid/esm/ServerStackIcon.js
var React276 = __toESM(require_react(), 1);
function ServerStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React276.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React276.createElement("title", {
    id: titleId
  }, title) : null, React276.createElement("path", {
    d: "M5.507 4.048A3 3 0 0 1 7.785 3h8.43a3 3 0 0 1 2.278 1.048l1.722 2.008A4.533 4.533 0 0 0 19.5 6h-15c-.243 0-.482.02-.715.056l1.722-2.008Z"
  }), React276.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 10.5a3 3 0 0 1 3-3h15a3 3 0 1 1 0 6h-15a3 3 0 0 1-3-3Zm15 0a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Zm2.25.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM4.5 15a3 3 0 1 0 0 6h15a3 3 0 1 0 0-6h-15Zm11.25 3.75a.75.75 0 1 0 0-********* 0 0 0 0 1.5ZM19.5 18a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef276 = React276.forwardRef(ServerStackIcon);
var ServerStackIcon_default = ForwardRef276;

// node_modules/@heroicons/react/24/solid/esm/ServerIcon.js
var React277 = __toESM(require_react(), 1);
function ServerIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React277.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React277.createElement("title", {
    id: titleId
  }, title) : null, React277.createElement("path", {
    d: "M4.08 5.227A3 3 0 0 1 6.979 3H17.02a3 3 0 0 1 2.9 2.227l2.113 7.926A5.228 5.228 0 0 0 18.75 12H5.25a5.228 5.228 0 0 0-3.284 1.153L4.08 5.227Z"
  }), React277.createElement("path", {
    fillRule: "evenodd",
    d: "M5.25 13.5a3.75 3.75 0 1 0 0 7.5h13.5a3.75 3.75 0 1 0 0-7.5H5.25Zm10.5 4.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm3.75-.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef277 = React277.forwardRef(ServerIcon);
var ServerIcon_default = ForwardRef277;

// node_modules/@heroicons/react/24/solid/esm/ShareIcon.js
var React278 = __toESM(require_react(), 1);
function ShareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React278.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React278.createElement("title", {
    id: titleId
  }, title) : null, React278.createElement("path", {
    fillRule: "evenodd",
    d: "M15.75 4.5a3 3 0 1 1 .825 2.066l-8.421 4.679a3.002 3.002 0 0 1 0 1.51l8.421 4.679a3 3 0 1 1-.729 1.31l-8.421-4.678a3 3 0 1 1 0-4.132l8.421-4.679a3 3 0 0 1-.096-.755Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef278 = React278.forwardRef(ShareIcon);
var ShareIcon_default = ForwardRef278;

// node_modules/@heroicons/react/24/solid/esm/ShieldCheckIcon.js
var React279 = __toESM(require_react(), 1);
function ShieldCheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React279.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React279.createElement("title", {
    id: titleId
  }, title) : null, React279.createElement("path", {
    fillRule: "evenodd",
    d: "M12.516 2.17a.75.75 0 0 0-1.032 0 11.209 11.209 0 0 1-7.877 ********** 0 0 0-.722.515A12.74 12.74 0 0 0 2.25 9.75c0 5.942 4.064 10.933 9.563 12.348a.749.749 0 0 0 .374 0c5.499-1.415 9.563-6.406 9.563-12.348 0-1.39-.223-2.73-.635-3.985a.75.75 0 0 0-.722-.516l-.143.001c-2.996 0-5.717-1.17-7.734-3.08Zm3.094 8.016a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef279 = React279.forwardRef(ShieldCheckIcon);
var ShieldCheckIcon_default = ForwardRef279;

// node_modules/@heroicons/react/24/solid/esm/ShieldExclamationIcon.js
var React280 = __toESM(require_react(), 1);
function ShieldExclamationIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React280.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React280.createElement("title", {
    id: titleId
  }, title) : null, React280.createElement("path", {
    fillRule: "evenodd",
    d: "M11.484 2.17a.75.75 0 0 1 1.032 0 11.209 11.209 0 0 0 7.877 ********** 0 0 1 .722.515 12.74 12.74 0 0 1 .635 3.985c0 5.942-4.064 10.933-9.563 12.348a.749.749 0 0 1-.374 0C6.314 20.683 2.25 15.692 2.25 9.75c0-1.39.223-2.73.635-3.985a.75.75 0 0 1 .722-.516l.143.001c2.996 0 5.718-1.17 7.734-3.08ZM12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75ZM12 15a.75.75 0 0 0-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75v-.008a.75.75 0 0 0-.75-.75H12Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef280 = React280.forwardRef(ShieldExclamationIcon);
var ShieldExclamationIcon_default = ForwardRef280;

// node_modules/@heroicons/react/24/solid/esm/ShoppingBagIcon.js
var React281 = __toESM(require_react(), 1);
function ShoppingBagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React281.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React281.createElement("title", {
    id: titleId
  }, title) : null, React281.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 6v.75H5.513c-.96 0-1.764.724-1.865 1.679l-1.263 12A1.875 1.875 0 0 0 4.25 22.5h15.5a1.875 1.875 0 0 0 1.865-2.071l-1.263-12a1.875 1.875 0 0 0-1.865-1.679H16.5V6a4.5 4.5 0 1 0-9 0ZM12 3a3 3 0 0 0-3 3v.75h6V6a3 3 0 0 0-3-3Zm-3 8.25a3 3 0 1 0 6 0v-.75a.75.75 0 0 1 1.5 0v.75a4.5 4.5 0 1 1-9 0v-.75a.75.75 0 0 1 1.5 0v.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef281 = React281.forwardRef(ShoppingBagIcon);
var ShoppingBagIcon_default = ForwardRef281;

// node_modules/@heroicons/react/24/solid/esm/ShoppingCartIcon.js
var React282 = __toESM(require_react(), 1);
function ShoppingCartIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React282.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React282.createElement("title", {
    id: titleId
  }, title) : null, React282.createElement("path", {
    d: "M2.25 2.25a.75.75 0 0 0 0 1.5h1.386c.17 0 .318.114.362.278l2.558 9.592a3.752 3.752 0 0 0-2.806 3.63c0 .414.336.75.75.75h15.75a.75.75 0 0 0 0-1.5H5.378A2.25 2.25 0 0 1 7.5 15h11.218a.75.75 0 0 0 .674-.421 60.358 60.358 0 0 0 2.96-*********** 0 0 0-.525-.965A60.864 60.864 0 0 0 5.68 4.509l-.232-.867A1.875 1.875 0 0 0 3.636 2.25H2.25ZM3.75 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM16.5 20.25a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
  }));
}
var ForwardRef282 = React282.forwardRef(ShoppingCartIcon);
var ShoppingCartIcon_default = ForwardRef282;

// node_modules/@heroicons/react/24/solid/esm/SignalSlashIcon.js
var React283 = __toESM(require_react(), 1);
function SignalSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React283.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React283.createElement("title", {
    id: titleId
  }, title) : null, React283.createElement("path", {
    fillRule: "evenodd",
    d: "M2.47 2.47a.75.75 0 0 1 1.06 0l8.407 8.407a1.125 1.125 0 0 1 1.186 1.186l1.462 1.461a3.001 3.001 0 0 0-.464-3.645.75.75 0 1 1 1.061-1.061 4.501 4.501 0 0 1 .486 5.79l1.072 1.072a6.001 6.001 0 0 0-.497-7.923.75.75 0 0 1 1.06-1.06 7.501 7.501 0 0 1 .505 10.05l1.064 1.065a9 9 0 0 0-.508-12.176.75.75 0 0 1 1.06-1.06c3.923 3.922 4.093 10.175.512 14.3l1.594 1.594a.75.75 0 1 1-1.06 1.06l-2.106-2.105-2.121-2.122h-.001l-4.705-4.706a.747.747 0 0 1-.127-.126L2.47 3.53a.75.75 0 0 1 0-1.061Zm1.189 4.422a.75.75 0 0 1 .326 1.01 9.004 9.004 0 0 0 1.651 10.462.75.75 0 1 1-1.06 1.06C1.27 16.12.63 11.165 2.648 7.219a.75.75 0 0 1 1.01-.326ZM5.84 9.134a.75.75 0 0 1 .472.95 6 6 0 0 0 1.444 *********** 0 0 1-1.06 1.06A7.5 7.5 0 0 1 4.89 9.606a.75.75 0 0 1 .95-.472Zm2.341 2.653a.75.75 0 0 1 .848.638c.088.62.37 1.218.849 1.696a.75.75 0 0 1-1.061 1.061 4.483 4.483 0 0 1-1.273-2.546.75.75 0 0 1 .637-.848Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef283 = React283.forwardRef(SignalSlashIcon);
var SignalSlashIcon_default = ForwardRef283;

// node_modules/@heroicons/react/24/solid/esm/SignalIcon.js
var React284 = __toESM(require_react(), 1);
function SignalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React284.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React284.createElement("title", {
    id: titleId
  }, title) : null, React284.createElement("path", {
    fillRule: "evenodd",
    d: "M5.636 4.575a.75.75 0 0 1 0 1.061 9 9 0 0 0 0 12.728.75.75 0 1 1-1.06 1.06c-4.101-4.1-4.101-10.748 0-14.849a.75.75 0 0 1 1.06 0Zm12.728 0a.75.75 0 0 1 1.06 0c4.101 4.1 4.101 10.75 0 14.85a.75.75 0 1 1-1.06-1.061 9 9 0 0 0 0-12.728.75.75 0 0 1 0-1.06ZM7.757 6.697a.75.75 0 0 1 0 1.06 6 6 0 0 0 0 8.486.75.75 0 0 1-1.06 1.06 7.5 7.5 0 0 1 0-10.606.75.75 0 0 1 1.06 0Zm8.486 0a.75.75 0 0 1 1.06 0 7.5 7.5 0 0 1 0 10.606.75.75 0 0 1-1.06-1.06 6 6 0 0 0 0-8.486.75.75 0 0 1 0-1.06ZM9.879 8.818a.75.75 0 0 1 0 1.06 3 3 0 0 0 0 *********** 0 1 1-1.061 1.061 4.5 4.5 0 0 1 0-6.364.75.75 0 0 1 1.06 0Zm4.242 0a.75.75 0 0 1 1.061 0 4.5 4.5 0 0 1 0 6.364.75.75 0 0 1-1.06-1.06 3 3 0 0 0 0-*********** 0 0 1 0-1.061ZM10.875 12a1.125 1.125 0 1 1 2.25 0 1.125 1.125 0 0 1-2.25 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef284 = React284.forwardRef(SignalIcon);
var SignalIcon_default = ForwardRef284;

// node_modules/@heroicons/react/24/solid/esm/SlashIcon.js
var React285 = __toESM(require_react(), 1);
function SlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React285.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React285.createElement("title", {
    id: titleId
  }, title) : null, React285.createElement("path", {
    fillRule: "evenodd",
    d: "M15.256 3.042a.75.75 0 0 1 .449.962l-6 16.5a.75.75 0 1 1-1.41-.513l6-16.5a.75.75 0 0 1 .961-.449Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef285 = React285.forwardRef(SlashIcon);
var SlashIcon_default = ForwardRef285;

// node_modules/@heroicons/react/24/solid/esm/SparklesIcon.js
var React286 = __toESM(require_react(), 1);
function SparklesIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React286.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React286.createElement("title", {
    id: titleId
  }, title) : null, React286.createElement("path", {
    fillRule: "evenodd",
    d: "M9 4.5a.75.75 0 0 1 .721.544l.813 2.846a3.75 3.75 0 0 0 2.576 2.576l2.846.813a.75.75 0 0 1 0 1.442l-2.846.813a3.75 3.75 0 0 0-2.576 2.576l-.813 2.846a.75.75 0 0 1-1.442 0l-.813-2.846a3.75 3.75 0 0 0-2.576-2.576l-2.846-.813a.75.75 0 0 1 0-1.442l2.846-.813A3.75 3.75 0 0 0 7.466 7.89l.813-2.846A.75.75 0 0 1 9 4.5ZM18 1.5a.75.75 0 0 1 .728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 0 1 0 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 0 1-1.456 0l-.258-1.036a2.625 2.625 0 0 0-1.91-1.91l-1.036-.258a.75.75 0 0 1 0-1.456l1.036-.258a2.625 2.625 0 0 0 1.91-1.91l.258-1.036A.75.75 0 0 1 18 1.5ZM16.5 15a.75.75 0 0 1 .712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 0 1 0 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 0 1-1.422 0l-.395-1.183a1.5 1.5 0 0 0-.948-.948l-1.183-.395a.75.75 0 0 1 0-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0 1 16.5 15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef286 = React286.forwardRef(SparklesIcon);
var SparklesIcon_default = ForwardRef286;

// node_modules/@heroicons/react/24/solid/esm/SpeakerWaveIcon.js
var React287 = __toESM(require_react(), 1);
function SpeakerWaveIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React287.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React287.createElement("title", {
    id: titleId
  }, title) : null, React287.createElement("path", {
    d: "M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 0 0 1.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.659 1.905h1.93l4.5 4.5c.945.945 2.561.276 2.561-1.06V4.06ZM18.584 5.106a.75.75 0 0 1 1.06 0c3.808 3.807 3.808 9.98 0 13.788a.75.75 0 0 1-1.06-1.06 8.25 8.25 0 0 0 0-11.668.75.75 0 0 1 0-1.06Z"
  }), React287.createElement("path", {
    d: "M15.932 7.757a.75.75 0 0 1 1.061 0 6 6 0 0 1 0 8.486.75.75 0 0 1-1.06-1.061 4.5 4.5 0 0 0 0-6.364.75.75 0 0 1 0-1.06Z"
  }));
}
var ForwardRef287 = React287.forwardRef(SpeakerWaveIcon);
var SpeakerWaveIcon_default = ForwardRef287;

// node_modules/@heroicons/react/24/solid/esm/SpeakerXMarkIcon.js
var React288 = __toESM(require_react(), 1);
function SpeakerXMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React288.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React288.createElement("title", {
    id: titleId
  }, title) : null, React288.createElement("path", {
    d: "M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 0 0 1.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.659 1.905h1.93l4.5 4.5c.945.945 2.561.276 2.561-1.06V4.06ZM17.78 9.22a.75.75 0 1 0-1.06 1.06L18.44 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06l1.72-1.72 1.72 1.72a.75.75 0 1 0 1.06-1.06L20.56 12l1.72-1.72a.75.75 0 1 0-1.06-1.06l-1.72 1.72-1.72-1.72Z"
  }));
}
var ForwardRef288 = React288.forwardRef(SpeakerXMarkIcon);
var SpeakerXMarkIcon_default = ForwardRef288;

// node_modules/@heroicons/react/24/solid/esm/Square2StackIcon.js
var React289 = __toESM(require_react(), 1);
function Square2StackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React289.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React289.createElement("title", {
    id: titleId
  }, title) : null, React289.createElement("path", {
    d: "M16.5 6a3 3 0 0 0-3-3H6a3 3 0 0 0-3 3v7.5a3 3 0 0 0 3 3v-6A4.5 4.5 0 0 1 10.5 6h6Z"
  }), React289.createElement("path", {
    d: "M18 7.5a3 3 0 0 1 3 3V18a3 3 0 0 1-3 3h-7.5a3 3 0 0 1-3-3v-7.5a3 3 0 0 1 3-3H18Z"
  }));
}
var ForwardRef289 = React289.forwardRef(Square2StackIcon);
var Square2StackIcon_default = ForwardRef289;

// node_modules/@heroicons/react/24/solid/esm/Square3Stack3DIcon.js
var React290 = __toESM(require_react(), 1);
function Square3Stack3DIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React290.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React290.createElement("title", {
    id: titleId
  }, title) : null, React290.createElement("path", {
    d: "M11.644 1.59a.75.75 0 0 1 .712 0l9.75 5.25a.75.75 0 0 1 0 1.32l-9.75 5.25a.75.75 0 0 1-.712 0l-9.75-5.25a.75.75 0 0 1 0-1.32l9.75-5.25Z"
  }), React290.createElement("path", {
    d: "m3.265 10.602 7.668 4.129a2.25 2.25 0 0 0 2.134 0l7.668-4.13 1.37.739a.75.75 0 0 1 0 1.32l-9.75 5.25a.75.75 0 0 1-.71 0l-9.75-5.25a.75.75 0 0 1 0-1.32l1.37-.738Z"
  }), React290.createElement("path", {
    d: "m10.933 19.231-7.668-4.13-1.37.739a.75.75 0 0 0 0 1.32l9.75 5.25c.221.12.489.12.71 0l9.75-5.25a.75.75 0 0 0 0-1.32l-1.37-.738-7.668 4.13a2.25 2.25 0 0 1-2.134-.001Z"
  }));
}
var ForwardRef290 = React290.forwardRef(Square3Stack3DIcon);
var Square3Stack3DIcon_default = ForwardRef290;

// node_modules/@heroicons/react/24/solid/esm/Squares2X2Icon.js
var React291 = __toESM(require_react(), 1);
function Squares2X2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React291.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React291.createElement("title", {
    id: titleId
  }, title) : null, React291.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6a3 3 0 0 1 3-3h2.25a3 3 0 0 1 3 3v2.25a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V6Zm9.75 0a3 3 0 0 1 3-3H18a3 3 0 0 1 3 3v2.25a3 3 0 0 1-3 3h-2.25a3 3 0 0 1-3-3V6ZM3 15.75a3 3 0 0 1 3-3h2.25a3 3 0 0 1 3 3V18a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-2.25Zm9.75 0a3 3 0 0 1 3-3H18a3 3 0 0 1 3 3V18a3 3 0 0 1-3 3h-2.25a3 3 0 0 1-3-3v-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef291 = React291.forwardRef(Squares2X2Icon);
var Squares2X2Icon_default = ForwardRef291;

// node_modules/@heroicons/react/24/solid/esm/SquaresPlusIcon.js
var React292 = __toESM(require_react(), 1);
function SquaresPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React292.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React292.createElement("title", {
    id: titleId
  }, title) : null, React292.createElement("path", {
    d: "M6 3a3 3 0 0 0-3 3v2.25a3 3 0 0 0 3 3h2.25a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3H6ZM15.75 3a3 3 0 0 0-3 3v2.25a3 3 0 0 0 3 3H18a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3h-2.25ZM6 12.75a3 3 0 0 0-3 3V18a3 3 0 0 0 3 3h2.25a3 3 0 0 0 3-3v-2.25a3 3 0 0 0-3-3H6ZM17.625 13.5a.75.75 0 0 0-1.5 0v2.625H13.5a.75.75 0 0 0 0 1.5h2.625v2.625a.75.75 0 0 0 1.5 0v-2.625h2.625a.75.75 0 0 0 0-1.5h-2.625V13.5Z"
  }));
}
var ForwardRef292 = React292.forwardRef(SquaresPlusIcon);
var SquaresPlusIcon_default = ForwardRef292;

// node_modules/@heroicons/react/24/solid/esm/StarIcon.js
var React293 = __toESM(require_react(), 1);
function StarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React293.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React293.createElement("title", {
    id: titleId
  }, title) : null, React293.createElement("path", {
    fillRule: "evenodd",
    d: "M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef293 = React293.forwardRef(StarIcon);
var StarIcon_default = ForwardRef293;

// node_modules/@heroicons/react/24/solid/esm/StopCircleIcon.js
var React294 = __toESM(require_react(), 1);
function StopCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React294.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React294.createElement("title", {
    id: titleId
  }, title) : null, React294.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm6-2.438c0-.724.588-1.312 1.313-1.312h4.874c.725 0 1.313.588 1.313 1.313v4.874c0 .725-.588 1.313-1.313 1.313H9.564a1.312 1.312 0 0 1-1.313-1.313V9.564Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef294 = React294.forwardRef(StopCircleIcon);
var StopCircleIcon_default = ForwardRef294;

// node_modules/@heroicons/react/24/solid/esm/StopIcon.js
var React295 = __toESM(require_react(), 1);
function StopIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React295.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React295.createElement("title", {
    id: titleId
  }, title) : null, React295.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 7.5a3 3 0 0 1 3-3h9a3 3 0 0 1 3 3v9a3 3 0 0 1-3 3h-9a3 3 0 0 1-3-3v-9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef295 = React295.forwardRef(StopIcon);
var StopIcon_default = ForwardRef295;

// node_modules/@heroicons/react/24/solid/esm/StrikethroughIcon.js
var React296 = __toESM(require_react(), 1);
function StrikethroughIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React296.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React296.createElement("title", {
    id: titleId
  }, title) : null, React296.createElement("path", {
    fillRule: "evenodd",
    d: "M9.657 4.728c-1.086.385-1.766 1.057-1.979 1.85-.214.8.046 1.733.81 2.616.746.862 1.93 1.612 3.388 ************.14.037.21.053h8.163a.75.75 0 0 1 0 1.5h-8.24a.66.66 0 0 1-.02 0H3.75a.75.75 0 0 1 0-1.5h4.78a7.108 7.108 0 0 1-1.175-1.074C6.372 9.042 5.849 7.61 6.229 6.19c.377-1.408 1.528-2.38 2.927-2.876 1.402-.497 3.127-.55 4.855-.086A8.937 8.937 0 0 1 16.94 4.6a.75.75 0 0 1-.881 1.215 7.437 7.437 0 0 0-2.436-1.14c-1.473-.394-2.885-.331-3.966.052Zm6.533 9.632a.75.75 0 0 1 1.03.25c.592.974.846 2.094.55 3.2-.378 1.408-1.529 2.38-2.927 2.876-1.402.497-3.127.55-4.855.087-1.712-.46-3.168-1.354-4.134-2.47a.75.75 0 0 1 1.134-.982c.746.862 1.93 1.612 3.388 2.003 1.473.394 2.884.331 3.966-.052 1.085-.384 1.766-1.056 1.978-1.85.169-.628.046-1.33-.381-2.032a.75.75 0 0 1 .25-1.03Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef296 = React296.forwardRef(StrikethroughIcon);
var StrikethroughIcon_default = ForwardRef296;

// node_modules/@heroicons/react/24/solid/esm/SunIcon.js
var React297 = __toESM(require_react(), 1);
function SunIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React297.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React297.createElement("title", {
    id: titleId
  }, title) : null, React297.createElement("path", {
    d: "M12 2.25a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-1.5 0V3a.75.75 0 0 1 .75-.75ZM7.5 12a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM18.894 6.166a.75.75 0 0 0-1.06-1.06l-1.591 1.59a.75.75 0 1 0 1.06 1.061l1.591-1.59ZM21.75 12a.75.75 0 0 1-.75.75h-2.25a.75.75 0 0 1 0-1.5H21a.75.75 0 0 1 .75.75ZM17.834 18.894a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 1 0-1.061 1.06l1.59 1.591ZM12 18a.75.75 0 0 1 .75.75V21a.75.75 0 0 1-1.5 0v-2.25A.75.75 0 0 1 12 18ZM7.758 17.303a.75.75 0 0 0-1.061-1.06l-1.591 1.59a.75.75 0 0 0 1.06 1.061l1.591-1.59ZM6 12a.75.75 0 0 1-.75.75H3a.75.75 0 0 1 0-1.5h2.25A.75.75 0 0 1 6 12ZM6.697 7.757a.75.75 0 0 0 1.06-1.06l-1.59-1.591a.75.75 0 0 0-1.061 1.06l1.59 1.591Z"
  }));
}
var ForwardRef297 = React297.forwardRef(SunIcon);
var SunIcon_default = ForwardRef297;

// node_modules/@heroicons/react/24/solid/esm/SwatchIcon.js
var React298 = __toESM(require_react(), 1);
function SwatchIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React298.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React298.createElement("title", {
    id: titleId
  }, title) : null, React298.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 4.125c0-1.036.84-1.875 1.875-1.875h5.25c1.036 0 1.875.84 1.875 1.875V17.25a4.5 4.5 0 1 1-9 0V4.125Zm4.5 14.25a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",
    clipRule: "evenodd"
  }), React298.createElement("path", {
    d: "M10.719 21.75h9.156c1.036 0 1.875-.84 1.875-1.875v-5.25c0-1.036-.84-1.875-1.875-1.875h-.14l-8.742 8.743c-.09.089-.18.175-.274.257ZM12.738 17.625l6.474-6.474a1.875 1.875 0 0 0 0-2.651L15.5 4.787a1.875 1.875 0 0 0-2.651 0l-.1.099V17.25c0 .126-.003.251-.01.375Z"
  }));
}
var ForwardRef298 = React298.forwardRef(SwatchIcon);
var SwatchIcon_default = ForwardRef298;

// node_modules/@heroicons/react/24/solid/esm/TableCellsIcon.js
var React299 = __toESM(require_react(), 1);
function TableCellsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React299.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React299.createElement("title", {
    id: titleId
  }, title) : null, React299.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 18.375V5.625ZM21 9.375A.375.375 0 0 0 20.625 9h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 0 0 .375-.375v-1.5Zm0 3.75a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 0 0 .375-.375v-1.5Zm0 3.75a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 0 0 .375-.375v-1.5ZM10.875 18.75a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5ZM3.375 15h7.5a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375Zm0-3.75h7.5a.375.375 0 0 0 .375-.375v-1.5A.375.375 0 0 0 10.875 9h-7.5A.375.375 0 0 0 3 9.375v1.5c0 .207.168.375.375.375Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef299 = React299.forwardRef(TableCellsIcon);
var TableCellsIcon_default = ForwardRef299;

// node_modules/@heroicons/react/24/solid/esm/TagIcon.js
var React300 = __toESM(require_react(), 1);
function TagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React300.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React300.createElement("title", {
    id: titleId
  }, title) : null, React300.createElement("path", {
    fillRule: "evenodd",
    d: "M5.25 2.25a3 3 0 0 0-3 3v4.318a3 3 0 0 0 .879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 0 0 5.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 0 0-2.122-.879H5.25ZM6.375 7.5a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef300 = React300.forwardRef(TagIcon);
var TagIcon_default = ForwardRef300;

// node_modules/@heroicons/react/24/solid/esm/TicketIcon.js
var React301 = __toESM(require_react(), 1);
function TicketIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React301.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React301.createElement("title", {
    id: titleId
  }, title) : null, React301.createElement("path", {
    fillRule: "evenodd",
    d: "M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 0 1-.375.65 2.249 2.249 0 0 0 0 3.898.75.75 0 0 1 .375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 17.625v-3.026a.75.75 0 0 1 .374-.65 2.249 2.249 0 0 0 0-3.898.75.75 0 0 1-.374-.65V6.375Zm15-1.125a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-1.5 0V6a.75.75 0 0 1 .75-.75Zm.75 4.5a.75.75 0 0 0-1.5 0v.75a.75.75 0 0 0 1.5 0v-.75Zm-.75 3a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-1.5 0v-.75a.75.75 0 0 1 .75-.75Zm.75 4.5a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-.75ZM6 12a.75.75 0 0 1 .75-.75H12a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 12Zm.75 2.25a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef301 = React301.forwardRef(TicketIcon);
var TicketIcon_default = ForwardRef301;

// node_modules/@heroicons/react/24/solid/esm/TrashIcon.js
var React302 = __toESM(require_react(), 1);
function TrashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React302.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React302.createElement("title", {
    id: titleId
  }, title) : null, React302.createElement("path", {
    fillRule: "evenodd",
    d: "M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef302 = React302.forwardRef(TrashIcon);
var TrashIcon_default = ForwardRef302;

// node_modules/@heroicons/react/24/solid/esm/TrophyIcon.js
var React303 = __toESM(require_react(), 1);
function TrophyIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React303.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React303.createElement("title", {
    id: titleId
  }, title) : null, React303.createElement("path", {
    fillRule: "evenodd",
    d: "M5.166 2.621v.858c-1.035.148-2.059.33-3.071.543a.75.75 0 0 0-.584.859 6.753 6.753 0 0 0 6.138 5.6 6.73 6.73 0 0 0 2.743 1.346A6.707 6.707 0 0 1 9.279 15H8.54c-1.036 0-1.875.84-1.875 1.875V19.5h-.75a2.25 2.25 0 0 0-2.25 2.25c0 .414.336.75.75.75h15a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-2.25-2.25h-.75v-2.625c0-1.036-.84-1.875-1.875-1.875h-.739a6.706 6.706 0 0 1-1.112-3.173 6.73 6.73 0 0 0 2.743-1.347 6.753 6.753 0 0 0 6.139-********* 0 0 0-.585-.858 47.077 47.077 0 0 0-3.07-.543V2.62a.75.75 0 0 0-.658-.744 49.22 49.22 0 0 0-6.093-.377c-2.063 0-4.096.128-6.093.377a.75.75 0 0 0-.657.744Zm0 2.629c0 1.196.312 2.32.857 3.294A5.266 5.266 0 0 1 3.16 5.337a45.6 45.6 0 0 1 2.006-.343v.256Zm13.5 0v-.256c.674.1 1.343.214 2.006.343a5.265 5.265 0 0 1-2.863 3.207 6.72 6.72 0 0 0 .857-3.294Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef303 = React303.forwardRef(TrophyIcon);
var TrophyIcon_default = ForwardRef303;

// node_modules/@heroicons/react/24/solid/esm/TruckIcon.js
var React304 = __toESM(require_react(), 1);
function TruckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React304.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React304.createElement("title", {
    id: titleId
  }, title) : null, React304.createElement("path", {
    d: "M3.375 4.5C2.339 4.5 1.5 5.34 1.5 6.375V13.5h12V6.375c0-1.036-.84-1.875-1.875-1.875h-8.25ZM13.5 15h-12v2.625c0 1.035.84 1.875 1.875 1.875h.375a3 3 0 1 1 6 0h3a.75.75 0 0 0 .75-.75V15Z"
  }), React304.createElement("path", {
    d: "M8.25 19.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0ZM15.75 6.75a.75.75 0 0 0-.75.75v11.25c0 .087.015.17.042.248a3 3 0 0 1 5.958.464c.853-.175 1.522-.935 1.464-1.883a18.659 18.659 0 0 0-3.732-10.104 1.837 1.837 0 0 0-1.47-.725H15.75Z"
  }), React304.createElement("path", {
    d: "M19.5 19.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0Z"
  }));
}
var ForwardRef304 = React304.forwardRef(TruckIcon);
var TruckIcon_default = ForwardRef304;

// node_modules/@heroicons/react/24/solid/esm/TvIcon.js
var React305 = __toESM(require_react(), 1);
function TvIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React305.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React305.createElement("title", {
    id: titleId
  }, title) : null, React305.createElement("path", {
    d: "M19.5 6h-15v9h15V6Z"
  }), React305.createElement("path", {
    fillRule: "evenodd",
    d: "M3.375 3C2.339 3 1.5 3.84 1.5 4.875v11.25C1.5 17.16 2.34 18 3.375 18H9.75v1.5H6A.75.75 0 0 0 6 21h12a.75.75 0 0 0 0-1.5h-3.75V18h6.375c1.035 0 1.875-.84 1.875-1.875V4.875C22.5 3.839 21.66 3 20.625 3H3.375Zm0 13.5h17.25a.375.375 0 0 0 .375-.375V4.875a.375.375 0 0 0-.375-.375H3.375A.375.375 0 0 0 3 4.875v11.25c0 .207.168.375.375.375Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef305 = React305.forwardRef(TvIcon);
var TvIcon_default = ForwardRef305;

// node_modules/@heroicons/react/24/solid/esm/UnderlineIcon.js
var React306 = __toESM(require_react(), 1);
function UnderlineIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React306.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React306.createElement("title", {
    id: titleId
  }, title) : null, React306.createElement("path", {
    fillRule: "evenodd",
    d: "M5.995 2.994a.75.75 0 0 1 .75.75v7.5a5.25 5.25 0 1 0 10.5 0v-7.5a.75.75 0 0 1 1.5 0v7.5a6.75 6.75 0 1 1-13.5 0v-7.5a.75.75 0 0 1 .75-.75Zm-3 17.252a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5h-16.5a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef306 = React306.forwardRef(UnderlineIcon);
var UnderlineIcon_default = ForwardRef306;

// node_modules/@heroicons/react/24/solid/esm/UserCircleIcon.js
var React307 = __toESM(require_react(), 1);
function UserCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React307.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React307.createElement("title", {
    id: titleId
  }, title) : null, React307.createElement("path", {
    fillRule: "evenodd",
    d: "M18.685 19.097A9.723 9.723 0 0 0 21.75 12c0-5.385-4.365-9.75-9.75-9.75S2.25 6.615 2.25 12a9.723 9.723 0 0 0 3.065 7.097A9.716 9.716 0 0 0 12 21.75a9.716 9.716 0 0 0 6.685-2.653Zm-12.54-1.285A7.486 7.486 0 0 1 12 15a7.486 7.486 0 0 1 5.855 2.812A8.224 8.224 0 0 1 12 20.25a8.224 8.224 0 0 1-5.855-2.438ZM15.75 9a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef307 = React307.forwardRef(UserCircleIcon);
var UserCircleIcon_default = ForwardRef307;

// node_modules/@heroicons/react/24/solid/esm/UserGroupIcon.js
var React308 = __toESM(require_react(), 1);
function UserGroupIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React308.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React308.createElement("title", {
    id: titleId
  }, title) : null, React308.createElement("path", {
    fillRule: "evenodd",
    d: "M8.25 6.75a3.75 3.75 0 1 1 7.5 0 3.75 3.75 0 0 1-7.5 0ZM15.75 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM2.25 9.75a3 3 0 1 1 6 0 3 3 0 0 1-6 0ZM6.31 15.117A6.745 6.745 0 0 1 12 12a6.745 6.745 0 0 1 6.709 7.498.75.75 0 0 1-.372.568A12.696 12.696 0 0 1 12 21.75c-2.305 0-4.47-.612-6.337-1.684a.75.75 0 0 1-.372-.568 6.787 6.787 0 0 1 1.019-4.38Z",
    clipRule: "evenodd"
  }), React308.createElement("path", {
    d: "M5.082 14.254a8.287 8.287 0 0 0-1.308 5.135 9.687 9.687 0 0 1-1.764-.44l-.115-.04a.563.563 0 0 1-.373-.487l-.01-.121a3.75 3.75 0 0 1 3.57-4.047ZM20.226 19.389a8.287 8.287 0 0 0-1.308-5.135 3.75 3.75 0 0 1 3.57 4.047l-.01.121a.563.563 0 0 1-.373.486l-.115.04c-.567.2-1.156.349-1.764.441Z"
  }));
}
var ForwardRef308 = React308.forwardRef(UserGroupIcon);
var UserGroupIcon_default = ForwardRef308;

// node_modules/@heroicons/react/24/solid/esm/UserMinusIcon.js
var React309 = __toESM(require_react(), 1);
function UserMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React309.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React309.createElement("title", {
    id: titleId
  }, title) : null, React309.createElement("path", {
    d: "M10.375 2.25a4.125 4.125 0 1 0 0 8.25 4.125 4.125 0 0 0 0-8.25ZM10.375 12a7.125 7.125 0 0 0-7.124 7.247.75.75 0 0 0 .363.63 13.067 13.067 0 0 0 6.761 1.873c2.472 0 4.786-.684 6.76-1.873a.75.75 0 0 0 .364-.63l.001-.12v-.002A7.125 7.125 0 0 0 10.375 12ZM16 9.75a.75.75 0 0 0 0 1.5h6a.75.75 0 0 0 0-1.5h-6Z"
  }));
}
var ForwardRef309 = React309.forwardRef(UserMinusIcon);
var UserMinusIcon_default = ForwardRef309;

// node_modules/@heroicons/react/24/solid/esm/UserPlusIcon.js
var React310 = __toESM(require_react(), 1);
function UserPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React310.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React310.createElement("title", {
    id: titleId
  }, title) : null, React310.createElement("path", {
    d: "M5.25 6.375a4.125 4.125 0 1 1 8.25 0 4.125 4.125 0 0 1-8.25 0ZM2.25 19.125a7.125 7.125 0 0 1 14.25 0v.003l-.001.119a.75.75 0 0 1-.363.63 13.067 13.067 0 0 1-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 0 1-.364-.63l-.001-.122ZM18.75 7.5a.75.75 0 0 0-1.5 0v2.25H15a.75.75 0 0 0 0 1.5h2.25v2.25a.75.75 0 0 0 1.5 0v-2.25H21a.75.75 0 0 0 0-1.5h-2.25V7.5Z"
  }));
}
var ForwardRef310 = React310.forwardRef(UserPlusIcon);
var UserPlusIcon_default = ForwardRef310;

// node_modules/@heroicons/react/24/solid/esm/UserIcon.js
var React311 = __toESM(require_react(), 1);
function UserIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React311.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React311.createElement("title", {
    id: titleId
  }, title) : null, React311.createElement("path", {
    fillRule: "evenodd",
    d: "M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef311 = React311.forwardRef(UserIcon);
var UserIcon_default = ForwardRef311;

// node_modules/@heroicons/react/24/solid/esm/UsersIcon.js
var React312 = __toESM(require_react(), 1);
function UsersIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React312.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React312.createElement("title", {
    id: titleId
  }, title) : null, React312.createElement("path", {
    d: "M4.5 6.375a4.125 4.125 0 1 1 8.25 0 4.125 4.125 0 0 1-8.25 0ZM14.25 8.625a3.375 3.375 0 1 1 6.75 0 3.375 3.375 0 0 1-6.75 0ZM1.5 19.125a7.125 7.125 0 0 1 14.25 0v.003l-.001.119a.75.75 0 0 1-.363.63 13.067 13.067 0 0 1-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 0 1-.364-.63l-.001-.122ZM17.25 19.128l-.001.144a2.25 2.25 0 0 1-.233.96 10.088 10.088 0 0 0 5.06-1.01.75.75 0 0 0 .42-.643 4.875 4.875 0 0 0-6.957-4.611 8.586 8.586 0 0 1 1.71 5.157v.003Z"
  }));
}
var ForwardRef312 = React312.forwardRef(UsersIcon);
var UsersIcon_default = ForwardRef312;

// node_modules/@heroicons/react/24/solid/esm/VariableIcon.js
var React313 = __toESM(require_react(), 1);
function VariableIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React313.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React313.createElement("title", {
    id: titleId
  }, title) : null, React313.createElement("path", {
    fillRule: "evenodd",
    d: "M19.253 2.292a.75.75 0 0 1 .955.461A28.123 28.123 0 0 1 21.75 12c0 3.266-.547 6.388-1.542 9.247a.75.75 0 1 1-1.416-.494c.94-2.7 1.458-5.654 1.458-8.753s-.519-6.054-1.458-8.754a.75.75 0 0 1 .461-.954Zm-14.227.013a.75.75 0 0 1 .414.976A23.183 23.183 0 0 0 3.75 12c0 3.085.6 6.027 1.69 8.718a.75.75 0 0 1-1.39.563c-1.161-2.867-1.8-6-1.8-9.281 0-3.28.639-6.414 1.8-9.281a.75.75 0 0 1 .976-.414Zm4.275 5.052a1.5 1.5 0 0 1 2.21.803l.716 2.148L13.6 8.246a2.438 2.438 0 0 1 2.978-.892l.213.09a.75.75 0 1 1-.584 1.381l-.214-.09a.937.937 0 0 0-1.145.343l-2.021 3.033 1.084 3.255 1.445-.89a.75.75 0 1 1 .786 1.278l-1.444.889a1.5 1.5 0 0 1-2.21-.803l-.716-2.148-1.374 2.062a2.437 2.437 0 0 1-2.978.892l-.213-.09a.75.75 0 0 1 .584-1.381l.214.09a.938.938 0 0 0 1.145-.344l2.021-3.032-1.084-3.255-1.445.89a.75.75 0 1 1-.786-1.278l1.444-.89Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef313 = React313.forwardRef(VariableIcon);
var VariableIcon_default = ForwardRef313;

// node_modules/@heroicons/react/24/solid/esm/VideoCameraSlashIcon.js
var React314 = __toESM(require_react(), 1);
function VideoCameraSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React314.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React314.createElement("title", {
    id: titleId
  }, title) : null, React314.createElement("path", {
    d: "M.97 3.97a.75.75 0 0 1 1.06 0l15 15a.75.75 0 1 1-1.06 1.06l-15-15a.75.75 0 0 1 0-1.06ZM17.25 16.06l2.69 2.69c.944.945 2.56.276 2.56-1.06V6.31c0-1.336-1.616-2.005-2.56-1.06l-2.69 2.69v8.12ZM15.75 7.5v8.068L4.682 4.5h8.068a3 3 0 0 1 3 3ZM1.5 16.5V7.682l11.773 11.773c-.17.03-.345.045-.523.045H4.5a3 3 0 0 1-3-3Z"
  }));
}
var ForwardRef314 = React314.forwardRef(VideoCameraSlashIcon);
var VideoCameraSlashIcon_default = ForwardRef314;

// node_modules/@heroicons/react/24/solid/esm/VideoCameraIcon.js
var React315 = __toESM(require_react(), 1);
function VideoCameraIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React315.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React315.createElement("title", {
    id: titleId
  }, title) : null, React315.createElement("path", {
    d: "M4.5 4.5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h8.25a3 3 0 0 0 3-3v-9a3 3 0 0 0-3-3H4.5ZM19.94 18.75l-2.69-2.69V7.94l2.69-2.69c.944-.945 2.56-.276 2.56 1.06v11.38c0 1.336-1.616 2.005-2.56 1.06Z"
  }));
}
var ForwardRef315 = React315.forwardRef(VideoCameraIcon);
var VideoCameraIcon_default = ForwardRef315;

// node_modules/@heroicons/react/24/solid/esm/ViewColumnsIcon.js
var React316 = __toESM(require_react(), 1);
function ViewColumnsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React316.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React316.createElement("title", {
    id: titleId
  }, title) : null, React316.createElement("path", {
    d: "M15 3.75H9v16.5h6V3.75ZM16.5 20.25h3.375c1.035 0 1.875-.84 1.875-1.875V5.625c0-1.036-.84-1.875-1.875-1.875H16.5v16.5ZM4.125 3.75H7.5v16.5H4.125a1.875 1.875 0 0 1-1.875-1.875V5.625c0-1.036.84-1.875 1.875-1.875Z"
  }));
}
var ForwardRef316 = React316.forwardRef(ViewColumnsIcon);
var ViewColumnsIcon_default = ForwardRef316;

// node_modules/@heroicons/react/24/solid/esm/ViewfinderCircleIcon.js
var React317 = __toESM(require_react(), 1);
function ViewfinderCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React317.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React317.createElement("title", {
    id: titleId
  }, title) : null, React317.createElement("path", {
    d: "M6 3a3 3 0 0 0-3 3v1.5a.75.75 0 0 0 1.5 0V6A1.5 1.5 0 0 1 6 4.5h1.5a.75.75 0 0 0 0-1.5H6ZM16.5 3a.75.75 0 0 0 0 1.5H18A1.5 1.5 0 0 1 19.5 6v1.5a.75.75 0 0 0 1.5 0V6a3 3 0 0 0-3-3h-1.5ZM12 8.25a3.75 3.75 0 1 0 0 7.5 3.75 3.75 0 0 0 0-7.5ZM4.5 16.5a.75.75 0 0 0-1.5 0V18a3 3 0 0 0 3 3h1.5a.75.75 0 0 0 0-1.5H6A1.5 1.5 0 0 1 4.5 18v-1.5ZM21 16.5a.75.75 0 0 0-1.5 0V18a1.5 1.5 0 0 1-1.5 1.5h-1.5a.75.75 0 0 0 0 1.5H18a3 3 0 0 0 3-3v-1.5Z"
  }));
}
var ForwardRef317 = React317.forwardRef(ViewfinderCircleIcon);
var ViewfinderCircleIcon_default = ForwardRef317;

// node_modules/@heroicons/react/24/solid/esm/WalletIcon.js
var React318 = __toESM(require_react(), 1);
function WalletIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React318.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React318.createElement("title", {
    id: titleId
  }, title) : null, React318.createElement("path", {
    d: "M2.273 5.625A4.483 4.483 0 0 1 5.25 4.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0 0 18.75 3H5.25a3 3 0 0 0-2.977 2.625ZM2.273 8.625A4.483 4.483 0 0 1 5.25 7.5h13.5c1.141 0 2.183.425 2.977 1.125A3 3 0 0 0 18.75 6H5.25a3 3 0 0 0-2.977 2.625ZM5.25 9a3 3 0 0 0-3 3v6a3 3 0 0 0 3 3h13.5a3 3 0 0 0 3-3v-6a3 3 0 0 0-3-3H15a.75.75 0 0 0-.75.75 2.25 2.25 0 0 1-4.5 0A.75.75 0 0 0 9 9H5.25Z"
  }));
}
var ForwardRef318 = React318.forwardRef(WalletIcon);
var WalletIcon_default = ForwardRef318;

// node_modules/@heroicons/react/24/solid/esm/WifiIcon.js
var React319 = __toESM(require_react(), 1);
function WifiIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React319.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React319.createElement("title", {
    id: titleId
  }, title) : null, React319.createElement("path", {
    fillRule: "evenodd",
    d: "M1.371 8.143c5.858-5.857 15.356-5.857 21.213 0a.75.75 0 0 1 0 1.061l-.53.53a.75.75 0 0 1-1.06 0c-4.98-4.979-13.053-4.979-18.032 0a.75.75 0 0 1-1.06 0l-.53-.53a.75.75 0 0 1 0-1.06Zm3.182 3.182c4.1-4.1 10.749-4.1 14.85 0a.75.75 0 0 1 0 1.061l-.53.53a.75.75 0 0 1-1.062 0 8.25 8.25 0 0 0-11.667 0 .75.75 0 0 1-1.06 0l-.53-.53a.75.75 0 0 1 0-1.06Zm3.204 3.182a6 6 0 0 1 8.486 0 .75.75 0 0 1 0 1.061l-.53.53a.75.75 0 0 1-1.061 0 3.75 3.75 0 0 0-5.304 0 .75.75 0 0 1-1.06 0l-.53-.53a.75.75 0 0 1 0-1.06Zm3.182 3.182a1.5 1.5 0 0 1 2.122 0 .75.75 0 0 1 0 1.061l-.53.53a.75.75 0 0 1-1.061 0l-.53-.53a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef319 = React319.forwardRef(WifiIcon);
var WifiIcon_default = ForwardRef319;

// node_modules/@heroicons/react/24/solid/esm/WindowIcon.js
var React320 = __toESM(require_react(), 1);
function WindowIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React320.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React320.createElement("title", {
    id: titleId
  }, title) : null, React320.createElement("path", {
    fillRule: "evenodd",
    d: "M2.25 6a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V6Zm18 3H3.75v9a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V9Zm-15-3.75A.75.75 0 0 0 4.5 6v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V6a.75.75 0 0 0-.75-.75H5.25Zm1.5.75a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H7.5a.75.75 0 0 1-.75-.75V6Zm3-.75A.75.75 0 0 0 9 6v.008c0 .414.336.75.75.75h.008a.75.75 0 0 0 .75-.75V6a.75.75 0 0 0-.75-.75H9.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef320 = React320.forwardRef(WindowIcon);
var WindowIcon_default = ForwardRef320;

// node_modules/@heroicons/react/24/solid/esm/WrenchScrewdriverIcon.js
var React321 = __toESM(require_react(), 1);
function WrenchScrewdriverIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React321.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React321.createElement("title", {
    id: titleId
  }, title) : null, React321.createElement("path", {
    fillRule: "evenodd",
    d: "M12 6.75a5.25 5.25 0 0 1 6.775-*********** 0 0 1 .313 1.248l-3.32 3.319c.063.475.276.934.641 1.299.365.365.824.578 1.3.64l3.318-3.319a.75.75 0 0 1 1.248.313 5.25 5.25 0 0 1-5.472 6.756c-1.018-.086-1.87.1-2.309.634L7.344 21.3A3.298 3.298 0 1 1 2.7 16.657l8.684-7.151c.533-.44.72-1.291.634-2.309A5.342 5.342 0 0 1 12 6.75ZM4.117 19.125a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z",
    clipRule: "evenodd"
  }), React321.createElement("path", {
    d: "m10.076 8.64-2.201-2.2V4.874a.75.75 0 0 0-.364-.643l-3.75-2.25a.75.75 0 0 0-.916.113l-.75.75a.75.75 0 0 0-.113.916l2.25 3.75a.75.75 0 0 0 .643.364h1.564l2.062 2.062 1.575-1.297Z"
  }), React321.createElement("path", {
    fillRule: "evenodd",
    d: "m12.556 17.329 4.183 4.182a3.375 3.375 0 0 0 4.773-4.773l-3.306-3.305a6.803 6.803 0 0 1-1.53.043c-.394-.034-.682-.006-.867.042a.589.589 0 0 0-.167.063l-3.086 3.748Zm3.414-1.36a.75.75 0 0 1 1.06 0l1.875 1.876a.75.75 0 1 1-1.06 1.06L15.97 17.03a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef321 = React321.forwardRef(WrenchScrewdriverIcon);
var WrenchScrewdriverIcon_default = ForwardRef321;

// node_modules/@heroicons/react/24/solid/esm/WrenchIcon.js
var React322 = __toESM(require_react(), 1);
function WrenchIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React322.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React322.createElement("title", {
    id: titleId
  }, title) : null, React322.createElement("path", {
    fillRule: "evenodd",
    d: "M12 6.75a5.25 5.25 0 0 1 6.775-*********** 0 0 1 .313 1.248l-3.32 3.319c.063.475.276.934.641 1.299.365.365.824.578 1.3.64l3.318-3.319a.75.75 0 0 1 1.248.313 5.25 5.25 0 0 1-5.472 6.756c-1.018-.086-1.87.1-2.309.634L7.344 21.3A3.298 3.298 0 1 1 2.7 16.657l8.684-7.151c.533-.44.72-1.291.634-2.309A5.342 5.342 0 0 1 12 6.75ZM4.117 19.125a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75h-.008a.75.75 0 0 1-.75-.75v-.008Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef322 = React322.forwardRef(WrenchIcon);
var WrenchIcon_default = ForwardRef322;

// node_modules/@heroicons/react/24/solid/esm/XCircleIcon.js
var React323 = __toESM(require_react(), 1);
function XCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React323.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React323.createElement("title", {
    id: titleId
  }, title) : null, React323.createElement("path", {
    fillRule: "evenodd",
    d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef323 = React323.forwardRef(XCircleIcon);
var XCircleIcon_default = ForwardRef323;

// node_modules/@heroicons/react/24/solid/esm/XMarkIcon.js
var React324 = __toESM(require_react(), 1);
function XMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React324.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 24 24",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React324.createElement("title", {
    id: titleId
  }, title) : null, React324.createElement("path", {
    fillRule: "evenodd",
    d: "M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef324 = React324.forwardRef(XMarkIcon);
var XMarkIcon_default = ForwardRef324;
export {
  AcademicCapIcon_default as AcademicCapIcon,
  AdjustmentsHorizontalIcon_default as AdjustmentsHorizontalIcon,
  AdjustmentsVerticalIcon_default as AdjustmentsVerticalIcon,
  ArchiveBoxArrowDownIcon_default as ArchiveBoxArrowDownIcon,
  ArchiveBoxIcon_default as ArchiveBoxIcon,
  ArchiveBoxXMarkIcon_default as ArchiveBoxXMarkIcon,
  ArrowDownCircleIcon_default as ArrowDownCircleIcon,
  ArrowDownIcon_default as ArrowDownIcon,
  ArrowDownLeftIcon_default as ArrowDownLeftIcon,
  ArrowDownOnSquareIcon_default as ArrowDownOnSquareIcon,
  ArrowDownOnSquareStackIcon_default as ArrowDownOnSquareStackIcon,
  ArrowDownRightIcon_default as ArrowDownRightIcon,
  ArrowDownTrayIcon_default as ArrowDownTrayIcon,
  ArrowLeftCircleIcon_default as ArrowLeftCircleIcon,
  ArrowLeftEndOnRectangleIcon_default as ArrowLeftEndOnRectangleIcon,
  ArrowLeftIcon_default as ArrowLeftIcon,
  ArrowLeftOnRectangleIcon_default as ArrowLeftOnRectangleIcon,
  ArrowLeftStartOnRectangleIcon_default as ArrowLeftStartOnRectangleIcon,
  ArrowLongDownIcon_default as ArrowLongDownIcon,
  ArrowLongLeftIcon_default as ArrowLongLeftIcon,
  ArrowLongRightIcon_default as ArrowLongRightIcon,
  ArrowLongUpIcon_default as ArrowLongUpIcon,
  ArrowPathIcon_default as ArrowPathIcon,
  ArrowPathRoundedSquareIcon_default as ArrowPathRoundedSquareIcon,
  ArrowRightCircleIcon_default as ArrowRightCircleIcon,
  ArrowRightEndOnRectangleIcon_default as ArrowRightEndOnRectangleIcon,
  ArrowRightIcon_default as ArrowRightIcon,
  ArrowRightOnRectangleIcon_default as ArrowRightOnRectangleIcon,
  ArrowRightStartOnRectangleIcon_default as ArrowRightStartOnRectangleIcon,
  ArrowSmallDownIcon_default as ArrowSmallDownIcon,
  ArrowSmallLeftIcon_default as ArrowSmallLeftIcon,
  ArrowSmallRightIcon_default as ArrowSmallRightIcon,
  ArrowSmallUpIcon_default as ArrowSmallUpIcon,
  ArrowTopRightOnSquareIcon_default as ArrowTopRightOnSquareIcon,
  ArrowTrendingDownIcon_default as ArrowTrendingDownIcon,
  ArrowTrendingUpIcon_default as ArrowTrendingUpIcon,
  ArrowTurnDownLeftIcon_default as ArrowTurnDownLeftIcon,
  ArrowTurnDownRightIcon_default as ArrowTurnDownRightIcon,
  ArrowTurnLeftDownIcon_default as ArrowTurnLeftDownIcon,
  ArrowTurnLeftUpIcon_default as ArrowTurnLeftUpIcon,
  ArrowTurnRightDownIcon_default as ArrowTurnRightDownIcon,
  ArrowTurnRightUpIcon_default as ArrowTurnRightUpIcon,
  ArrowTurnUpLeftIcon_default as ArrowTurnUpLeftIcon,
  ArrowTurnUpRightIcon_default as ArrowTurnUpRightIcon,
  ArrowUpCircleIcon_default as ArrowUpCircleIcon,
  ArrowUpIcon_default as ArrowUpIcon,
  ArrowUpLeftIcon_default as ArrowUpLeftIcon,
  ArrowUpOnSquareIcon_default as ArrowUpOnSquareIcon,
  ArrowUpOnSquareStackIcon_default as ArrowUpOnSquareStackIcon,
  ArrowUpRightIcon_default as ArrowUpRightIcon,
  ArrowUpTrayIcon_default as ArrowUpTrayIcon,
  ArrowUturnDownIcon_default as ArrowUturnDownIcon,
  ArrowUturnLeftIcon_default as ArrowUturnLeftIcon,
  ArrowUturnRightIcon_default as ArrowUturnRightIcon,
  ArrowUturnUpIcon_default as ArrowUturnUpIcon,
  ArrowsPointingInIcon_default as ArrowsPointingInIcon,
  ArrowsPointingOutIcon_default as ArrowsPointingOutIcon,
  ArrowsRightLeftIcon_default as ArrowsRightLeftIcon,
  ArrowsUpDownIcon_default as ArrowsUpDownIcon,
  AtSymbolIcon_default as AtSymbolIcon,
  BackspaceIcon_default as BackspaceIcon,
  BackwardIcon_default as BackwardIcon,
  BanknotesIcon_default as BanknotesIcon,
  Bars2Icon_default as Bars2Icon,
  Bars3BottomLeftIcon_default as Bars3BottomLeftIcon,
  Bars3BottomRightIcon_default as Bars3BottomRightIcon,
  Bars3CenterLeftIcon_default as Bars3CenterLeftIcon,
  Bars3Icon_default as Bars3Icon,
  Bars4Icon_default as Bars4Icon,
  BarsArrowDownIcon_default as BarsArrowDownIcon,
  BarsArrowUpIcon_default as BarsArrowUpIcon,
  Battery0Icon_default as Battery0Icon,
  Battery100Icon_default as Battery100Icon,
  Battery50Icon_default as Battery50Icon,
  BeakerIcon_default as BeakerIcon,
  BellAlertIcon_default as BellAlertIcon,
  BellIcon_default as BellIcon,
  BellSlashIcon_default as BellSlashIcon,
  BellSnoozeIcon_default as BellSnoozeIcon,
  BoldIcon_default as BoldIcon,
  BoltIcon_default as BoltIcon,
  BoltSlashIcon_default as BoltSlashIcon,
  BookOpenIcon_default as BookOpenIcon,
  BookmarkIcon_default as BookmarkIcon,
  BookmarkSlashIcon_default as BookmarkSlashIcon,
  BookmarkSquareIcon_default as BookmarkSquareIcon,
  BriefcaseIcon_default as BriefcaseIcon,
  BugAntIcon_default as BugAntIcon,
  BuildingLibraryIcon_default as BuildingLibraryIcon,
  BuildingOffice2Icon_default as BuildingOffice2Icon,
  BuildingOfficeIcon_default as BuildingOfficeIcon,
  BuildingStorefrontIcon_default as BuildingStorefrontIcon,
  CakeIcon_default as CakeIcon,
  CalculatorIcon_default as CalculatorIcon,
  CalendarDateRangeIcon_default as CalendarDateRangeIcon,
  CalendarDaysIcon_default as CalendarDaysIcon,
  CalendarIcon_default as CalendarIcon,
  CameraIcon_default as CameraIcon,
  ChartBarIcon_default as ChartBarIcon,
  ChartBarSquareIcon_default as ChartBarSquareIcon,
  ChartPieIcon_default as ChartPieIcon,
  ChatBubbleBottomCenterIcon_default as ChatBubbleBottomCenterIcon,
  ChatBubbleBottomCenterTextIcon_default as ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftEllipsisIcon_default as ChatBubbleLeftEllipsisIcon,
  ChatBubbleLeftIcon_default as ChatBubbleLeftIcon,
  ChatBubbleLeftRightIcon_default as ChatBubbleLeftRightIcon,
  ChatBubbleOvalLeftEllipsisIcon_default as ChatBubbleOvalLeftEllipsisIcon,
  ChatBubbleOvalLeftIcon_default as ChatBubbleOvalLeftIcon,
  CheckBadgeIcon_default as CheckBadgeIcon,
  CheckCircleIcon_default as CheckCircleIcon,
  CheckIcon_default as CheckIcon,
  ChevronDoubleDownIcon_default as ChevronDoubleDownIcon,
  ChevronDoubleLeftIcon_default as ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon_default as ChevronDoubleRightIcon,
  ChevronDoubleUpIcon_default as ChevronDoubleUpIcon,
  ChevronDownIcon_default as ChevronDownIcon,
  ChevronLeftIcon_default as ChevronLeftIcon,
  ChevronRightIcon_default as ChevronRightIcon,
  ChevronUpDownIcon_default as ChevronUpDownIcon,
  ChevronUpIcon_default as ChevronUpIcon,
  CircleStackIcon_default as CircleStackIcon,
  ClipboardDocumentCheckIcon_default as ClipboardDocumentCheckIcon,
  ClipboardDocumentIcon_default as ClipboardDocumentIcon,
  ClipboardDocumentListIcon_default as ClipboardDocumentListIcon,
  ClipboardIcon_default as ClipboardIcon,
  ClockIcon_default as ClockIcon,
  CloudArrowDownIcon_default as CloudArrowDownIcon,
  CloudArrowUpIcon_default as CloudArrowUpIcon,
  CloudIcon_default as CloudIcon,
  CodeBracketIcon_default as CodeBracketIcon,
  CodeBracketSquareIcon_default as CodeBracketSquareIcon,
  Cog6ToothIcon_default as Cog6ToothIcon,
  Cog8ToothIcon_default as Cog8ToothIcon,
  CogIcon_default as CogIcon,
  CommandLineIcon_default as CommandLineIcon,
  ComputerDesktopIcon_default as ComputerDesktopIcon,
  CpuChipIcon_default as CpuChipIcon,
  CreditCardIcon_default as CreditCardIcon,
  CubeIcon_default as CubeIcon,
  CubeTransparentIcon_default as CubeTransparentIcon,
  CurrencyBangladeshiIcon_default as CurrencyBangladeshiIcon,
  CurrencyDollarIcon_default as CurrencyDollarIcon,
  CurrencyEuroIcon_default as CurrencyEuroIcon,
  CurrencyPoundIcon_default as CurrencyPoundIcon,
  CurrencyRupeeIcon_default as CurrencyRupeeIcon,
  CurrencyYenIcon_default as CurrencyYenIcon,
  CursorArrowRaysIcon_default as CursorArrowRaysIcon,
  CursorArrowRippleIcon_default as CursorArrowRippleIcon,
  DevicePhoneMobileIcon_default as DevicePhoneMobileIcon,
  DeviceTabletIcon_default as DeviceTabletIcon,
  DivideIcon_default as DivideIcon,
  DocumentArrowDownIcon_default as DocumentArrowDownIcon,
  DocumentArrowUpIcon_default as DocumentArrowUpIcon,
  DocumentChartBarIcon_default as DocumentChartBarIcon,
  DocumentCheckIcon_default as DocumentCheckIcon,
  DocumentCurrencyBangladeshiIcon_default as DocumentCurrencyBangladeshiIcon,
  DocumentCurrencyDollarIcon_default as DocumentCurrencyDollarIcon,
  DocumentCurrencyEuroIcon_default as DocumentCurrencyEuroIcon,
  DocumentCurrencyPoundIcon_default as DocumentCurrencyPoundIcon,
  DocumentCurrencyRupeeIcon_default as DocumentCurrencyRupeeIcon,
  DocumentCurrencyYenIcon_default as DocumentCurrencyYenIcon,
  DocumentDuplicateIcon_default as DocumentDuplicateIcon,
  DocumentIcon_default as DocumentIcon,
  DocumentMagnifyingGlassIcon_default as DocumentMagnifyingGlassIcon,
  DocumentMinusIcon_default as DocumentMinusIcon,
  DocumentPlusIcon_default as DocumentPlusIcon,
  DocumentTextIcon_default as DocumentTextIcon,
  EllipsisHorizontalCircleIcon_default as EllipsisHorizontalCircleIcon,
  EllipsisHorizontalIcon_default as EllipsisHorizontalIcon,
  EllipsisVerticalIcon_default as EllipsisVerticalIcon,
  EnvelopeIcon_default as EnvelopeIcon,
  EnvelopeOpenIcon_default as EnvelopeOpenIcon,
  EqualsIcon_default as EqualsIcon,
  ExclamationCircleIcon_default as ExclamationCircleIcon,
  ExclamationTriangleIcon_default as ExclamationTriangleIcon,
  EyeDropperIcon_default as EyeDropperIcon,
  EyeIcon_default as EyeIcon,
  EyeSlashIcon_default as EyeSlashIcon,
  FaceFrownIcon_default as FaceFrownIcon,
  FaceSmileIcon_default as FaceSmileIcon,
  FilmIcon_default as FilmIcon,
  FingerPrintIcon_default as FingerPrintIcon,
  FireIcon_default as FireIcon,
  FlagIcon_default as FlagIcon,
  FolderArrowDownIcon_default as FolderArrowDownIcon,
  FolderIcon_default as FolderIcon,
  FolderMinusIcon_default as FolderMinusIcon,
  FolderOpenIcon_default as FolderOpenIcon,
  FolderPlusIcon_default as FolderPlusIcon,
  ForwardIcon_default as ForwardIcon,
  FunnelIcon_default as FunnelIcon,
  GifIcon_default as GifIcon,
  GiftIcon_default as GiftIcon,
  GiftTopIcon_default as GiftTopIcon,
  GlobeAltIcon_default as GlobeAltIcon,
  GlobeAmericasIcon_default as GlobeAmericasIcon,
  GlobeAsiaAustraliaIcon_default as GlobeAsiaAustraliaIcon,
  GlobeEuropeAfricaIcon_default as GlobeEuropeAfricaIcon,
  H1Icon_default as H1Icon,
  H2Icon_default as H2Icon,
  H3Icon_default as H3Icon,
  HandRaisedIcon_default as HandRaisedIcon,
  HandThumbDownIcon_default as HandThumbDownIcon,
  HandThumbUpIcon_default as HandThumbUpIcon,
  HashtagIcon_default as HashtagIcon,
  HeartIcon_default as HeartIcon,
  HomeIcon_default as HomeIcon,
  HomeModernIcon_default as HomeModernIcon,
  IdentificationIcon_default as IdentificationIcon,
  InboxArrowDownIcon_default as InboxArrowDownIcon,
  InboxIcon_default as InboxIcon,
  InboxStackIcon_default as InboxStackIcon,
  InformationCircleIcon_default as InformationCircleIcon,
  ItalicIcon_default as ItalicIcon,
  KeyIcon_default as KeyIcon,
  LanguageIcon_default as LanguageIcon,
  LifebuoyIcon_default as LifebuoyIcon,
  LightBulbIcon_default as LightBulbIcon,
  LinkIcon_default as LinkIcon,
  LinkSlashIcon_default as LinkSlashIcon,
  ListBulletIcon_default as ListBulletIcon,
  LockClosedIcon_default as LockClosedIcon,
  LockOpenIcon_default as LockOpenIcon,
  MagnifyingGlassCircleIcon_default as MagnifyingGlassCircleIcon,
  MagnifyingGlassIcon_default as MagnifyingGlassIcon,
  MagnifyingGlassMinusIcon_default as MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon_default as MagnifyingGlassPlusIcon,
  MapIcon_default as MapIcon,
  MapPinIcon_default as MapPinIcon,
  MegaphoneIcon_default as MegaphoneIcon,
  MicrophoneIcon_default as MicrophoneIcon,
  MinusCircleIcon_default as MinusCircleIcon,
  MinusIcon_default as MinusIcon,
  MinusSmallIcon_default as MinusSmallIcon,
  MoonIcon_default as MoonIcon,
  MusicalNoteIcon_default as MusicalNoteIcon,
  NewspaperIcon_default as NewspaperIcon,
  NoSymbolIcon_default as NoSymbolIcon,
  NumberedListIcon_default as NumberedListIcon,
  PaintBrushIcon_default as PaintBrushIcon,
  PaperAirplaneIcon_default as PaperAirplaneIcon,
  PaperClipIcon_default as PaperClipIcon,
  PauseCircleIcon_default as PauseCircleIcon,
  PauseIcon_default as PauseIcon,
  PencilIcon_default as PencilIcon,
  PencilSquareIcon_default as PencilSquareIcon,
  PercentBadgeIcon_default as PercentBadgeIcon,
  PhoneArrowDownLeftIcon_default as PhoneArrowDownLeftIcon,
  PhoneArrowUpRightIcon_default as PhoneArrowUpRightIcon,
  PhoneIcon_default as PhoneIcon,
  PhoneXMarkIcon_default as PhoneXMarkIcon,
  PhotoIcon_default as PhotoIcon,
  PlayCircleIcon_default as PlayCircleIcon,
  PlayIcon_default as PlayIcon,
  PlayPauseIcon_default as PlayPauseIcon,
  PlusCircleIcon_default as PlusCircleIcon,
  PlusIcon_default as PlusIcon,
  PlusSmallIcon_default as PlusSmallIcon,
  PowerIcon_default as PowerIcon,
  PresentationChartBarIcon_default as PresentationChartBarIcon,
  PresentationChartLineIcon_default as PresentationChartLineIcon,
  PrinterIcon_default as PrinterIcon,
  PuzzlePieceIcon_default as PuzzlePieceIcon,
  QrCodeIcon_default as QrCodeIcon,
  QuestionMarkCircleIcon_default as QuestionMarkCircleIcon,
  QueueListIcon_default as QueueListIcon,
  RadioIcon_default as RadioIcon,
  ReceiptPercentIcon_default as ReceiptPercentIcon,
  ReceiptRefundIcon_default as ReceiptRefundIcon,
  RectangleGroupIcon_default as RectangleGroupIcon,
  RectangleStackIcon_default as RectangleStackIcon,
  RocketLaunchIcon_default as RocketLaunchIcon,
  RssIcon_default as RssIcon,
  ScaleIcon_default as ScaleIcon,
  ScissorsIcon_default as ScissorsIcon,
  ServerIcon_default as ServerIcon,
  ServerStackIcon_default as ServerStackIcon,
  ShareIcon_default as ShareIcon,
  ShieldCheckIcon_default as ShieldCheckIcon,
  ShieldExclamationIcon_default as ShieldExclamationIcon,
  ShoppingBagIcon_default as ShoppingBagIcon,
  ShoppingCartIcon_default as ShoppingCartIcon,
  SignalIcon_default as SignalIcon,
  SignalSlashIcon_default as SignalSlashIcon,
  SlashIcon_default as SlashIcon,
  SparklesIcon_default as SparklesIcon,
  SpeakerWaveIcon_default as SpeakerWaveIcon,
  SpeakerXMarkIcon_default as SpeakerXMarkIcon,
  Square2StackIcon_default as Square2StackIcon,
  Square3Stack3DIcon_default as Square3Stack3DIcon,
  Squares2X2Icon_default as Squares2X2Icon,
  SquaresPlusIcon_default as SquaresPlusIcon,
  StarIcon_default as StarIcon,
  StopCircleIcon_default as StopCircleIcon,
  StopIcon_default as StopIcon,
  StrikethroughIcon_default as StrikethroughIcon,
  SunIcon_default as SunIcon,
  SwatchIcon_default as SwatchIcon,
  TableCellsIcon_default as TableCellsIcon,
  TagIcon_default as TagIcon,
  TicketIcon_default as TicketIcon,
  TrashIcon_default as TrashIcon,
  TrophyIcon_default as TrophyIcon,
  TruckIcon_default as TruckIcon,
  TvIcon_default as TvIcon,
  UnderlineIcon_default as UnderlineIcon,
  UserCircleIcon_default as UserCircleIcon,
  UserGroupIcon_default as UserGroupIcon,
  UserIcon_default as UserIcon,
  UserMinusIcon_default as UserMinusIcon,
  UserPlusIcon_default as UserPlusIcon,
  UsersIcon_default as UsersIcon,
  VariableIcon_default as VariableIcon,
  VideoCameraIcon_default as VideoCameraIcon,
  VideoCameraSlashIcon_default as VideoCameraSlashIcon,
  ViewColumnsIcon_default as ViewColumnsIcon,
  ViewfinderCircleIcon_default as ViewfinderCircleIcon,
  WalletIcon_default as WalletIcon,
  WifiIcon_default as WifiIcon,
  WindowIcon_default as WindowIcon,
  WrenchIcon_default as WrenchIcon,
  WrenchScrewdriverIcon_default as WrenchScrewdriverIcon,
  XCircleIcon_default as XCircleIcon,
  XMarkIcon_default as XMarkIcon
};
//# sourceMappingURL=@heroicons_react_24_solid.js.map
