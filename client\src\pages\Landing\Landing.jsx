import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Calculator, 
  FileText, 
  TrendingUp, 
  Shield, 
  Globe, 
  Smartphone,
  Check,
  Star,
  ArrowRight,
  Menu,
  X
} from 'lucide-react';

const Landing = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const features = [
    {
      icon: <FileText className="w-8 h-8 text-blue-600" />,
      title: "Smart Invoicing",
      description: "Create professional invoices in seconds with automated calculations and multi-currency support."
    },
    {
      icon: <Calculator className="w-8 h-8 text-green-600" />,
      title: "Expense Tracking",
      description: "Track business expenses effortlessly with receipt scanning and automatic categorization."
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-purple-600" />,
      title: "Financial Analytics",
      description: "Get insights into your business performance with detailed reports and analytics."
    },
    {
      icon: <Shield className="w-8 h-8 text-red-600" />,
      title: "Tax Compliance",
      description: "Stay compliant with automated GST/VAT calculations and tax reporting."
    },
    {
      icon: <Globe className="w-8 h-8 text-indigo-600" />,
      title: "Multi-Currency",
      description: "Work with clients globally with support for 150+ currencies and real-time exchange rates."
    },
    {
      icon: <Smartphone className="w-8 h-8 text-orange-600" />,
      title: "Mobile First",
      description: "Manage your finances on the go with our responsive mobile-first design."
    }
  ];

  const testimonials = [
    {
      name: "Priya Sharma",
      role: "Freelance Designer",
      content: "FinanceFlow has simplified my invoicing process completely. I can now focus on my work instead of paperwork!",
      rating: 5
    },
    {
      name: "Rajesh Kumar",
      role: "Small Business Owner",
      content: "The multi-currency support is fantastic. I work with international clients and this makes everything seamless.",
      rating: 5
    },
    {
      name: "Anita Patel",
      role: "Consultant",
      content: "Best investment for my business. The lifetime plan is incredible value and the features are top-notch.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">FinanceFlow</h1>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <a href="#features" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Features</a>
                <a href="#pricing" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Pricing</a>
                <a href="#testimonials" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Reviews</a>
                <Link to="/login" className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium">Login</Link>
                <Link to="/signup" className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                  Get Started
                </Link>
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-gray-600 hover:text-gray-900"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <a href="#features" className="block text-gray-600 hover:text-gray-900 px-3 py-2 text-base font-medium">Features</a>
              <a href="#pricing" className="block text-gray-600 hover:text-gray-900 px-3 py-2 text-base font-medium">Pricing</a>
              <a href="#testimonials" className="block text-gray-600 hover:text-gray-900 px-3 py-2 text-base font-medium">Reviews</a>
              <Link to="/login" className="block text-gray-600 hover:text-gray-900 px-3 py-2 text-base font-medium">Login</Link>
              <Link to="/signup" className="block bg-blue-600 text-white px-3 py-2 rounded-md text-base font-medium hover:bg-blue-700 mx-3">
                Get Started
              </Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Simplify Your <span className="text-blue-600">Business Finances</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              The complete financial management solution for freelancers and small businesses. 
              Create invoices, track expenses, and manage your finances with ease.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/signup" 
                className="bg-blue-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center"
              >
                Start Free Trial <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <button 
                className="border border-gray-300 text-gray-700 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-50 transition-colors"
              >
                Watch Demo
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              No credit card required • 14-day free trial • Cancel anytime
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Manage Your Finances
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Powerful features designed specifically for freelancers and small businesses
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="p-6 border border-gray-200 rounded-lg hover:shadow-lg transition-shadow">
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600">
              Choose the plan that works best for your business
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Monthly Plan */}
            <div className="bg-white p-8 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Starter</h3>
              <p className="text-gray-600 mb-6">Perfect for freelancers</p>
              <div className="mb-6">
                <span className="text-4xl font-bold text-gray-900">$2</span>
                <span className="text-gray-600">/month</span>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Up to 50 invoices/month</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Up to 25 clients</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Basic reporting</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Email support</li>
              </ul>
              <Link to="/signup?plan=monthly" className="w-full bg-gray-900 text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors block text-center">
                Start Free Trial
              </Link>
            </div>

            {/* Yearly Plan */}
            <div className="bg-white p-8 rounded-lg border-2 border-blue-500 hover:shadow-lg transition-shadow relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Professional</h3>
              <p className="text-gray-600 mb-6">Best for growing businesses</p>
              <div className="mb-6">
                <span className="text-4xl font-bold text-gray-900">$20</span>
                <span className="text-gray-600">/year</span>
                <div className="text-sm text-green-600 font-semibold">Save 83%</div>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Unlimited invoices</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Unlimited clients</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Advanced reporting</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Priority support</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Multi-currency</li>
              </ul>
              <Link to="/signup?plan=yearly" className="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors block text-center">
                Start Free Trial
              </Link>
            </div>

            {/* Lifetime Plan */}
            <div className="bg-white p-8 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Lifetime</h3>
              <p className="text-gray-600 mb-6">One-time payment, forever access</p>
              <div className="mb-6">
                <span className="text-4xl font-bold text-gray-900">$100</span>
                <span className="text-gray-600">/lifetime</span>
                <div className="text-sm text-green-600 font-semibold">Best Value</div>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Everything in Professional</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Lifetime updates</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />Premium support</li>
                <li className="flex items-center"><Check className="w-5 h-5 text-green-500 mr-2" />White-label option</li>
              </ul>
              <Link to="/signup?plan=lifetime" className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-colors block text-center">
                Get Lifetime Access
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Loved by Thousands of Users
            </h2>
            <p className="text-xl text-gray-600">
              See what our customers have to say about FinanceFlow
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-lg">
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-gray-600 text-sm">{testimonial.role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Business Finances?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of freelancers and small businesses who trust FinanceFlow
          </p>
          <Link 
            to="/signup" 
            className="bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
          >
            Start Your Free Trial <ArrowRight className="ml-2 w-5 h-5" />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">FinanceFlow</h3>
              <p className="text-gray-400">
                Simplifying business finances for freelancers and small businesses worldwide.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#features" className="hover:text-white">Features</a></li>
                <li><a href="#pricing" className="hover:text-white">Pricing</a></li>
                <li><button className="hover:text-white">Demo</button></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/help" className="hover:text-white">Help Center</a></li>
                <li><a href="/contact" className="hover:text-white">Contact Us</a></li>
                <li><a href="/api" className="hover:text-white">API Docs</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/privacy" className="hover:text-white">Privacy Policy</a></li>
                <li><a href="/terms" className="hover:text-white">Terms of Service</a></li>
                <li><a href="/security" className="hover:text-white">Security</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 FinanceFlow. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
