const express = require("express");
const { authenticateToken } = require("../middleware/auth-mock");

const router = express.Router();

// Mock invoices storage (in-memory for testing)
const mockInvoices = [];

/**
 * @route   GET /api/invoices
 * @desc    Get all invoices for user
 * @access  Private
 */
router.get("/", authenticateToken, (req, res) => {
  const userInvoices = mockInvoices.filter(
    (invoice) => invoice.userId === req.user.id
  );
  res.json({
    message: "Invoices retrieved successfully (mock mode)",
    invoices: userInvoices,
    total: userInvoices.length,
  });
});

/**
 * @route   POST /api/invoices
 * @desc    Create a new invoice
 * @access  Private
 */
router.post("/", authenticateToken, (req, res) => {
  try {
    const {
      clientId,
      title,
      description,
      dueDate,
      items,
      taxRate,
      currency,
      notes,
      terms,
    } = req.body;

    // Calculate totals
    const subtotal = items.reduce(
      (sum, item) => sum + item.quantity * item.rate,
      0
    );
    const taxAmount = (subtotal * (taxRate || 0)) / 100;
    const total = subtotal + taxAmount;

    const invoice = {
      id: `invoice_${Date.now()}`,
      userId: req.user.id,
      clientId,
      invoiceNumber: `INV-${Date.now()}`,
      title: title || null,
      description: description || null,
      issueDate: new Date().toISOString(),
      dueDate:
        dueDate ||
        new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      status: "DRAFT",
      subtotal: subtotal.toFixed(2),
      taxRate: taxRate || 0,
      taxAmount: taxAmount.toFixed(2),
      total: total.toFixed(2),
      currency: currency || "USD",
      notes: notes || null,
      terms: terms || null,
      items: items.map((item, index) => ({
        id: `item_${Date.now()}_${index}`,
        description: item.description,
        quantity: item.quantity,
        rate: item.rate,
        amount: (item.quantity * item.rate).toFixed(2),
      })),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockInvoices.push(invoice);

    res.status(201).json({
      message: "Invoice created successfully (mock mode)",
      invoice,
    });
  } catch (error) {
    console.error("Create invoice error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create invoice",
    });
  }
});

/**
 * @route   GET /api/invoices/:id
 * @desc    Get invoice by ID
 * @access  Private
 */
router.get("/:id", authenticateToken, (req, res) => {
  const invoice = mockInvoices.find(
    (i) => i.id === req.params.id && i.userId === req.user.id
  );

  if (!invoice) {
    return res.status(404).json({
      error: "Not Found",
      message: "Invoice not found",
    });
  }

  res.json({
    message: "Invoice retrieved successfully (mock mode)",
    invoice,
  });
});

/**
 * @route   PUT /api/invoices/:id
 * @desc    Update invoice
 * @access  Private
 */
router.put("/:id", authenticateToken, (req, res) => {
  try {
    const invoiceIndex = mockInvoices.findIndex(
      (i) => i.id === req.params.id && i.userId === req.user.id
    );

    if (invoiceIndex === -1) {
      return res.status(404).json({
        error: "Not Found",
        message: "Invoice not found",
      });
    }

    const {
      title,
      description,
      dueDate,
      items,
      taxRate,
      currency,
      notes,
      terms,
      status,
    } = req.body;

    // Recalculate totals if items changed
    let updatedInvoice = { ...mockInvoices[invoiceIndex] };

    if (items) {
      const subtotal = items.reduce(
        (sum, item) => sum + item.quantity * item.rate,
        0
      );
      const taxAmount = (subtotal * (taxRate || updatedInvoice.taxRate)) / 100;
      const total = subtotal + taxAmount;

      updatedInvoice.subtotal = subtotal.toFixed(2);
      updatedInvoice.taxAmount = taxAmount.toFixed(2);
      updatedInvoice.total = total.toFixed(2);
      updatedInvoice.items = items.map((item, index) => ({
        id: item.id || `item_${Date.now()}_${index}`,
        description: item.description,
        quantity: item.quantity,
        rate: item.rate,
        amount: (item.quantity * item.rate).toFixed(2),
      }));
    }

    // Update other fields
    if (title !== undefined) updatedInvoice.title = title;
    if (description !== undefined) updatedInvoice.description = description;
    if (dueDate !== undefined) updatedInvoice.dueDate = dueDate;
    if (taxRate !== undefined) updatedInvoice.taxRate = taxRate;
    if (currency !== undefined) updatedInvoice.currency = currency;
    if (notes !== undefined) updatedInvoice.notes = notes;
    if (terms !== undefined) updatedInvoice.terms = terms;
    if (status !== undefined) updatedInvoice.status = status;

    updatedInvoice.updatedAt = new Date().toISOString();

    mockInvoices[invoiceIndex] = updatedInvoice;

    res.json({
      message: "Invoice updated successfully (mock mode)",
      invoice: updatedInvoice,
    });
  } catch (error) {
    console.error("Update invoice error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update invoice",
    });
  }
});

/**
 * @route   POST /api/invoices/:id/mark-paid
 * @desc    Mark invoice as paid
 * @access  Private
 */
router.post("/:id/mark-paid", authenticateToken, (req, res) => {
  const invoiceIndex = mockInvoices.findIndex(
    (i) => i.id === req.params.id && i.userId === req.user.id
  );

  if (invoiceIndex === -1) {
    return res.status(404).json({
      error: "Not Found",
      message: "Invoice not found",
    });
  }

  mockInvoices[invoiceIndex].status = "PAID";
  mockInvoices[invoiceIndex].paidAt = new Date().toISOString();
  mockInvoices[invoiceIndex].updatedAt = new Date().toISOString();

  res.json({
    message: "Invoice marked as paid successfully (mock mode)",
    invoice: mockInvoices[invoiceIndex],
  });
});

/**
 * @route   DELETE /api/invoices/:id
 * @desc    Delete invoice
 * @access  Private
 */
router.delete("/:id", authenticateToken, (req, res) => {
  const invoiceIndex = mockInvoices.findIndex(
    (i) => i.id === req.params.id && i.userId === req.user.id
  );

  if (invoiceIndex === -1) {
    return res.status(404).json({
      error: "Not Found",
      message: "Invoice not found",
    });
  }

  mockInvoices.splice(invoiceIndex, 1);

  res.json({
    message: "Invoice deleted successfully (mock mode)",
  });
});

module.exports = router;
