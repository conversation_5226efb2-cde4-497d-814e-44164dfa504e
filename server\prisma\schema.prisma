generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @default(cuid())
  email         String         @unique
  password      String
  firstName     String
  lastName      String
  company       String?
  phone         String?
  address       String?
  city          String?
  state         String?
  zipCode       String?
  country       String         @default("US")
  role          UserRole       @default(USER)
  isActive      Boolean        @default(true)
  isVerified    Boolean        @default(false)
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  auditLogs     AuditLog[]
  clients       Client[]
  expenses      Expense[]
  invoices      Invoice[]
  notifications Notification[]
  taxReports    TaxReport[]
  transactions  Transaction[]

  @@map("users")
}

model Client {
  id        String    @id @default(cuid())
  userId    String
  name      String
  email     String?
  phone     String?
  company   String?
  address   String?
  city      String?
  state     String?
  zipCode   String?
  country   String    @default("US")
  taxId     String?
  notes     String?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  invoices  Invoice[]

  @@map("clients")
}

model Invoice {
  id            String        @id @default(cuid())
  userId        String
  clientId      String
  invoiceNumber String        @unique
  title         String?
  description   String?
  issueDate     DateTime      @default(now())
  dueDate       DateTime
  status        InvoiceStatus @default(DRAFT)
  subtotal      Decimal       @db.Decimal(10, 2)
  taxRate       Decimal       @default(0) @db.Decimal(5, 2)
  taxAmount     Decimal       @default(0) @db.Decimal(10, 2)
  total         Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD")
  notes         String?
  terms         String?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  items         InvoiceItem[]
  client        Client        @relation(fields: [clientId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions  Transaction[]

  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoiceId   String
  description String
  quantity    Decimal  @db.Decimal(10, 2)
  rate        Decimal  @db.Decimal(10, 2)
  amount      Decimal  @db.Decimal(10, 2)
  taxRate     Decimal  @default(0) @db.Decimal(5, 2)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

model Expense {
  id           String          @id @default(cuid())
  userId       String
  category     ExpenseCategory
  description  String
  amount       Decimal         @db.Decimal(10, 2)
  currency     String          @default("USD")
  date         DateTime        @default(now())
  receipt      String?
  notes        String?
  isRecurring  Boolean         @default(false)
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  user         User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]

  @@map("expenses")
}

model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Decimal           @db.Decimal(10, 2)
  currency    String            @default("USD")
  description String
  date        DateTime          @default(now())
  status      TransactionStatus @default(COMPLETED)
  reference   String?
  invoiceId   String?
  expenseId   String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  expense     Expense?          @relation(fields: [expenseId], references: [id])
  invoice     Invoice?          @relation(fields: [invoiceId], references: [id])
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

model TaxReport {
  id            String          @id @default(cuid())
  userId        String
  period        String
  year          Int
  quarter       Int?
  totalIncome   Decimal         @db.Decimal(10, 2)
  totalExpenses Decimal         @db.Decimal(10, 2)
  taxableIncome Decimal         @db.Decimal(10, 2)
  taxDue        Decimal         @db.Decimal(10, 2)
  status        TaxReportStatus @default(DRAFT)
  filedAt       DateTime?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tax_reports")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  data      Json?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String
  action     String
  resource   String
  resourceId String?
  oldValues  Json?
  newValues  Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("audit_logs")
}

enum UserRole {
  USER
  ADMIN
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  OVERDUE
  CANCELLED
}

enum ExpenseCategory {
  OFFICE_SUPPLIES
  TRAVEL
  MEALS
  UTILITIES
  RENT
  INSURANCE
  MARKETING
  PROFESSIONAL_SERVICES
  SOFTWARE
  EQUIPMENT
  OTHER
}

enum TransactionType {
  INCOME
  EXPENSE
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum TaxReportStatus {
  DRAFT
  GENERATED
  FILED
}

enum NotificationType {
  INVOICE_SENT
  INVOICE_PAID
  INVOICE_OVERDUE
  EXPENSE_ADDED
  TAX_REMINDER
  SYSTEM_UPDATE
}
