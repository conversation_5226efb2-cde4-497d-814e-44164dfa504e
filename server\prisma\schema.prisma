generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String         @id @default(cuid())
  email             String         @unique
  password          String
  firstName         String
  lastName          String
  company           String?
  phone             String?
  address           String?
  city              String?
  state             String?
  zipCode           String?
  country           String         @default("US")
  currency          String         @default("USD")
  timezone          String         @default("UTC")
  language          String         @default("en")
  role              UserRole       @default(USER)
  isActive          Boolean        @default(true)
  isVerified        Boolean        @default(false)
  emailVerifiedAt   DateTime?
  lastLoginAt       DateTime?
  subscriptionId    String?        @unique
  stripeCustomerId  String?        @unique
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt

  // Relations
  subscription      Subscription?  @relation(fields: [subscriptionId], references: [id])
  auditLogs         AuditLog[]
  clients           Client[]
  expenses          Expense[]
  invoices          Invoice[]
  notifications     Notification[]
  taxReports        TaxReport[]
  transactions      Transaction[]
  paymentMethods    PaymentMethod[]
  usageMetrics      UsageMetric[]

  @@map("users")
}

model Client {
  id        String    @id @default(cuid())
  userId    String
  name      String
  email     String?
  phone     String?
  company   String?
  address   String?
  city      String?
  state     String?
  zipCode   String?
  country   String    @default("US")
  taxId     String?
  notes     String?
  isActive  Boolean   @default(true)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  invoices  Invoice[]

  @@map("clients")
}

model Invoice {
  id            String        @id @default(cuid())
  userId        String
  clientId      String
  invoiceNumber String        @unique
  title         String?
  description   String?
  issueDate     DateTime      @default(now())
  dueDate       DateTime
  status        InvoiceStatus @default(DRAFT)
  subtotal      Decimal       @db.Decimal(10, 2)
  taxRate       Decimal       @default(0) @db.Decimal(5, 2)
  taxAmount     Decimal       @default(0) @db.Decimal(10, 2)
  total         Decimal       @db.Decimal(10, 2)
  currency      String        @default("USD")
  notes         String?
  terms         String?
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  items         InvoiceItem[]
  client        Client        @relation(fields: [clientId], references: [id], onDelete: Cascade)
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions  Transaction[]

  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoiceId   String
  description String
  quantity    Decimal  @db.Decimal(10, 2)
  rate        Decimal  @db.Decimal(10, 2)
  amount      Decimal  @db.Decimal(10, 2)
  taxRate     Decimal  @default(0) @db.Decimal(5, 2)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}

model Expense {
  id           String          @id @default(cuid())
  userId       String
  category     ExpenseCategory
  description  String
  amount       Decimal         @db.Decimal(10, 2)
  currency     String          @default("USD")
  date         DateTime        @default(now())
  receipt      String?
  notes        String?
  isRecurring  Boolean         @default(false)
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  user         User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions Transaction[]

  @@map("expenses")
}

model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Decimal           @db.Decimal(10, 2)
  currency    String            @default("USD")
  description String
  date        DateTime          @default(now())
  status      TransactionStatus @default(COMPLETED)
  reference   String?
  invoiceId   String?
  expenseId   String?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  expense     Expense?          @relation(fields: [expenseId], references: [id])
  invoice     Invoice?          @relation(fields: [invoiceId], references: [id])
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

model TaxReport {
  id            String          @id @default(cuid())
  userId        String
  period        String
  year          Int
  quarter       Int?
  totalIncome   Decimal         @db.Decimal(10, 2)
  totalExpenses Decimal         @db.Decimal(10, 2)
  taxableIncome Decimal         @db.Decimal(10, 2)
  taxDue        Decimal         @db.Decimal(10, 2)
  status        TaxReportStatus @default(DRAFT)
  filedAt       DateTime?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tax_reports")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  data      Json?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String
  action     String
  resource   String
  resourceId String?
  oldValues  Json?
  newValues  Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("audit_logs")
}

enum UserRole {
  USER
  ADMIN
}

enum InvoiceStatus {
  DRAFT
  SENT
  VIEWED
  PAID
  OVERDUE
  CANCELLED
}

enum ExpenseCategory {
  OFFICE_SUPPLIES
  TRAVEL
  MEALS
  UTILITIES
  RENT
  INSURANCE
  MARKETING
  PROFESSIONAL_SERVICES
  SOFTWARE
  EQUIPMENT
  OTHER
}

enum TransactionType {
  INCOME
  EXPENSE
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum TaxReportStatus {
  DRAFT
  GENERATED
  FILED
}

enum NotificationType {
  INVOICE_SENT
  INVOICE_PAID
  INVOICE_OVERDUE
  EXPENSE_ADDED
  TAX_REMINDER
  SYSTEM_UPDATE
  SUBSCRIPTION_CREATED
  SUBSCRIPTION_RENEWED
  SUBSCRIPTION_CANCELLED
  SUBSCRIPTION_EXPIRED
  PAYMENT_FAILED
  PAYMENT_SUCCESS
}

// Subscription Management Models
model SubscriptionPlan {
  id            String         @id @default(cuid())
  name          String         @unique
  description   String?
  price         Decimal        @db.Decimal(10, 2)
  currency      String         @default("USD")
  interval      PlanInterval   // MONTHLY, YEARLY, LIFETIME
  features      Json           // JSON array of features
  maxInvoices   Int?           // null for unlimited
  maxClients    Int?           // null for unlimited
  maxUsers      Int            @default(1)
  isActive      Boolean        @default(true)
  stripePriceId String?        @unique
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  subscriptions Subscription[]

  @@map("subscription_plans")
}

model Subscription {
  id                String             @id @default(cuid())
  userId            String             @unique
  planId            String
  status            SubscriptionStatus @default(ACTIVE)
  stripeSubscriptionId String?         @unique
  currentPeriodStart DateTime
  currentPeriodEnd   DateTime
  cancelAtPeriodEnd  Boolean           @default(false)
  cancelledAt        DateTime?
  trialStart         DateTime?
  trialEnd           DateTime?
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt

  // Relations
  user               User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan               SubscriptionPlan   @relation(fields: [planId], references: [id])
  payments           Payment[]
  usageMetrics       UsageMetric[]

  @@map("subscriptions")
}

model Payment {
  id                 String        @id @default(cuid())
  subscriptionId     String
  amount             Decimal       @db.Decimal(10, 2)
  currency           String        @default("USD")
  status             PaymentStatus @default(PENDING)
  paymentMethod      String        // stripe, razorpay, etc.
  stripePaymentId    String?       @unique
  razorpayPaymentId  String?       @unique
  failureReason      String?
  paidAt             DateTime?
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt

  // Relations
  subscription       Subscription  @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@map("payments")
}

model PaymentMethod {
  id               String  @id @default(cuid())
  userId           String
  type             String  // card, upi, netbanking, wallet
  provider         String  // stripe, razorpay
  stripeMethodId   String? @unique
  razorpayMethodId String? @unique
  last4            String?
  brand            String?
  expiryMonth      Int?
  expiryYear       Int?
  isDefault        Boolean @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  user             User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payment_methods")
}

model UsageMetric {
  id             String   @id @default(cuid())
  userId         String
  subscriptionId String
  metric         String   // invoices_created, clients_created, etc.
  value          Int      @default(0)
  period         String   // YYYY-MM format
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@unique([userId, subscriptionId, metric, period])
  @@map("usage_metrics")
}

// Admin Models
model AdminUser {
  id          String    @id @default(cuid())
  email       String    @unique
  password    String
  firstName   String
  lastName    String
  role        AdminRole @default(ADMIN)
  isActive    Boolean   @default(true)
  lastLoginAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("admin_users")
}

// Enums for new models
enum PlanInterval {
  MONTHLY
  YEARLY
  LIFETIME
}

enum SubscriptionStatus {
  ACTIVE
  CANCELLED
  EXPIRED
  PAST_DUE
  TRIALING
  INCOMPLETE
}

enum PaymentStatus {
  PENDING
  SUCCESS
  FAILED
  CANCELLED
  REFUNDED
}

enum AdminRole {
  SUPER_ADMIN
  ADMIN
  SUPPORT
}
