{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^4.1.10", "vite": "^6.3.5"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-react": "^4.5.2", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.2", "yup": "^1.6.1"}}