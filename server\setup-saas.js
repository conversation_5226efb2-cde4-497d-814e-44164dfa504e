const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function setupSaaS() {
  try {
    console.log('🚀 Setting up SaaS platform...');

    // Test database connection
    await prisma.$connect();
    console.log('✅ Database connected successfully');

    // Create subscription plans
    console.log('📋 Creating subscription plans...');
    
    const plans = [
      {
        id: 'plan_starter_monthly',
        name: 'Starter',
        description: 'Perfect for freelancers and solo entrepreneurs',
        price: 2.00,
        currency: 'USD',
        interval: 'MONTHLY',
        features: [
          'Up to 50 invoices per month',
          'Up to 25 clients',
          'Basic expense tracking',
          'Email support',
          'Mobile app access',
          'Basic reporting'
        ],
        maxInvoices: 50,
        maxClients: 25,
        maxUsers: 1,
        isActive: true
      },
      {
        id: 'plan_professional_yearly',
        name: 'Professional',
        description: 'Best for growing businesses and teams',
        price: 20.00,
        currency: 'USD',
        interval: 'YEARLY',
        features: [
          'Unlimited invoices',
          'Unlimited clients',
          'Advanced expense tracking',
          'Priority support',
          'Multi-currency support',
          'Advanced reporting & analytics',
          'Tax compliance tools',
          'API access',
          'Custom branding'
        ],
        maxInvoices: null,
        maxClients: null,
        maxUsers: 5,
        isActive: true
      },
      {
        id: 'plan_lifetime',
        name: 'Lifetime',
        description: 'One-time payment for lifetime access',
        price: 100.00,
        currency: 'USD',
        interval: 'LIFETIME',
        features: [
          'Everything in Professional',
          'Lifetime updates',
          'Premium support',
          'White-label option',
          'Advanced integrations',
          'Custom features on request',
          'Priority feature requests'
        ],
        maxInvoices: null,
        maxClients: null,
        maxUsers: 10,
        isActive: true
      }
    ];

    for (const planData of plans) {
      await prisma.subscriptionPlan.upsert({
        where: { id: planData.id },
        update: planData,
        create: planData
      });
    }

    console.log('✅ Subscription plans created');

    // Create admin user
    console.log('👤 Creating admin user...');
    
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const hashedAdminPassword = await bcrypt.hash(adminPassword, 12);
    
    const admin = await prisma.adminUser.upsert({
      where: { email: process.env.ADMIN_EMAIL || '<EMAIL>' },
      update: {},
      create: {
        email: process.env.ADMIN_EMAIL || '<EMAIL>',
        password: hashedAdminPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'SUPER_ADMIN',
        isActive: true
      }
    });

    console.log('✅ Admin user created:', admin.email);

    // Create sample test user with subscription
    console.log('👥 Creating test user...');
    
    const testUserPassword = await bcrypt.hash('password123', 12);
    
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: testUserPassword,
        firstName: 'Test',
        lastName: 'User',
        company: 'Test Company',
        phone: '******-0123',
        country: 'IN',
        currency: 'INR',
        timezone: 'Asia/Kolkata',
        language: 'en',
        isVerified: true,
        isActive: true
      }
    });

    // Create subscription for test user
    const subscription = await prisma.subscription.upsert({
      where: { userId: testUser.id },
      update: {},
      create: {
        userId: testUser.id,
        planId: 'plan_professional_yearly',
        status: 'ACTIVE',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        trialStart: new Date(),
        trialEnd: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // 14 days trial
      }
    });

    console.log('✅ Test user with subscription created');

    // Create sample clients for test user
    const clients = [
      {
        userId: testUser.id,
        name: 'Acme Corporation',
        email: '<EMAIL>',
        phone: '******-0123',
        company: 'Acme Corp',
        address: '123 Business Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        zipCode: '400001',
        country: 'IN'
      },
      {
        userId: testUser.id,
        name: 'Tech Solutions Pvt Ltd',
        email: '<EMAIL>',
        phone: '+91-98765-43210',
        company: 'Tech Solutions Pvt Ltd',
        address: '456 Tech Park',
        city: 'Bangalore',
        state: 'Karnataka',
        zipCode: '560001',
        country: 'IN'
      }
    ];

    for (const clientData of clients) {
      await prisma.client.create({ data: clientData });
    }

    console.log('✅ Sample clients created');

    // Create sample invoice
    const invoice = await prisma.invoice.create({
      data: {
        userId: testUser.id,
        clientId: (await prisma.client.findFirst({ where: { userId: testUser.id } })).id,
        invoiceNumber: 'INV-2024-0001',
        title: 'Web Development Services',
        description: 'Monthly web development and maintenance services',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        status: 'SENT',
        currency: 'INR',
        taxRate: 18, // GST rate for India
        notes: 'Thank you for your business!',
        subtotal: 50000.00,
        taxAmount: 9000.00,
        total: 59000.00,
        items: {
          create: [
            {
              description: 'Frontend Development',
              quantity: 40,
              rate: 750.00,
              amount: 30000.00
            },
            {
              description: 'Backend API Development',
              quantity: 20,
              rate: 1000.00,
              amount: 20000.00
            }
          ]
        }
      }
    });

    console.log('✅ Sample invoice created');

    // Create sample expenses
    const expenses = [
      {
        userId: testUser.id,
        category: 'SOFTWARE',
        description: 'Adobe Creative Suite Subscription',
        amount: 1699.00,
        currency: 'INR',
        date: new Date()
      },
      {
        userId: testUser.id,
        category: 'OFFICE_SUPPLIES',
        description: 'Office Supplies and Stationery',
        amount: 2500.00,
        currency: 'INR',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },
      {
        userId: testUser.id,
        category: 'MEALS',
        description: 'Business Lunch with Client',
        amount: 1200.00,
        currency: 'INR',
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      }
    ];

    await prisma.expense.createMany({ data: expenses });
    console.log('✅ Sample expenses created');

    // Create usage metrics
    await prisma.usageMetric.create({
      data: {
        userId: testUser.id,
        subscriptionId: subscription.id,
        metric: 'invoices_created',
        value: 1,
        period: new Date().toISOString().slice(0, 7) // YYYY-MM format
      }
    });

    console.log('✅ Usage metrics initialized');

    console.log('\n🎉 SaaS platform setup complete!');
    console.log('\n📋 Admin Account:');
    console.log(`Email: ${admin.email}`);
    console.log(`Password: ${adminPassword}`);
    console.log('\n📋 Test User Account:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('\n🌐 Access URLs:');
    console.log('Landing Page: http://localhost:5173');
    console.log('Admin Dashboard: http://localhost:5173/admin');
    console.log('User Dashboard: http://localhost:5173/dashboard');
    console.log('\n💳 Payment Integration:');
    console.log('- Configure Stripe keys in .env for international payments');
    console.log('- Configure Razorpay keys in .env for Indian payments');
    console.log('\n🚀 Ready to launch your SaaS platform!');

  } catch (error) {
    console.error('❌ SaaS setup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

setupSaaS();
