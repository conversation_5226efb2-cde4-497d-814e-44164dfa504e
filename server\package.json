{"name": "financial-management-server", "version": "1.0.0", "description": "Backend API for Financial Management SaaS", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "node src/utils/seed.js", "db:reset": "npx prisma migrate reset"}, "dependencies": {"@prisma/client": "^5.6.0", "@types/pg": "^8.15.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "pdfkit": "^0.13.0", "pg": "^8.16.0", "prisma": "^5.6.0", "razorpay": "^2.9.6", "stripe": "^14.25.0"}, "devDependencies": {"@types/jest": "^29.5.8", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/index.js"]}}