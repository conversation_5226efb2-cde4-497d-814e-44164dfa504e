# 🗄️ Database Setup Guide

## Step 1: Check PostgreSQL Installation

First, verify if PostgreSQL is installed and running:

```bash
# Check if PostgreSQL is installed
psql --version

# Check if PostgreSQL service is running (Windows)
Get-Service postgresql*

# Check if PostgreSQL service is running (macOS/Linux)
brew services list | grep postgresql
# or
sudo systemctl status postgresql
```

## Step 2: Install PostgreSQL (if not installed)

### Windows:
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user
4. Default port is usually 5432

### macOS:
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create a database user (optional)
createuser -s postgres
```

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## Step 3: Find Your PostgreSQL Credentials

### Option A: Use Default Postgres User
Most installations have a default `postgres` user. Try these common scenarios:

1. **No password (common on macOS/Linux):**
   ```
   DATABASE_URL="postgresql://postgres@localhost:5432/financial_management_db"
   ```

2. **With password (common on Windows):**
   ```
   DATABASE_URL="postgresql://postgres:your_password@localhost:5432/financial_management_db"
   ```

3. **Custom user:**
   ```
   DATABASE_URL="postgresql://your_username:your_password@localhost:5432/financial_management_db"
   ```

### Option B: Check Current PostgreSQL Users
```bash
# Connect to PostgreSQL as superuser
sudo -u postgres psql

# List all users
\du

# Exit PostgreSQL
\q
```

## Step 4: Create Database and User (if needed)

### Method 1: Using Command Line
```bash
# Create database
createdb financial_management_db

# Or if you need to specify user
sudo -u postgres createdb financial_management_db
```

### Method 2: Using PostgreSQL Console
```bash
# Connect to PostgreSQL
psql -U postgres

# Create database
CREATE DATABASE financial_management_db;

# Create user (optional)
CREATE USER fin_user WITH PASSWORD 'secure_password';

# Grant privileges
GRANT ALL PRIVILEGES ON DATABASE financial_management_db TO fin_user;

# Exit
\q
```

## Step 5: Test Database Connection

Create a test script to verify your connection:

```bash
# Test connection with psql
psql -U postgres -d financial_management_db -h localhost -p 5432

# If successful, you should see:
# financial_management_db=#
```

## Step 6: Update Environment File

Based on your setup, update `server/.env`:

### Scenario 1: Default postgres user, no password
```env
DATABASE_URL="postgresql://postgres@localhost:5432/financial_management_db"
```

### Scenario 2: Default postgres user with password
```env
DATABASE_URL="postgresql://postgres:your_actual_password@localhost:5432/financial_management_db"
```

### Scenario 3: Custom user
```env
DATABASE_URL="postgresql://fin_user:secure_password@localhost:5432/financial_management_db"
```

### Scenario 4: Different port
```env
DATABASE_URL="postgresql://postgres:password@localhost:5433/financial_management_db"
```

## Step 7: Run Database Migration

After updating the DATABASE_URL:

```bash
cd server
npx prisma migrate dev --name init
npx prisma generate
```

## Common Issues and Solutions

### Issue 1: "role 'admin' does not exist"
**Solution:** The user 'admin' doesn't exist. Use 'postgres' or create the user:
```sql
CREATE USER admin WITH PASSWORD 'admin';
ALTER USER admin CREATEDB;
```

### Issue 2: "database 'financial_management_db' does not exist"
**Solution:** Create the database:
```bash
createdb financial_management_db
# or
psql -U postgres -c "CREATE DATABASE financial_management_db;"
```

### Issue 3: "password authentication failed"
**Solution:** Check your password or reset it:
```bash
# Reset postgres user password
sudo -u postgres psql
ALTER USER postgres PASSWORD 'new_password';
```

### Issue 4: "could not connect to server"
**Solution:** PostgreSQL service is not running:
```bash
# Windows
net start postgresql-x64-13

# macOS
brew services start postgresql

# Linux
sudo systemctl start postgresql
```

### Issue 5: Connection refused on port 5432
**Solution:** Check if PostgreSQL is running on a different port:
```bash
# Check what's running on port 5432
netstat -an | grep 5432

# Check PostgreSQL configuration
sudo -u postgres psql -c "SHOW port;"
```

## Quick Setup Commands

### For Windows (typical setup):
```bash
# 1. Install PostgreSQL from official website
# 2. Use the password you set during installation
# 3. Update .env file:
DATABASE_URL="postgresql://postgres:your_install_password@localhost:5432/financial_management_db"

# 4. Create database
psql -U postgres -c "CREATE DATABASE financial_management_db;"

# 5. Run migrations
cd server
npx prisma migrate dev --name init
```

### For macOS with Homebrew:
```bash
# 1. Install PostgreSQL
brew install postgresql
brew services start postgresql

# 2. Create database
createdb financial_management_db

# 3. Update .env file:
DATABASE_URL="postgresql://postgres@localhost:5432/financial_management_db"

# 4. Run migrations
cd server
npx prisma migrate dev --name init
```

### For Linux (Ubuntu/Debian):
```bash
# 1. Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# 2. Switch to postgres user and create database
sudo -u postgres createdb financial_management_db

# 3. Update .env file:
DATABASE_URL="postgresql://postgres@localhost:5432/financial_management_db"

# 4. Run migrations
cd server
npx prisma migrate dev --name init
```

## Verification Steps

After setup, verify everything works:

1. **Test database connection:**
   ```bash
   cd server
   npx prisma db pull
   ```

2. **Check if tables were created:**
   ```bash
   psql -U postgres -d financial_management_db -c "\dt"
   ```

3. **Run the application:**
   ```bash
   npm run dev
   ```

## Need Help?

If you're still having issues, please share:
1. Your operating system
2. PostgreSQL version (`psql --version`)
3. The exact error message
4. Your current DATABASE_URL (without the password)

This will help me provide more specific guidance!
