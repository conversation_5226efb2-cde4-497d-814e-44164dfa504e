import React from 'react';

function App() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🚀 Financial Management SaaS
          </h1>
          <p className="text-gray-600 mb-6">
            Platform is loading successfully!
          </p>
          <div className="space-y-4">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              ✅ React is working
            </div>
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
              ✅ Tailwind CSS is working
            </div>
            <div className="bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded">
              ✅ Vite is working
            </div>
          </div>
          <div className="mt-6">
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors">
              Test Button
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
