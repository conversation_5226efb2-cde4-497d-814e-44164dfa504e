import React from "react";

function App() {
  return (
    <div
      style={{
        minHeight: "100vh",
        backgroundColor: "#f9fafb",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontFamily: "Arial, sans-serif",
      }}
    >
      <div
        style={{
          maxWidth: "400px",
          width: "100%",
          backgroundColor: "white",
          borderRadius: "8px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
          padding: "32px",
          textAlign: "center",
        }}
      >
        <h1
          style={{
            fontSize: "24px",
            fontWeight: "bold",
            color: "#111827",
            marginBottom: "16px",
          }}
        >
          🚀 Financial Management SaaS
        </h1>
        <p
          style={{
            color: "#6b7280",
            marginBottom: "24px",
          }}
        >
          Platform is loading successfully!
        </p>
        <div style={{ marginBottom: "16px" }}>
          <div
            style={{
              backgroundColor: "#dcfce7",
              border: "1px solid #16a34a",
              color: "#15803d",
              padding: "12px",
              borderRadius: "4px",
              marginBottom: "8px",
            }}
          >
            ✅ React is working
          </div>
          <div
            style={{
              backgroundColor: "#dbeafe",
              border: "1px solid #2563eb",
              color: "#1d4ed8",
              padding: "12px",
              borderRadius: "4px",
              marginBottom: "8px",
            }}
          >
            ✅ Vite is working
          </div>
          <div
            style={{
              backgroundColor: "#fef3c7",
              border: "1px solid #d97706",
              color: "#92400e",
              padding: "12px",
              borderRadius: "4px",
            }}
          >
            ⚠️ Tailwind CSS needs fixing
          </div>
        </div>
        <button
          style={{
            backgroundColor: "#2563eb",
            color: "white",
            fontWeight: "bold",
            padding: "8px 16px",
            borderRadius: "4px",
            border: "none",
            cursor: "pointer",
          }}
        >
          Test Button
        </button>
      </div>
    </div>
  );
}

export default App;
