# Financial Management SaaS - Setup Instructions

## 🚀 Quick Start Guide

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- npm or yarn

### 1. Install Dependencies

```bash
# Install root dependencies
npm install

# Install server dependencies
cd server
npm install

# Install client dependencies
cd ../client
npm install
```

### 2. Database Setup

1. Create a PostgreSQL database named `financial_management_db`
2. Update the `DATABASE_URL` in `server/.env` with your database credentials
3. Run database migrations:

```bash
cd server
npx prisma migrate dev
npx prisma generate
```

### 3. Environment Configuration

#### Server Environment
Copy `server/.env.example` to `server/.env` and update the values:

```bash
cd server
cp .env.example .env
```

Key variables to update:
- `DATABASE_URL`: Your PostgreSQL connection string
- `JWT_SECRET`: A secure random string for JWT tokens
- `SMTP_*`: Email configuration for notifications (optional)

#### Client Environment
Copy `client/.env.example` to `client/.env`:

```bash
cd client
cp .env.example .env
```

### 4. Start the Application

#### Option 1: Start both services together (from root directory)
```bash
npm run dev
```

#### Option 2: Start services separately

**Start the backend server:**
```bash
cd server
npm run dev
```

**Start the frontend client (in a new terminal):**
```bash
cd client
npm run dev
```

### 5. Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3000
- **API Health Check**: http://localhost:3000/health

### 6. Default Test Account

You can register a new account or use these test credentials:
- Email: <EMAIL>
- Password: password123

## 📁 Project Structure

```
financial-management-saas/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── context/        # React context providers
│   │   └── utils/          # Utility functions
│   └── package.json
├── server/                 # Node.js backend
│   ├── src/
│   │   ├── controllers/    # Route controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── prisma/             # Database schema and migrations
│   └── package.json
└── package.json           # Root package.json
```

## 🔧 Development Commands

### Root Level
- `npm run dev` - Start both client and server
- `npm run setup` - Install all dependencies
- `npm test` - Run all tests

### Server
- `npm run dev` - Start development server with nodemon
- `npm start` - Start production server
- `npm test` - Run server tests
- `npm run db:migrate` - Run database migrations
- `npm run db:generate` - Generate Prisma client
- `npm run db:seed` - Seed database with sample data

### Client
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run client tests

## 🌟 Features Implemented

### ✅ Core Features
- User authentication (register/login)
- Dashboard with financial overview
- Client management
- Invoice creation and management
- Expense tracking and categorization
- Transaction history
- Tax calculation and reporting
- Responsive design with Tailwind CSS

### 🚧 In Development
- PDF invoice generation
- Email notifications
- Advanced reporting and analytics
- File upload for receipts
- Recurring invoices
- Multi-currency support
- Bank integration

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Input validation and sanitization
- SQL injection prevention with Prisma
- XSS protection

## 📊 Database Schema

The application uses PostgreSQL with Prisma ORM. Key entities include:

- **Users**: User accounts and authentication
- **Clients**: Customer information
- **Invoices**: Invoice records with line items
- **Expenses**: Business expense tracking
- **Transactions**: Financial transaction history
- **Tax Reports**: Generated tax calculations
- **Notifications**: System notifications

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check DATABASE_URL in server/.env
   - Run `npx prisma migrate dev` to create tables

2. **Port Already in Use**
   - Change PORT in server/.env (default: 3000)
   - Change port in client/vite.config.js (default: 5173)

3. **CORS Errors**
   - Ensure FRONTEND_URL in server/.env matches client URL
   - Check that both services are running

4. **Authentication Issues**
   - Verify JWT_SECRET is set in server/.env
   - Clear browser localStorage and try again

### Getting Help

If you encounter issues:
1. Check the console for error messages
2. Verify all environment variables are set correctly
3. Ensure all dependencies are installed
4. Check that the database is running and accessible

## 📝 API Documentation

The API follows RESTful conventions. Key endpoints:

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/forgot-password` - Request password reset

### Users
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/change-password` - Change password

### Clients
- `GET /api/clients` - List clients
- `POST /api/clients` - Create client
- `GET /api/clients/:id` - Get client details
- `PUT /api/clients/:id` - Update client
- `DELETE /api/clients/:id` - Delete client

### Invoices
- `GET /api/invoices` - List invoices
- `POST /api/invoices` - Create invoice
- `GET /api/invoices/:id` - Get invoice details
- `PUT /api/invoices/:id` - Update invoice
- `DELETE /api/invoices/:id` - Delete invoice

### Expenses
- `GET /api/expenses` - List expenses
- `POST /api/expenses` - Create expense
- `GET /api/expenses/:id` - Get expense details
- `PUT /api/expenses/:id` - Update expense
- `DELETE /api/expenses/:id` - Delete expense

### Transactions
- `GET /api/transactions` - List transactions
- `GET /api/transactions/:id` - Get transaction details
- `GET /api/transactions/summary/monthly` - Monthly summary
- `GET /api/transactions/cash-flow` - Cash flow data

### Tax Reports
- `GET /api/tax/reports` - List tax reports
- `POST /api/tax/calculate` - Calculate tax for period
- `POST /api/tax/reports` - Generate tax report

### Dashboard
- `GET /api/dashboard/overview` - Dashboard overview data
- `GET /api/dashboard/charts` - Chart data
- `GET /api/dashboard/metrics` - Key metrics

## 🚀 Deployment

### Production Build

1. **Build the client:**
```bash
cd client
npm run build
```

2. **Set production environment variables**
3. **Deploy to your preferred hosting platform**

### Environment Variables for Production

Ensure these are set in production:
- `NODE_ENV=production`
- `DATABASE_URL` (production database)
- `JWT_SECRET` (secure random string)
- `FRONTEND_URL` (production frontend URL)

## 📄 License

This project is licensed under the MIT License.
