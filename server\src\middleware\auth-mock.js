const jwt = require("jsonwebtoken");

/**
 * Mock middleware to authenticate JWT tokens (works without database)
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: "Access denied",
        message: "No token provided",
      });
    }

    // Verify the token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "fallback-secret"
    );

    // Mock user data (since we don't have database)
    const mockUser = {
      id: "user_1", // Fixed user ID for consistency
      userId: "user_1", // Also add userId for compatibility
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User",
      role: "USER",
      isActive: true,
      isVerified: true,
    };

    // Add user info to request object
    req.user = mockUser;
    next();
  } catch (error) {
    if (error.name === "JsonWebTokenError") {
      return res.status(401).json({
        error: "Access denied",
        message: "Invalid token",
      });
    }

    if (error.name === "TokenExpiredError") {
      return res.status(401).json({
        error: "Access denied",
        message: "Token expired",
      });
    }

    console.error("Authentication error:", error);
    return res.status(500).json({
      error: "Internal server error",
      message: "Authentication failed",
    });
  }
};

/**
 * Middleware to check if user has admin role
 */
const requireAdmin = (req, res, next) => {
  if (req.user.role !== "ADMIN") {
    return res.status(403).json({
      error: "Access denied",
      message: "Admin privileges required",
    });
  }
  next();
};

/**
 * Middleware to check if user is verified
 */
const requireVerified = (req, res, next) => {
  if (!req.user.isVerified) {
    return res.status(403).json({
      error: "Access denied",
      message: "Email verification required",
    });
  }
  next();
};

/**
 * Generate JWT token for user
 */
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET || "fallback-secret", {
    expiresIn: process.env.JWT_EXPIRES_IN || "7d",
  });
};

/**
 * Extract user ID from token without verification (for optional auth)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1];

    if (token) {
      const decoded = jwt.verify(
        token,
        process.env.JWT_SECRET || "fallback-secret"
      );
      const mockUser = {
        id: decoded.userId,
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
        role: "USER",
        isActive: true,
        isVerified: true,
      };

      req.user = mockUser;
    }
  } catch (error) {
    // Ignore errors for optional auth
  }

  next();
};

module.exports = {
  authenticateToken,
  requireAdmin,
  requireVerified,
  generateToken,
  optionalAuth,
};
