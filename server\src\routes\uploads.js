const express = require("express");
const path = require("path");
const { body, validationResult } = require("express-validator");
const { PrismaClient } = require("@prisma/client");
const { asyncHandler, AppError } = require("../middleware/errorHandler");
const fileUploadService = require("../services/fileUploadService");

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @route   POST /api/uploads/receipt
 * @desc    Upload receipt file
 * @access  Private
 */
router.post(
  "/receipt",
  fileUploadService.single("receipt"),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      throw new AppError("No file uploaded", 400);
    }

    // Validate file type for receipts
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "application/pdf",
    ];
    fileUploadService.validateFileType(req.file, allowedTypes);

    // Process the uploaded file
    const fileInfo = fileUploadService.processUploadedFile(
      req.file,
      req.user.id
    );

    res.json({
      message: "Receipt uploaded successfully",
      file: fileInfo,
    });
  })
);

/**
 * @route   POST /api/uploads/receipts
 * @desc    Upload multiple receipt files
 * @access  Private
 */
router.post(
  "/receipts",
  fileUploadService.multiple("receipts", 5),
  asyncHandler(async (req, res) => {
    if (!req.files || req.files.length === 0) {
      throw new AppError("No files uploaded", 400);
    }

    // Validate file types
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "application/pdf",
    ];
    req.files.forEach((file) => {
      fileUploadService.validateFileType(file, allowedTypes);
    });

    // Process the uploaded files
    const filesInfo = fileUploadService.processUploadedFiles(
      req.files,
      req.user.id
    );

    res.json({
      message: `${filesInfo.length} receipts uploaded successfully`,
      files: filesInfo,
    });
  })
);

/**
 * @route   POST /api/uploads/expense-receipt/:expenseId
 * @desc    Upload receipt for specific expense
 * @access  Private
 */
router.post(
  "/expense-receipt/:expenseId",
  fileUploadService.single("receipt"),
  asyncHandler(async (req, res) => {
    if (!req.file) {
      throw new AppError("No file uploaded", 400);
    }

    // Check if expense exists and belongs to user
    const expense = await prisma.expense.findFirst({
      where: {
        id: req.params.expenseId,
        userId: req.user.id,
      },
    });

    if (!expense) {
      throw new AppError("Expense not found", 404);
    }

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "application/pdf",
    ];
    fileUploadService.validateFileType(req.file, allowedTypes);

    // Process the uploaded file
    const fileInfo = fileUploadService.processUploadedFile(
      req.file,
      req.user.id
    );

    // Update expense with receipt path
    await prisma.expense.update({
      where: { id: req.params.expenseId },
      data: { receipt: fileInfo.url },
    });

    res.json({
      message: "Receipt uploaded and linked to expense successfully",
      file: fileInfo,
    });
  })
);

/**
 * @route   DELETE /api/uploads/:filename
 * @desc    Delete uploaded file
 * @access  Private
 */
router.delete(
  "/:filename",
  asyncHandler(async (req, res) => {
    const filename = req.params.filename;
    const filepath = path.join(__dirname, "../../uploads", filename);

    // Check if file exists
    const fileInfo = fileUploadService.getFileInfo(filepath);
    if (!fileInfo.exists) {
      throw new AppError("File not found", 404);
    }

    // Delete the file
    await fileUploadService.deleteFile(filepath);

    res.json({
      message: "File deleted successfully",
    });
  })
);

/**
 * @route   GET /api/uploads/stats
 * @desc    Get upload statistics
 * @access  Private
 */
router.get(
  "/stats",
  asyncHandler(async (req, res) => {
    const uploadsDir = path.join(__dirname, "../../uploads");
    const receiptsDir = path.join(uploadsDir, "receipts");
    const invoicesDir = path.join(uploadsDir, "invoices");
    const reportsDir = path.join(uploadsDir, "reports");

    const stats = {
      total: fileUploadService.getUploadStats(uploadsDir),
      receipts: fileUploadService.getUploadStats(receiptsDir),
      invoices: fileUploadService.getUploadStats(invoicesDir),
      reports: fileUploadService.getUploadStats(reportsDir),
    };

    res.json({
      message: "Upload statistics retrieved successfully",
      stats,
    });
  })
);

module.exports = router;
