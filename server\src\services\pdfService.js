const PDFDocument = require("pdfkit");
const fs = require("fs");
const path = require("path");

class PDFService {
  constructor() {
    this.ensureUploadsDirectory();
  }

  ensureUploadsDirectory() {
    const uploadsDir = path.join(__dirname, "../../uploads");
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
  }

  async generateInvoicePDF(invoice, user) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 });
        const filename = `invoice-${invoice.invoiceNumber}.pdf`;
        const filepath = path.join(__dirname, "../../uploads", filename);

        // Create write stream
        const stream = fs.createWriteStream(filepath);
        doc.pipe(stream);

        // Simple header
        doc.fontSize(20).font("Helvetica-Bold").text("INVOICE", 50, 50);

        // Business info
        doc
          .fontSize(12)
          .font("Helvetica")
          .text(`From: ${user.firstName} ${user.lastName}`, 50, 100)
          .text(`Email: ${user.email}`, 50, 120);

        // Invoice details
        doc
          .text(`Invoice #: ${invoice.invoiceNumber}`, 350, 100)
          .text(
            `Date: ${new Date(invoice.issueDate).toLocaleDateString()}`,
            350,
            120
          )
          .text(
            `Due: ${new Date(invoice.dueDate).toLocaleDateString()}`,
            350,
            140
          );

        // Client info
        doc
          .text(`Bill To:`, 50, 180)
          .text(`${invoice.client.name}`, 50, 200)
          .text(`${invoice.client.email || ""}`, 50, 220);

        // Items table header
        let currentY = 280;
        doc
          .fontSize(10)
          .font("Helvetica-Bold")
          .text("Description", 50, currentY)
          .text("Qty", 300, currentY)
          .text("Rate", 350, currentY)
          .text("Amount", 450, currentY);

        // Draw line
        currentY += 20;
        doc.moveTo(50, currentY).lineTo(500, currentY).stroke();

        // Items
        currentY += 10;
        doc.font("Helvetica");
        invoice.items.forEach((item) => {
          currentY += 20;
          doc
            .text(item.description, 50, currentY)
            .text(item.quantity.toString(), 300, currentY)
            .text(`$${parseFloat(item.rate).toFixed(2)}`, 350, currentY)
            .text(`$${parseFloat(item.amount).toFixed(2)}`, 450, currentY);
        });

        // Totals
        currentY += 40;
        doc.text(
          `Subtotal: $${parseFloat(invoice.subtotal).toFixed(2)}`,
          350,
          currentY
        );
        currentY += 20;
        doc.text(
          `Tax (${invoice.taxRate}%): $${parseFloat(invoice.taxAmount).toFixed(
            2
          )}`,
          350,
          currentY
        );
        currentY += 20;
        doc
          .font("Helvetica-Bold")
          .text(
            `Total: $${parseFloat(invoice.total).toFixed(2)}`,
            350,
            currentY
          );

        // Notes
        if (invoice.notes) {
          currentY += 60;
          doc
            .font("Helvetica")
            .text("Notes:", 50, currentY)
            .text(invoice.notes, 50, currentY + 20, { width: 400 });
        }

        // Finalize
        doc.end();

        stream.on("finish", () => {
          resolve({
            filename,
            filepath,
            url: `/uploads/${filename}`,
          });
        });

        stream.on("error", (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  addHeader(doc, user) {
    // Company name and logo area
    doc
      .fontSize(24)
      .font("Helvetica-Bold")
      .text(user.company || `${user.firstName} ${user.lastName}`, 50, 50);

    // Company details
    doc
      .fontSize(10)
      .font("Helvetica")
      .text(user.address || "", 50, 80)
      .text(
        `${user.city || ""} ${user.state || ""} ${user.zipCode || ""}`,
        50,
        95
      )
      .text(user.country || "", 50, 110)
      .text(`Email: ${user.email}`, 50, 125)
      .text(`Phone: ${user.phone || "N/A"}`, 50, 140);

    // Invoice title
    doc
      .fontSize(20)
      .font("Helvetica-Bold")
      .text("INVOICE", 400, 50, { align: "right" });

    // Add a line separator
    doc.moveTo(50, 170).lineTo(550, 170).stroke();
  }

  addInvoiceDetails(doc, invoice) {
    const startY = 190;

    doc
      .fontSize(12)
      .font("Helvetica-Bold")
      .text("Invoice Details:", 50, startY);

    doc
      .fontSize(10)
      .font("Helvetica")
      .text(`Invoice Number: ${invoice.invoiceNumber}`, 50, startY + 20)
      .text(
        `Issue Date: ${new Date(invoice.issueDate).toLocaleDateString()}`,
        50,
        startY + 35
      )
      .text(
        `Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}`,
        50,
        startY + 50
      )
      .text(`Status: ${invoice.status}`, 50, startY + 65);

    if (invoice.title) {
      doc.text(`Title: ${invoice.title}`, 50, startY + 80);
    }
  }

  addClientDetails(doc, client) {
    const startY = 190;

    doc.fontSize(12).font("Helvetica-Bold").text("Bill To:", 300, startY);

    doc
      .fontSize(10)
      .font("Helvetica")
      .text(client.name, 300, startY + 20)
      .text(client.company || "", 300, startY + 35)
      .text(client.address || "", 300, startY + 50)
      .text(
        `${client.city || ""} ${client.state || ""} ${client.zipCode || ""}`,
        300,
        startY + 65
      )
      .text(client.country || "", 300, startY + 80)
      .text(`Email: ${client.email || "N/A"}`, 300, startY + 95)
      .text(`Phone: ${client.phone || "N/A"}`, 300, startY + 110);
  }

  addInvoiceItemsTable(doc, items) {
    const tableTop = 320;
    const tableLeft = 50;

    // Table headers
    doc.fontSize(10).font("Helvetica-Bold");

    this.generateTableRow(
      doc,
      tableTop,
      "Description",
      "Qty",
      "Rate",
      "Tax Rate",
      "Amount"
    );

    // Table header line
    doc
      .moveTo(tableLeft, tableTop + 15)
      .lineTo(550, tableTop + 15)
      .stroke();

    // Table rows
    doc.font("Helvetica");
    let currentY = tableTop + 25;

    items.forEach((item, index) => {
      this.generateTableRow(
        doc,
        currentY,
        item.description,
        item.quantity.toString(),
        `$${parseFloat(item.rate).toFixed(2)}`,
        `${parseFloat(item.taxRate).toFixed(1)}%`,
        `$${parseFloat(item.amount).toFixed(2)}`
      );
      currentY += 20;
    });

    return currentY;
  }

  generateTableRow(doc, y, description, qty, rate, taxRate, amount) {
    doc
      .text(description, 50, y, { width: 200 })
      .text(qty, 260, y, { width: 50, align: "center" })
      .text(rate, 320, y, { width: 80, align: "right" })
      .text(taxRate, 410, y, { width: 60, align: "center" })
      .text(amount, 480, y, { width: 70, align: "right" });
  }

  addInvoiceTotals(doc, invoice) {
    const startY = 450;

    // Subtotal
    doc
      .fontSize(10)
      .font("Helvetica")
      .text("Subtotal:", 400, startY, { align: "right" })
      .text(`$${parseFloat(invoice.subtotal).toFixed(2)}`, 480, startY, {
        align: "right",
      });

    // Tax
    doc
      .text(
        `Tax (${parseFloat(invoice.taxRate).toFixed(1)}%):`,
        400,
        startY + 15,
        { align: "right" }
      )
      .text(`$${parseFloat(invoice.taxAmount).toFixed(2)}`, 480, startY + 15, {
        align: "right",
      });

    // Total line
    doc
      .moveTo(400, startY + 30)
      .lineTo(550, startY + 30)
      .stroke();

    // Total
    doc
      .fontSize(12)
      .font("Helvetica-Bold")
      .text("Total:", 400, startY + 35, { align: "right" })
      .text(`$${parseFloat(invoice.total).toFixed(2)}`, 480, startY + 35, {
        align: "right",
      });
  }

  addFooter(doc, invoice) {
    const startY = 650;

    // Notes
    if (invoice.notes) {
      doc.fontSize(10).font("Helvetica-Bold").text("Notes:", 50, startY);

      doc
        .font("Helvetica")
        .text(invoice.notes, 50, startY + 15, { width: 500 });
    }

    // Terms
    if (invoice.terms) {
      doc
        .fontSize(10)
        .font("Helvetica-Bold")
        .text("Terms & Conditions:", 50, startY + 60);

      doc
        .font("Helvetica")
        .text(invoice.terms, 50, startY + 75, { width: 500 });
    }

    // Footer
    doc
      .fontSize(8)
      .font("Helvetica")
      .text("Thank you for your business!", 50, 750, {
        align: "center",
        width: 500,
      });
  }

  async generateExpenseReport(expenses, user, period) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 });
        const filename = `expense-report-${period
          .replace(/\s+/g, "-")
          .toLowerCase()}.pdf`;
        const filepath = path.join(__dirname, "../../uploads", filename);

        doc.pipe(fs.createWriteStream(filepath));

        // Header
        doc.fontSize(20).font("Helvetica-Bold").text("Expense Report", 50, 50);

        doc
          .fontSize(12)
          .font("Helvetica")
          .text(`Period: ${period}`, 50, 80)
          .text(`Generated: ${new Date().toLocaleDateString()}`, 50, 95)
          .text(
            `Company: ${user.company || `${user.firstName} ${user.lastName}`}`,
            50,
            110
          );

        // Summary
        const totalAmount = expenses.reduce(
          (sum, expense) => sum + parseFloat(expense.amount),
          0
        );
        doc
          .fontSize(14)
          .font("Helvetica-Bold")
          .text(`Total Expenses: $${totalAmount.toFixed(2)}`, 50, 140);

        // Expenses table
        let currentY = 180;
        doc
          .fontSize(10)
          .font("Helvetica-Bold")
          .text("Date", 50, currentY)
          .text("Category", 120, currentY)
          .text("Description", 220, currentY)
          .text("Amount", 450, currentY, { align: "right" });

        currentY += 20;
        doc
          .moveTo(50, currentY - 5)
          .lineTo(500, currentY - 5)
          .stroke();

        doc.font("Helvetica");
        expenses.forEach((expense) => {
          doc
            .text(new Date(expense.date).toLocaleDateString(), 50, currentY)
            .text(expense.category.replace(/_/g, " "), 120, currentY)
            .text(expense.description, 220, currentY, { width: 200 })
            .text(`$${parseFloat(expense.amount).toFixed(2)}`, 450, currentY, {
              align: "right",
            });
          currentY += 15;
        });

        doc.end();

        doc.on("end", () => {
          resolve({
            filename,
            filepath,
            url: `/uploads/${filename}`,
          });
        });

        doc.on("error", (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async generateTaxReport(taxReport, user) {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50 });
        const filename = `tax-report-${taxReport.period}.pdf`;
        const filepath = path.join(__dirname, "../../uploads", filename);

        doc.pipe(fs.createWriteStream(filepath));

        // Header
        doc.fontSize(20).font("Helvetica-Bold").text("Tax Report", 50, 50);

        doc
          .fontSize(12)
          .font("Helvetica")
          .text(`Period: ${taxReport.period}`, 50, 80)
          .text(`Generated: ${new Date().toLocaleDateString()}`, 50, 95)
          .text(
            `Company: ${user.company || `${user.firstName} ${user.lastName}`}`,
            50,
            110
          );

        // Tax summary
        let currentY = 150;
        doc
          .fontSize(14)
          .font("Helvetica-Bold")
          .text("Tax Summary", 50, currentY);

        currentY += 30;
        doc
          .fontSize(12)
          .font("Helvetica")
          .text(
            `Total Income: $${parseFloat(taxReport.totalIncome).toFixed(2)}`,
            50,
            currentY
          )
          .text(
            `Total Expenses: $${parseFloat(taxReport.totalExpenses).toFixed(
              2
            )}`,
            50,
            currentY + 20
          )
          .text(
            `Taxable Income: $${parseFloat(taxReport.taxableIncome).toFixed(
              2
            )}`,
            50,
            currentY + 40
          )
          .text(
            `Tax Due: $${parseFloat(taxReport.taxDue).toFixed(2)}`,
            50,
            currentY + 60
          );

        doc.end();

        doc.on("end", () => {
          resolve({
            filename,
            filepath,
            url: `/uploads/${filename}`,
          });
        });

        doc.on("error", (error) => {
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}

module.exports = new PDFService();
