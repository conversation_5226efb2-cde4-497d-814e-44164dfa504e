const express = require("express");
const { PrismaClient } = require("@prisma/client");
const { authenticateToken } = require("../middleware/auth");
const stripe = require("stripe")(
  process.env.STRIPE_SECRET_KEY || "sk_test_dummy"
);
const Razorpay = require("razorpay");

const router = express.Router();
const prisma = new PrismaClient();

// Initialize Razorpay (for Indian payments)
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

/**
 * @route   GET /api/subscriptions/plans
 * @desc    Get all available subscription plans
 * @access  Public
 */
router.get("/plans", async (req, res) => {
  try {
    const plans = await prisma.subscriptionPlan.findMany({
      where: { isActive: true },
      orderBy: { price: "asc" },
    });

    res.json({
      message: "Subscription plans retrieved successfully",
      plans,
    });
  } catch (error) {
    console.error("Get plans error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to retrieve subscription plans",
    });
  }
});

/**
 * @route   GET /api/subscriptions/current
 * @desc    Get current user's subscription
 * @access  Private
 */
router.get("/current", authenticateToken, async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      include: {
        subscription: {
          include: {
            plan: true,
            payments: {
              orderBy: { createdAt: "desc" },
              take: 5,
            },
          },
        },
      },
    });

    if (!user.subscription) {
      return res.status(404).json({
        error: "Not Found",
        message: "No active subscription found",
      });
    }

    res.json({
      message: "Subscription retrieved successfully",
      subscription: user.subscription,
    });
  } catch (error) {
    console.error("Get subscription error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to retrieve subscription",
    });
  }
});

/**
 * @route   POST /api/subscriptions/create
 * @desc    Create a new subscription
 * @access  Private
 */
router.post("/create", authenticateToken, async (req, res) => {
  try {
    const { planId, paymentMethod = "stripe" } = req.body;

    // Get the plan
    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId },
    });

    if (!plan || !plan.isActive) {
      return res.status(404).json({
        error: "Not Found",
        message: "Subscription plan not found or inactive",
      });
    }

    // Check if user already has an active subscription
    const existingSubscription = await prisma.subscription.findUnique({
      where: { userId: req.user.id },
    });

    if (existingSubscription && existingSubscription.status === "ACTIVE") {
      return res.status(400).json({
        error: "Bad Request",
        message: "User already has an active subscription",
      });
    }

    let subscriptionData = {
      userId: req.user.id,
      planId: plan.id,
      status: "ACTIVE",
      currentPeriodStart: new Date(),
    };

    // Calculate period end based on plan interval
    const now = new Date();
    if (plan.interval === "MONTHLY") {
      subscriptionData.currentPeriodEnd = new Date(
        now.setMonth(now.getMonth() + 1)
      );
    } else if (plan.interval === "YEARLY") {
      subscriptionData.currentPeriodEnd = new Date(
        now.setFullYear(now.getFullYear() + 1)
      );
    } else if (plan.interval === "LIFETIME") {
      subscriptionData.currentPeriodEnd = new Date("2099-12-31"); // Far future date
    }

    // Create subscription based on payment method
    if (paymentMethod === "stripe" && plan.stripePriceId) {
      // Create Stripe subscription
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: { userId: req.user.id },
      });

      const stripeSubscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: plan.stripePriceId }],
        trial_period_days: 14, // 14-day free trial
      });

      subscriptionData.stripeSubscriptionId = stripeSubscription.id;
      subscriptionData.status = "TRIALING";
      subscriptionData.trialStart = new Date();
      subscriptionData.trialEnd = new Date(
        Date.now() + 14 * 24 * 60 * 60 * 1000
      );

      // Update user with Stripe customer ID
      await prisma.user.update({
        where: { id: req.user.id },
        data: { stripeCustomerId: customer.id },
      });
    }

    // Create subscription in database
    const subscription = await prisma.subscription.create({
      data: subscriptionData,
      include: {
        plan: true,
      },
    });

    res.status(201).json({
      message: "Subscription created successfully",
      subscription,
    });
  } catch (error) {
    console.error("Create subscription error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create subscription",
    });
  }
});

/**
 * @route   POST /api/subscriptions/create-payment-intent
 * @desc    Create payment intent for subscription
 * @access  Private
 */
router.post("/create-payment-intent", authenticateToken, async (req, res) => {
  try {
    const { planId, paymentMethod = "stripe" } = req.body;

    const plan = await prisma.subscriptionPlan.findUnique({
      where: { id: planId },
    });

    if (!plan) {
      return res.status(404).json({
        error: "Not Found",
        message: "Plan not found",
      });
    }

    if (paymentMethod === "stripe") {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(plan.price * 100), // Convert to cents
        currency: plan.currency.toLowerCase(),
        metadata: {
          userId: req.user.id,
          planId: plan.id,
        },
      });

      res.json({
        clientSecret: paymentIntent.client_secret,
        amount: plan.price,
        currency: plan.currency,
      });
    } else if (paymentMethod === "razorpay") {
      const order = await razorpay.orders.create({
        amount: Math.round(plan.price * 100), // Convert to paise for INR
        currency: "INR",
        receipt: `receipt_${Date.now()}`,
        notes: {
          userId: req.user.id,
          planId: plan.id,
        },
      });

      res.json({
        orderId: order.id,
        amount: plan.price,
        currency: "INR",
        key: process.env.RAZORPAY_KEY_ID,
      });
    } else {
      res.status(400).json({
        error: "Bad Request",
        message: "Unsupported payment method",
      });
    }
  } catch (error) {
    console.error("Create payment intent error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create payment intent",
    });
  }
});

/**
 * @route   POST /api/subscriptions/cancel
 * @desc    Cancel subscription
 * @access  Private
 */
router.post("/cancel", authenticateToken, async (req, res) => {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: { userId: req.user.id },
      include: { plan: true },
    });

    if (!subscription) {
      return res.status(404).json({
        error: "Not Found",
        message: "No subscription found",
      });
    }

    // Cancel Stripe subscription if exists
    if (subscription.stripeSubscriptionId) {
      await stripe.subscriptions.update(subscription.stripeSubscriptionId, {
        cancel_at_period_end: true,
      });
    }

    // Update subscription in database
    const updatedSubscription = await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        cancelAtPeriodEnd: true,
        cancelledAt: new Date(),
      },
      include: { plan: true },
    });

    res.json({
      message: "Subscription cancelled successfully",
      subscription: updatedSubscription,
    });
  } catch (error) {
    console.error("Cancel subscription error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to cancel subscription",
    });
  }
});

/**
 * @route   POST /api/subscriptions/webhook/stripe
 * @desc    Handle Stripe webhooks
 * @access  Public
 */
router.post(
  "/webhook/stripe",
  express.raw({ type: "application/json" }),
  async (req, res) => {
    const sig = req.headers["stripe-signature"];
    let event;

    try {
      event = stripe.webhooks.constructEvent(
        req.body,
        sig,
        process.env.STRIPE_WEBHOOK_SECRET
      );
    } catch (err) {
      console.error("Webhook signature verification failed:", err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
      switch (event.type) {
        case "customer.subscription.updated":
        case "customer.subscription.deleted":
          const subscription = event.data.object;
          await handleStripeSubscriptionUpdate(subscription);
          break;

        case "invoice.payment_succeeded":
          const invoice = event.data.object;
          await handleStripePaymentSuccess(invoice);
          break;

        case "invoice.payment_failed":
          const failedInvoice = event.data.object;
          await handleStripePaymentFailed(failedInvoice);
          break;

        default:
          console.log(`Unhandled event type ${event.type}`);
      }

      res.json({ received: true });
    } catch (error) {
      console.error("Webhook handling error:", error);
      res.status(500).json({ error: "Webhook handling failed" });
    }
  }
);

// Helper functions for webhook handling
async function handleStripeSubscriptionUpdate(stripeSubscription) {
  const subscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: stripeSubscription.id },
  });

  if (subscription) {
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        status: stripeSubscription.status.toUpperCase(),
        currentPeriodStart: new Date(
          stripeSubscription.current_period_start * 1000
        ),
        currentPeriodEnd: new Date(
          stripeSubscription.current_period_end * 1000
        ),
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      },
    });
  }
}

async function handleStripePaymentSuccess(invoice) {
  if (invoice.subscription) {
    const subscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: invoice.subscription },
    });

    if (subscription) {
      await prisma.payment.create({
        data: {
          subscriptionId: subscription.id,
          amount: invoice.amount_paid / 100,
          currency: invoice.currency.toUpperCase(),
          status: "SUCCESS",
          paymentMethod: "stripe",
          stripePaymentId: invoice.payment_intent,
          paidAt: new Date(),
        },
      });
    }
  }
}

async function handleStripePaymentFailed(invoice) {
  if (invoice.subscription) {
    const subscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: invoice.subscription },
    });

    if (subscription) {
      await prisma.payment.create({
        data: {
          subscriptionId: subscription.id,
          amount: invoice.amount_due / 100,
          currency: invoice.currency.toUpperCase(),
          status: "FAILED",
          paymentMethod: "stripe",
          failureReason: "Payment failed",
        },
      });
    }
  }
}

module.exports = router;
