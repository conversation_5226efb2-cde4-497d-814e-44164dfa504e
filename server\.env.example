# Database Configuration
DATABASE_URL="postgresql://admin:admin@localhost:5432/financial_management_db"

# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=6c0ea373d6ffc7115e56bddd7b848bffb6978b558aaa0034727a17effff5bca5
JWT_EXPIRES_IN=7d

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Tax Configuration
DEFAULT_TAX_RATE=18
DEFAULT_CURRENCY=USD

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173
