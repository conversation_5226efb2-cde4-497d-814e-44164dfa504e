const express = require("express");
const { authenticateToken } = require("../middleware/auth-mock");

const router = express.Router();

// Mock clients storage (in-memory for testing)
const mockClients = [];

/**
 * @route   GET /api/clients
 * @desc    Get all clients for user
 * @access  Private
 */
router.get("/", authenticateToken, (req, res) => {
  const userClients = mockClients.filter(
    (client) => client.userId === req.user.id
  );
  res.json({
    message: "Clients retrieved successfully (mock mode)",
    clients: userClients,
    total: userClients.length,
  });
});

/**
 * @route   POST /api/clients
 * @desc    Create a new client
 * @access  Private
 */
router.post("/", authenticateToken, (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      company,
      address,
      city,
      state,
      zipCode,
      country,
      taxId,
      notes,
    } = req.body;

    const client = {
      id: `client_${Date.now()}`,
      userId: req.user.id,
      name,
      email: email || null,
      phone: phone || null,
      company: company || null,
      address: address || null,
      city: city || null,
      state: state || null,
      zipCode: zipCode || null,
      country: country || "US",
      taxId: taxId || null,
      notes: notes || null,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockClients.push(client);

    res.status(201).json({
      message: "Client created successfully (mock mode)",
      client,
    });
  } catch (error) {
    console.error("Create client error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create client",
    });
  }
});

/**
 * @route   GET /api/clients/:id
 * @desc    Get client by ID
 * @access  Private
 */
router.get("/:id", authenticateToken, (req, res) => {
  const client = mockClients.find(
    (c) => c.id === req.params.id && c.userId === req.user.id
  );

  if (!client) {
    return res.status(404).json({
      error: "Not Found",
      message: "Client not found",
    });
  }

  res.json({
    message: "Client retrieved successfully (mock mode)",
    client,
  });
});

/**
 * @route   PUT /api/clients/:id
 * @desc    Update client
 * @access  Private
 */
router.put("/:id", authenticateToken, (req, res) => {
  try {
    const clientIndex = mockClients.findIndex(
      (c) => c.id === req.params.id && c.userId === req.user.id
    );

    if (clientIndex === -1) {
      return res.status(404).json({
        error: "Not Found",
        message: "Client not found",
      });
    }

    const {
      name,
      email,
      phone,
      company,
      address,
      city,
      state,
      zipCode,
      country,
      taxId,
      notes,
    } = req.body;

    mockClients[clientIndex] = {
      ...mockClients[clientIndex],
      name: name || mockClients[clientIndex].name,
      email: email || mockClients[clientIndex].email,
      phone: phone || mockClients[clientIndex].phone,
      company: company || mockClients[clientIndex].company,
      address: address || mockClients[clientIndex].address,
      city: city || mockClients[clientIndex].city,
      state: state || mockClients[clientIndex].state,
      zipCode: zipCode || mockClients[clientIndex].zipCode,
      country: country || mockClients[clientIndex].country,
      taxId: taxId || mockClients[clientIndex].taxId,
      notes: notes || mockClients[clientIndex].notes,
      updatedAt: new Date().toISOString(),
    };

    res.json({
      message: "Client updated successfully (mock mode)",
      client: mockClients[clientIndex],
    });
  } catch (error) {
    console.error("Update client error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update client",
    });
  }
});

/**
 * @route   DELETE /api/clients/:id
 * @desc    Delete client
 * @access  Private
 */
router.delete("/:id", authenticateToken, (req, res) => {
  const clientIndex = mockClients.findIndex(
    (c) => c.id === req.params.id && c.userId === req.user.id
  );

  if (clientIndex === -1) {
    return res.status(404).json({
      error: "Not Found",
      message: "Client not found",
    });
  }

  mockClients.splice(clientIndex, 1);

  res.json({
    message: "Client deleted successfully (mock mode)",
  });
});

module.exports = router;
