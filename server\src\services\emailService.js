const nodemailer = require("nodemailer");
const fs = require("fs");
const path = require("path");

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    if (
      !process.env.SMTP_HOST ||
      !process.env.SMTP_USER ||
      !process.env.SMTP_PASS
    ) {
      console.warn(
        "Email service not configured. Set SMTP environment variables to enable email functionality."
      );
      return;
    }

    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Verify connection
    this.transporter.verify((error, success) => {
      if (error) {
        console.error("Email service configuration error:", error);
      } else {
        console.log("Email service ready");
      }
    });
  }

  async sendEmail(to, subject, html, attachments = []) {
    if (!this.transporter) {
      console.warn("Email service not configured - email not sent to:", to);
      return { messageId: "email-service-not-configured" };
    }

    try {
      const mailOptions = {
        from: `"${process.env.SMTP_FROM_NAME || "Financial Management"}" <${
          process.env.SMTP_USER
        }>`,
        to,
        subject,
        html,
        attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log("Email sent successfully:", result.messageId);
      return result;
    } catch (error) {
      console.error("Failed to send email:", error);
      throw error;
    }
  }

  async sendInvoiceEmail(invoice, client, user, pdfPath = null) {
    const subject = `Invoice ${invoice.invoiceNumber} from ${
      user.company || user.firstName + " " + user.lastName
    }`;

    const html = this.generateInvoiceEmailTemplate(invoice, client, user);

    const attachments = [];
    if (pdfPath && fs.existsSync(pdfPath)) {
      attachments.push({
        filename: `invoice-${invoice.invoiceNumber}.pdf`,
        path: pdfPath,
        contentType: "application/pdf",
      });
    }

    return await this.sendEmail(client.email, subject, html, attachments);
  }

  generateInvoiceEmailTemplate(invoice, client, user) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invoice ${invoice.invoiceNumber}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #3b82f6; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .invoice-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
            .button { display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            .amount { font-size: 24px; font-weight: bold; color: #3b82f6; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Invoice ${invoice.invoiceNumber}</h1>
                <p>From ${
                  user.company || user.firstName + " " + user.lastName
                }</p>
            </div>
            
            <div class="content">
                <p>Dear ${client.name},</p>
                
                <p>Please find attached your invoice for the services provided. Here are the details:</p>
                
                <div class="invoice-details">
                    <h3>Invoice Details</h3>
                    <p><strong>Invoice Number:</strong> ${
                      invoice.invoiceNumber
                    }</p>
                    <p><strong>Issue Date:</strong> ${new Date(
                      invoice.issueDate
                    ).toLocaleDateString()}</p>
                    <p><strong>Due Date:</strong> ${new Date(
                      invoice.dueDate
                    ).toLocaleDateString()}</p>
                    ${
                      invoice.title
                        ? `<p><strong>Title:</strong> ${invoice.title}</p>`
                        : ""
                    }
                    <p><strong>Total Amount:</strong> <span class="amount">$${parseFloat(
                      invoice.total
                    ).toFixed(2)}</span></p>
                </div>
                
                ${
                  invoice.description
                    ? `<p><strong>Description:</strong><br>${invoice.description}</p>`
                    : ""
                }
                
                <p>Please process this payment by the due date. If you have any questions about this invoice, please don't hesitate to contact us.</p>
                
                ${
                  invoice.notes
                    ? `<div class="invoice-details"><h4>Notes:</h4><p>${invoice.notes}</p></div>`
                    : ""
                }
            </div>
            
            <div class="footer">
                <p>Thank you for your business!</p>
                <p>${user.company || user.firstName + " " + user.lastName}</p>
                <p>${user.email} | ${user.phone || ""}</p>
                ${
                  user.address
                    ? `<p>${user.address}, ${user.city || ""} ${
                        user.state || ""
                      } ${user.zipCode || ""}</p>`
                    : ""
                }
            </div>
        </div>
    </body>
    </html>
    `;
  }

  async sendWelcomeEmail(user) {
    const subject = "Welcome to Financial Management Platform!";
    const html = this.generateWelcomeEmailTemplate(user);

    return await this.sendEmail(user.email, subject, html);
  }

  generateWelcomeEmailTemplate(user) {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to Financial Management</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #22c55e; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .feature { background-color: white; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
            .button { display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Welcome to Financial Management!</h1>
                <p>Your journey to better financial management starts here</p>
            </div>
            
            <div class="content">
                <p>Dear ${user.firstName},</p>
                
                <p>Welcome to our Financial Management platform! We're excited to help you streamline your business finances.</p>
                
                <div class="feature">
                    <h3>🧾 Invoice Management</h3>
                    <p>Create, send, and track professional invoices with ease.</p>
                </div>
                
                <div class="feature">
                    <h3>💰 Expense Tracking</h3>
                    <p>Keep track of all your business expenses and categorize them for better insights.</p>
                </div>
                
                <div class="feature">
                    <h3>📊 Financial Reports</h3>
                    <p>Generate comprehensive reports and tax calculations automatically.</p>
                </div>
                
                <div class="feature">
                    <h3>👥 Client Management</h3>
                    <p>Manage your client database and maintain professional relationships.</p>
                </div>
                
                <p>To get started, log in to your account and explore the dashboard. If you need any help, our support team is here to assist you.</p>
                
                <div style="text-align: center;">
                    <a href="${process.env.FRONTEND_URL}/dashboard" class="button">Go to Dashboard</a>
                </div>
            </div>
            
            <div class="footer">
                <p>Thank you for choosing Financial Management Platform!</p>
                <p>If you have any questions, please contact our support team.</p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  async sendPasswordResetEmail(user, resetToken) {
    const subject = "Password Reset Request";
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f59e0b; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
            .button { display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            .warning { background-color: #fef3c7; padding: 15px; border-radius: 5px; margin: 15px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Password Reset Request</h1>
            </div>
            
            <div class="content">
                <p>Dear ${user.firstName},</p>
                
                <p>We received a request to reset your password for your Financial Management account.</p>
                
                <div style="text-align: center;">
                    <a href="${resetUrl}" class="button">Reset Password</a>
                </div>
                
                <div class="warning">
                    <p><strong>Important:</strong> This link will expire in 1 hour for security reasons.</p>
                </div>
                
                <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>
                
                <p>For security reasons, please don't share this email with anyone.</p>
            </div>
            
            <div class="footer">
                <p>Financial Management Platform</p>
                <p>This is an automated email, please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
    `;

    return await this.sendEmail(user.email, subject, html);
  }

  async sendInvoiceReminderEmail(invoice, client, user) {
    const subject = `Payment Reminder: Invoice ${invoice.invoiceNumber}`;
    const daysOverdue = Math.floor(
      (new Date() - new Date(invoice.dueDate)) / (1000 * 60 * 60 * 24)
    );

    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment Reminder</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #f59e0b; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .invoice-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
            .amount { font-size: 24px; font-weight: bold; color: #f59e0b; }
            .overdue { color: #dc2626; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Payment Reminder</h1>
                <p>Invoice ${invoice.invoiceNumber}</p>
            </div>
            
            <div class="content">
                <p>Dear ${client.name},</p>
                
                <p>This is a friendly reminder that payment for the following invoice is ${
                  daysOverdue > 0
                    ? `<span class="overdue">overdue by ${daysOverdue} days</span>`
                    : "due soon"
                }.</p>
                
                <div class="invoice-details">
                    <h3>Invoice Details</h3>
                    <p><strong>Invoice Number:</strong> ${
                      invoice.invoiceNumber
                    }</p>
                    <p><strong>Issue Date:</strong> ${new Date(
                      invoice.issueDate
                    ).toLocaleDateString()}</p>
                    <p><strong>Due Date:</strong> ${new Date(
                      invoice.dueDate
                    ).toLocaleDateString()}</p>
                    <p><strong>Amount Due:</strong> <span class="amount">$${parseFloat(
                      invoice.total
                    ).toFixed(2)}</span></p>
                </div>
                
                <p>Please process this payment at your earliest convenience. If you have already made this payment, please disregard this reminder.</p>
                
                <p>If you have any questions or concerns about this invoice, please don't hesitate to contact us.</p>
            </div>
            
            <div class="footer">
                <p>Thank you for your business!</p>
                <p>${user.company || user.firstName + " " + user.lastName}</p>
                <p>${user.email} | ${user.phone || ""}</p>
            </div>
        </div>
    </body>
    </html>
    `;

    return await this.sendEmail(client.email, subject, html);
  }
}

module.exports = new EmailService();
