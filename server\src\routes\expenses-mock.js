const express = require("express");
const { authenticateToken } = require("../middleware/auth-mock");

const router = express.Router();

// Mock expenses storage (in-memory for testing)
const mockExpenses = [];

/**
 * @route   GET /api/expenses
 * @desc    Get all expenses for user
 * @access  Private
 */
router.get("/", authenticateToken, (req, res) => {
  const userExpenses = mockExpenses.filter(
    (expense) => expense.userId === req.user.id
  );
  res.json({
    message: "Expenses retrieved successfully (mock mode)",
    expenses: userExpenses,
    total: userExpenses.length,
  });
});

/**
 * @route   POST /api/expenses
 * @desc    Create a new expense
 * @access  Private
 */
router.post("/", authenticateToken, (req, res) => {
  try {
    const {
      category,
      description,
      amount,
      currency,
      date,
      receipt,
      notes,
      isRecurring,
    } = req.body;

    const expense = {
      id: `expense_${Date.now()}`,
      userId: req.user.id,
      category: category || "OTHER",
      description,
      amount: parseFloat(amount).toFixed(2),
      currency: currency || "USD",
      date: date || new Date().toISOString(),
      receipt: receipt || null,
      notes: notes || null,
      isRecurring: isRecurring || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockExpenses.push(expense);

    res.status(201).json({
      message: "Expense created successfully (mock mode)",
      expense,
    });
  } catch (error) {
    console.error("Create expense error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to create expense",
    });
  }
});

/**
 * @route   GET /api/expenses/:id
 * @desc    Get expense by ID
 * @access  Private
 */
router.get("/:id", authenticateToken, (req, res) => {
  const expense = mockExpenses.find(
    (e) => e.id === req.params.id && e.userId === req.user.id
  );

  if (!expense) {
    return res.status(404).json({
      error: "Not Found",
      message: "Expense not found",
    });
  }

  res.json({
    message: "Expense retrieved successfully (mock mode)",
    expense,
  });
});

/**
 * @route   PUT /api/expenses/:id
 * @desc    Update expense
 * @access  Private
 */
router.put("/:id", authenticateToken, (req, res) => {
  try {
    const expenseIndex = mockExpenses.findIndex(
      (e) => e.id === req.params.id && e.userId === req.user.id
    );

    if (expenseIndex === -1) {
      return res.status(404).json({
        error: "Not Found",
        message: "Expense not found",
      });
    }

    const {
      category,
      description,
      amount,
      currency,
      date,
      receipt,
      notes,
      isRecurring,
    } = req.body;

    mockExpenses[expenseIndex] = {
      ...mockExpenses[expenseIndex],
      category: category || mockExpenses[expenseIndex].category,
      description: description || mockExpenses[expenseIndex].description,
      amount: amount
        ? parseFloat(amount).toFixed(2)
        : mockExpenses[expenseIndex].amount,
      currency: currency || mockExpenses[expenseIndex].currency,
      date: date || mockExpenses[expenseIndex].date,
      receipt:
        receipt !== undefined ? receipt : mockExpenses[expenseIndex].receipt,
      notes: notes !== undefined ? notes : mockExpenses[expenseIndex].notes,
      isRecurring:
        isRecurring !== undefined
          ? isRecurring
          : mockExpenses[expenseIndex].isRecurring,
      updatedAt: new Date().toISOString(),
    };

    res.json({
      message: "Expense updated successfully (mock mode)",
      expense: mockExpenses[expenseIndex],
    });
  } catch (error) {
    console.error("Update expense error:", error);
    res.status(500).json({
      error: "Internal Server Error",
      message: "Failed to update expense",
    });
  }
});

/**
 * @route   DELETE /api/expenses/:id
 * @desc    Delete expense
 * @access  Private
 */
router.delete("/:id", authenticateToken, (req, res) => {
  const expenseIndex = mockExpenses.findIndex(
    (e) => e.id === req.params.id && e.userId === req.user.id
  );

  if (expenseIndex === -1) {
    return res.status(404).json({
      error: "Not Found",
      message: "Expense not found",
    });
  }

  mockExpenses.splice(expenseIndex, 1);

  res.json({
    message: "Expense deleted successfully (mock mode)",
  });
});

/**
 * @route   GET /api/expenses/categories
 * @desc    Get expense categories
 * @access  Private
 */
router.get("/categories", authenticateToken, (req, res) => {
  const categories = [
    "OFFICE_SUPPLIES",
    "TRAVEL",
    "MEALS",
    "UTILITIES",
    "RENT",
    "INSURANCE",
    "MARKETING",
    "PROFESSIONAL_SERVICES",
    "SOFTWARE",
    "EQUIPMENT",
    "OTHER",
  ];

  res.json({
    message: "Expense categories retrieved successfully",
    categories,
  });
});

module.exports = router;
