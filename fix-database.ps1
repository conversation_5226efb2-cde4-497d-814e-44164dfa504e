# PostgreSQL Database Setup Script for Windows
# Run this script in PowerShell as Administrator

Write-Host "🗄️ PostgreSQL Database Setup for Financial Management SaaS" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Set PostgreSQL path
$pgPath = "C:\Program Files\PostgreSQL\17\bin"
$psqlPath = "$pgPath\psql.exe"
$createdbPath = "$pgPath\createdb.exe"

# Check if PostgreSQL is installed
if (-not (Test-Path $psqlPath)) {
    Write-Host "❌ PostgreSQL not found at $psqlPath" -ForegroundColor Red
    Write-Host "Please install PostgreSQL from https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ PostgreSQL found at $psqlPath" -ForegroundColor Green

# Function to test database connection
function Test-DatabaseConnection {
    param($connectionString)
    
    Write-Host "🔍 Testing connection: $connectionString" -ForegroundColor Yellow
    
    # Extract components from connection string
    if ($connectionString -match "postgresql://([^:]+):?([^@]*)@([^:]+):(\d+)/(.+)") {
        $user = $matches[1]
        $password = $matches[2]
        $host = $matches[3]
        $port = $matches[4]
        $database = $matches[5]
    } elseif ($connectionString -match "postgresql://([^@]+)@([^:]+):(\d+)/(.+)") {
        $user = $matches[1]
        $password = ""
        $host = $matches[2]
        $port = $matches[3]
        $database = $matches[4]
    } else {
        Write-Host "❌ Invalid connection string format" -ForegroundColor Red
        return $false
    }
    
    # Test connection
    $env:PGPASSWORD = $password
    $testResult = & $psqlPath -U $user -h $host -p $port -d postgres -c "SELECT 1;" 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Connection successful!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ Connection failed" -ForegroundColor Red
        return $false
    }
}

# Function to create database
function Create-Database {
    param($user, $password, $host, $port, $database)
    
    Write-Host "🏗️ Creating database: $database" -ForegroundColor Yellow
    
    $env:PGPASSWORD = $password
    $createResult = & $psqlPath -U $user -h $host -p $port -d postgres -c "CREATE DATABASE $database;" 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database created successfully!" -ForegroundColor Green
        return $true
    } else {
        # Database might already exist
        $checkResult = & $psqlPath -U $user -h $host -p $port -d $database -c "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Database already exists!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to create database" -ForegroundColor Red
            return $false
        }
    }
}

# Common connection strings to try
$connectionStrings = @(
    "postgresql://postgres@localhost:5432/financial_management_db",
    "postgresql://postgres:postgres@localhost:5432/financial_management_db",
    "postgresql://postgres:admin@localhost:5432/financial_management_db",
    "postgresql://postgres:password@localhost:5432/financial_management_db"
)

Write-Host "🔍 Trying common PostgreSQL configurations..." -ForegroundColor Yellow

$workingConnection = $null

foreach ($connStr in $connectionStrings) {
    if (Test-DatabaseConnection $connStr) {
        $workingConnection = $connStr
        break
    }
}

if ($workingConnection) {
    Write-Host "🎉 Found working connection!" -ForegroundColor Green
    Write-Host "Connection string: $workingConnection" -ForegroundColor Cyan
    
    # Extract database info
    if ($workingConnection -match "postgresql://([^:]+):?([^@]*)@([^:]+):(\d+)/(.+)") {
        $user = $matches[1]
        $password = $matches[2]
        $host = $matches[3]
        $port = $matches[4]
        $database = $matches[5]
    } elseif ($workingConnection -match "postgresql://([^@]+)@([^:]+):(\d+)/(.+)") {
        $user = $matches[1]
        $password = ""
        $host = $matches[2]
        $port = $matches[3]
        $database = $matches[4]
    }
    
    # Create database if needed
    Create-Database $user $password $host $port $database
    
    # Update .env file
    $envPath = "server\.env"
    if (Test-Path $envPath) {
        Write-Host "📝 Updating $envPath file..." -ForegroundColor Yellow
        
        $envContent = Get-Content $envPath
        $newContent = @()
        
        foreach ($line in $envContent) {
            if ($line -match "^DATABASE_URL=") {
                $newContent += "DATABASE_URL=`"$workingConnection`""
            } elseif ($line -match "^#.*DATABASE_URL=") {
                # Comment out other DATABASE_URL lines
                $newContent += $line
            } else {
                $newContent += $line
            }
        }
        
        $newContent | Set-Content $envPath
        Write-Host "✅ Updated $envPath with working connection string" -ForegroundColor Green
    }
    
    Write-Host "`n🚀 Next steps:" -ForegroundColor Green
    Write-Host "1. Run: npm run db:migrate" -ForegroundColor Cyan
    Write-Host "2. Run: npm run db:generate" -ForegroundColor Cyan
    Write-Host "3. Run: npm run dev" -ForegroundColor Cyan
    
} else {
    Write-Host "`n❌ Could not connect to PostgreSQL with common configurations" -ForegroundColor Red
    Write-Host "`n🔧 Manual setup required:" -ForegroundColor Yellow
    Write-Host "1. Open pgAdmin or connect to PostgreSQL manually" -ForegroundColor Cyan
    Write-Host "2. Find your postgres user password" -ForegroundColor Cyan
    Write-Host "3. Update the DATABASE_URL in server\.env" -ForegroundColor Cyan
    Write-Host "4. Create database: CREATE DATABASE financial_management_db;" -ForegroundColor Cyan
    
    Write-Host "`n💡 Common solutions:" -ForegroundColor Yellow
    Write-Host "- Check if you remember the password set during PostgreSQL installation" -ForegroundColor Cyan
    Write-Host "- Try connecting with pgAdmin first" -ForegroundColor Cyan
    Write-Host "- Reset postgres user password if needed" -ForegroundColor Cyan
}

Write-Host "`n📚 For detailed help, see DATABASE_SETUP_GUIDE.md" -ForegroundColor Blue
